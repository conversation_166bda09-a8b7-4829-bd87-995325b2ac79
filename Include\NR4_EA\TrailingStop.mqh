//+------------------------------------------------------------------+
//|                                                 TrailingStop.mqh |
//|                                      NR4_EA 移动止损模块          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"

#include "Common.mqh"
#include "Parameters.mqh"

//+------------------------------------------------------------------+
//| 移动止损管理类                                                    |
//+------------------------------------------------------------------+
class CTrailingStopManager {
private:
    string m_symbol;
    long m_magicNumber;

public:
    //+------------------------------------------------------------------+
    //| 构造函数                                                          |
    //+------------------------------------------------------------------+
    CTrailingStopManager(string symbol, long magicNumber) {
        m_symbol = symbol;
        m_magicNumber = magicNumber;
    }

    //+------------------------------------------------------------------+
    //| 初始化移动止损数组                                                |
    //+------------------------------------------------------------------+
    void Initialize() {
        ArrayResize(g_trailingData, MAX_TRAILING_DATA);
        g_trailingDataCount = 0;
    }

    //+------------------------------------------------------------------+
    //| 处理移动止损                                                      |
    //+------------------------------------------------------------------+
    void ProcessTrailingStop() {
        if(!g_inputEnableTrailingStop) return;
        
        // 更新移动止损数据
        UpdateTrailingStopData();
        
        // 检查并调整止损
        for(int i = 0; i < g_trailingDataCount; i++) {
            if(g_trailingData[i].ticket == 0) continue;
            
            if(g_positionInfo.SelectByTicket(g_trailingData[i].ticket)) {
                if(g_positionInfo.Symbol() == m_symbol && g_positionInfo.Magic() == m_magicNumber) {
                    CheckAndUpdateTrailingStop(i);
                }
            } else {
                // 持仓已关闭，清除数据
                RemoveTrailingData(i);
                i--; // 调整索引
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 更新移动止损数据                                                  |
    //+------------------------------------------------------------------+
    void UpdateTrailingStopData() {
        // 遍历所有持仓，确保都在移动止损数据中
        for(int i = PositionsTotal() - 1; i >= 0; i--) {
            if(g_positionInfo.SelectByIndex(i)) {
                if(g_positionInfo.Symbol() == m_symbol && g_positionInfo.Magic() == m_magicNumber) {
                    ulong ticket = g_positionInfo.Ticket();
                    
                    // 检查是否已存在
                    int dataIndex = FindTrailingDataIndex(ticket);
                    if(dataIndex == -1) {
                        // 添加新的移动止损数据
                        AddTrailingData(ticket);
                    } else {
                        // 更新现有数据
                        UpdatePriceData(dataIndex);
                    }
                }
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 查找移动止损数据索引                                               |
    //+------------------------------------------------------------------+
    int FindTrailingDataIndex(ulong ticket) {
        for(int i = 0; i < g_trailingDataCount; i++) {
            if(g_trailingData[i].ticket == ticket) {
                return i;
            }
        }
        return -1;
    }

    //+------------------------------------------------------------------+
    //| 添加移动止损数据                                                  |
    //+------------------------------------------------------------------+
    void AddTrailingData(ulong ticket) {
        if(g_trailingDataCount >= ArraySize(g_trailingData)) {
            ArrayResize(g_trailingData, ArraySize(g_trailingData) + 50);
        }
        
        if(g_positionInfo.SelectByTicket(ticket)) {
            g_trailingData[g_trailingDataCount].ticket = ticket;
            g_trailingData[g_trailingDataCount].highestPrice = g_positionInfo.PriceOpen();
            g_trailingData[g_trailingDataCount].lowestPrice = g_positionInfo.PriceOpen();
            g_trailingData[g_trailingDataCount].lastStopLoss = g_positionInfo.StopLoss();
            
            UpdatePriceData(g_trailingDataCount);
            g_trailingDataCount++;
        }
    }

    //+------------------------------------------------------------------+
    //| 更新价格数据                                                      |
    //+------------------------------------------------------------------+
    void UpdatePriceData(int index) {
        if(index < 0 || index >= g_trailingDataCount) return;
        
        if(g_positionInfo.SelectByTicket(g_trailingData[index].ticket)) {
            double currentPrice = (g_positionInfo.PositionType() == POSITION_TYPE_BUY) ?
                                 SymbolInfoDouble(m_symbol, SYMBOL_BID) :
                                 SymbolInfoDouble(m_symbol, SYMBOL_ASK);
            
            if(g_positionInfo.PositionType() == POSITION_TYPE_BUY) {
                // 多头持仓，更新最高价
                if(currentPrice > g_trailingData[index].highestPrice) {
                    g_trailingData[index].highestPrice = currentPrice;
                }
            } else {
                // 空头持仓，更新最低价
                if(currentPrice < g_trailingData[index].lowestPrice) {
                    g_trailingData[index].lowestPrice = currentPrice;
                }
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 检查并更新移动止损                                                |
    //+------------------------------------------------------------------+
    void CheckAndUpdateTrailingStop(int index) {
        if(index < 0 || index >= g_trailingDataCount) return;
        
        if(!g_positionInfo.SelectByTicket(g_trailingData[index].ticket)) return;
        
        double currentPrice = (g_positionInfo.PositionType() == POSITION_TYPE_BUY) ?
                             SymbolInfoDouble(m_symbol, SYMBOL_BID) :
                             SymbolInfoDouble(m_symbol, SYMBOL_ASK);
        
        double openPrice = g_positionInfo.PriceOpen();
        double currentStopLoss = g_positionInfo.StopLoss();
        double newStopLoss = 0;
        bool shouldUpdate = false;
        
        if(g_positionInfo.PositionType() == POSITION_TYPE_BUY) {
            // 多头持仓的移动止损逻辑
            double profit = currentPrice - openPrice;
            
            // 检查是否达到触发条件
            if(profit >= g_inputTrailingStart * _Point) {
                // 计算新的止损位置
                newStopLoss = g_trailingData[index].highestPrice - g_inputTrailingStep * _Point;
                
                // 确保新止损不低于当前止损（只能向上移动）
                if(newStopLoss > currentStopLoss + _Point) {
                    shouldUpdate = true;
                }
            }
        } else {
            // 空头持仓的移动止损逻辑
            double profit = openPrice - currentPrice;
            
            // 检查是否达到触发条件
            if(profit >= g_inputTrailingStart * _Point) {
                // 计算新的止损位置
                newStopLoss = g_trailingData[index].lowestPrice + g_inputTrailingStep * _Point;
                
                // 确保新止损不高于当前止损（只能向下移动）
                if(currentStopLoss == 0 || newStopLoss < currentStopLoss - _Point) {
                    shouldUpdate = true;
                }
            }
        }
        
        // 执行止损更新
        if(shouldUpdate) {
            // 规范化价格
            newStopLoss = NormalizeDouble(newStopLoss, _Digits);
            
            // Sleep(600); // 等待100毫秒
            if(g_trade.PositionModify(g_trailingData[index].ticket, newStopLoss, g_positionInfo.TakeProfit())) {
                g_trailingData[index].lastStopLoss = newStopLoss;
                Print("移动止损更新成功: Ticket=", g_trailingData[index].ticket,
                      ", 新止损=", newStopLoss,
                      ", 类型=", (g_positionInfo.PositionType() == POSITION_TYPE_BUY ? "多头" : "空头"));
            } else {
                Print("移动止损更新失败: Ticket=", g_trailingData[index].ticket,
                      ", 错误代码=", g_trade.ResultRetcode(),
                      ", 错误描述=", g_trade.ResultRetcodeDescription());
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 移除移动止损数据                                                  |
    //+------------------------------------------------------------------+
    void RemoveTrailingData(int index) {
        if(index < 0 || index >= g_trailingDataCount) return;
        
        // 将后面的元素前移
        for(int i = index; i < g_trailingDataCount - 1; i++) {
            g_trailingData[i] = g_trailingData[i + 1];
        }
        
        // 清空最后一个元素
        g_trailingData[g_trailingDataCount - 1].ticket = 0;
        g_trailingData[g_trailingDataCount - 1].highestPrice = 0;
        g_trailingData[g_trailingDataCount - 1].lowestPrice = 0;
        g_trailingData[g_trailingDataCount - 1].lastStopLoss = 0;
        
        g_trailingDataCount--;
    }
};
