//+------------------------------------------------------------------+
//|                                           ResourceMonitor.mqh    |
//+------------------------------------------------------------------+
#ifndef __RESOURCE_MONITOR_MQH__
#define __RESOURCE_MONITOR_MQH__

#include "Common.mqh"

// 资源监控变量
extern datetime lastResourceLogTime;  // 上次资源日志时间
extern bool ResourceMonitor_Enabled;  // 资源监控启用开关

// 记录资源使用情况
void LogResourceUsage()
{
   // 如果资源监控未启用，直接返回
   if(!ResourceMonitor_Enabled) {
      return;
   }

   // 获取当前时间
   datetime currentTime = TimeCurrent();

   // 检查是否需要记录（每小时一次）
   if(lastResourceLogTime == 0 || (currentTime - lastResourceLogTime) >= 3600) // 3600秒 = 1小时
   {
      // 获取内存使用情况
      uint totalMemory = TerminalInfoInteger(TERMINAL_MEMORY_TOTAL);
      uint availableMemory = TerminalInfoInteger(TERMINAL_MEMORY_AVAILABLE);
      uint usedMemory = totalMemory - availableMemory;
      double memoryUsagePercent = (double)usedMemory / totalMemory * 100.0;

      // 获取EA运行信息
      string runningInfo = GetEARunningInfo();

      // 获取对象数量
      int totalObjects = ObjectsTotal(0);
      int arrowCount = 0;

      // 计算箭头数量
      for(int i=ObjectsTotal(0, 0, OBJ_ARROW_CHECK)-1; i>=0; i--)
      {
         string name = ObjectName(0, i, 0, OBJ_ARROW_CHECK);
         if(StringFind(name, "BB_Width_Arrow_") == 0)
         {
            arrowCount++;
         }
      }

      // 记录资源使用情况
      Print("===== BB_Width_EA 资源使用情况 [", TimeToString(currentTime, TIME_DATE|TIME_MINUTES|TIME_SECONDS), "] =====");
      Print("内存: 总计 ", totalMemory/1024/1024, " MB, 已用 ", usedMemory/1024/1024, " MB (", DoubleToString(memoryUsagePercent, 2), "%)");
      Print("对象: 总计 ", totalObjects, ", 箭头 ", arrowCount);
      Print("指标句柄: BB=", (bbHandle != INVALID_HANDLE ? "有效" : "无效"), ", ATR=", (atrHandle != INVALID_HANDLE ? "有效" : "无效"));

      // 获取其他有用信息
      int dllsLoaded = TerminalInfoInteger(TERMINAL_DLLS_ALLOWED);
      int maxBars = TerminalInfoInteger(TERMINAL_MAXBARS);
      int codePage = TerminalInfoInteger(TERMINAL_CODEPAGE);
      Print("终端信息: DLL允许=", (dllsLoaded ? "是" : "否"), ", 最大K线数=", maxBars, ", 代码页=", codePage);
      Print("EA状态: ", runningInfo);
      Print("==================================================");

      // 更新上次记录时间
      lastResourceLogTime = currentTime;
   }
}

// 获取EA运行信息
string GetEARunningInfo()
{
   // 在MQL5中无法直接获取进程的CPU使用率
   // 但可以获取一些其他有用的运行信息
   string info = "";

   // 获取EA运行时间
   static datetime startTime = TimeCurrent();
   datetime currentTime = TimeCurrent();
   int runningSeconds = (int)(currentTime - startTime);

   int days = runningSeconds / (24 * 3600);
   int hours = (runningSeconds % (24 * 3600)) / 3600;
   int minutes = (runningSeconds % 3600) / 60;
   int seconds = runningSeconds % 60;

   info = StringFormat("运行时间: %d天 %02d:%02d:%02d", days, hours, minutes, seconds);
   return info;
}

// 记录最终资源使用情况
void LogFinalResourceUsage()
{
   // 如果资源监控未启用，直接返回
   if(!ResourceMonitor_Enabled) {
      return;
   }

   Print("===== BB_Width_EA 最终资源使用情况 [", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES|TIME_SECONDS), "] =====");
   uint totalMemory = TerminalInfoInteger(TERMINAL_MEMORY_TOTAL);
   uint availableMemory = TerminalInfoInteger(TERMINAL_MEMORY_AVAILABLE);
   uint usedMemory = totalMemory - availableMemory;
   Print("内存: 总计 ", totalMemory/1024/1024, " MB, 已用 ", usedMemory/1024/1024, " MB (", DoubleToString((double)usedMemory/totalMemory*100.0, 2), "%)");

   // 计算箭头数量
   int totalObjects = ObjectsTotal(0);
   int arrowCount = 0;
   for(int i=ObjectsTotal(0, 0, OBJ_ARROW_CHECK)-1; i>=0; i--)
   {
      string name = ObjectName(0, i, 0, OBJ_ARROW_CHECK);
      if(StringFind(name, "BB_Width_Arrow_") == 0)
      {
         arrowCount++;
      }
   }
   Print("对象: 总计 ", totalObjects, ", 箭头 ", arrowCount);
   Print("EA状态: ", GetEARunningInfo());
   Print("==================================================");
}

#endif
