#==============================================================================
# MT5 Installation Script
#==============================================================================
# Description: This script copies MQL5 files from the project directory to all
#              MetaTrader 5 installations found on the system.
#
# Supported directories:
# - Indicators: Copies .mq5, .ex5, and .md files, preserving subdirectories
# - Experts: Copies .mq5 and .ex5 files
# - Include: Copies .mqh files and all subdirectories
# - Files: Copies all files to both terminal and common directories
#
# Author: Updated by AI Assistant
# Version: 2.0
#==============================================================================

# Show welcome message
Write-Host "====================================================================" -ForegroundColor Cyan
Write-Host "                  MT5 Installation Script v2.0                      " -ForegroundColor Cyan
Write-Host "====================================================================" -ForegroundColor Cyan
Write-Host "This script will copy your MQL5 files to all MT5 installations."
Write-Host "Supported file types: .mq5, .ex5, .mqh, .md and other related files."
Write-Host "--------------------------------------------------------------------"

# Function to find all MT5 installation paths
function Find-MT5Paths {
    $terminalPath = "C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal"
    Write-Host "Searching for MT5 installations in: $terminalPath"

    # Get all terminal directories
    $terminalDirs = Get-ChildItem -Path $terminalPath -Directory -ErrorAction SilentlyContinue |
                    Where-Object { $_.Name -match "^[A-F0-9]{32}$" }

    # Find all MT5 installations
    $mt5Paths = @()
    foreach ($dir in $terminalDirs) {
        $mql5Path = Join-Path $dir.FullName "MQL5"
        if (Test-Path $mql5Path) {
            $mt5Paths += $mql5Path
        }
    }

    return $mt5Paths
}

# Initialize counters for summary report
$global:totalFilesCopied = 0
$global:totalDirectoriesCreated = 0
$global:installationCount = 0

# Get all MT5 installation paths
$mt5Paths = Find-MT5Paths
if ($mt5Paths.Count -eq 0) {
    Write-Host "Error: Cannot find any MetaTrader 5 installations" -ForegroundColor Red
    Write-Host "Please make sure MetaTrader 5 is installed on this computer." -ForegroundColor Red
    exit 1
}

Write-Host "Found $($mt5Paths.Count) MT5 installation(s)" -ForegroundColor Green

# Get source directories
$sourceBase = Get-Location
$sourceDirs = @(
    "Indicators",
    "Experts",
    "Include",
    "Files"
)

Write-Host "Source directory: $sourceBase" -ForegroundColor Cyan
Write-Host "--------------------------------------------------------------------"

# Process each MT5 installation
foreach ($mt5Path in $mt5Paths) {
    $global:installationCount++
    Write-Host "Processing MT5 installation: $mt5Path" -ForegroundColor Cyan

    # Track files copied for this installation
    $installationFilesCopied = 0
    $installationDirsCreated = 0

    # Process each source directory
    foreach ($sourceDir in $sourceDirs) {
        $fullSourceDir = Join-Path $sourceBase $sourceDir
        if (Test-Path $fullSourceDir) {
            Write-Host "  Processing directory: $sourceDir" -ForegroundColor Cyan

            # Get target directory in MT5
            $targetDir = Join-Path $mt5Path $sourceDir

            # Create target directory if it doesn't exist (for Files and other directories if needed)
            if (-not (Test-Path $targetDir)) {
                New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
                Write-Host "  Created directory: $targetDir" -ForegroundColor Green
                $global:totalDirectoriesCreated++
                $installationDirsCreated++
            }

            # Copy all files from source to target
            if (Test-Path $targetDir) {
                # Process different directory types
                if ($sourceDir -eq "Include") {
                    # Get subdirectories in Include folder
                    $subDirs = Get-ChildItem -Path $fullSourceDir -Directory

                    # Copy each subdirectory to target
                    foreach ($subDir in $subDirs) {
                        $targetSubDir = Join-Path $targetDir $subDir.Name

                        # Check if target subdirectory exists
                        if (-not (Test-Path $targetSubDir)) {
                            # Create the directory if it doesn't exist
                            New-Item -Path $targetSubDir -ItemType Directory -Force | Out-Null
                            $global:totalDirectoriesCreated++
                            $installationDirsCreated++
                        }

                        # Count files in the subdirectory
                        $subDirFiles = Get-ChildItem -Path (Join-Path $subDir.FullName "*") -File -Recurse
                        $fileCount = $subDirFiles.Count

                        # Copy entire folder with all contents
                        $sourcePath = Join-Path $subDir.FullName "*"
                        Copy-Item -Path $sourcePath -Destination $targetSubDir -Recurse -Force
                        Write-Host "  Copied entire folder $($subDir.Name) to MT5's Include directory ($fileCount files)" -ForegroundColor Green

                        $global:totalFilesCopied += $fileCount
                        $installationFilesCopied += $fileCount
                    }

                    # Also copy any .mqh files in the root of Include
                    $mqhFiles = Get-ChildItem -Path (Join-Path $fullSourceDir "*.mqh") -ErrorAction SilentlyContinue
                    if ($mqhFiles.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.mqh") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        Write-Host "  Copied $($mqhFiles.Count) .mqh files to MT5's Include directory" -ForegroundColor Green
                        $global:totalFilesCopied += $mqhFiles.Count
                        $installationFilesCopied += $mqhFiles.Count
                    }
                }
                elseif ($sourceDir -eq "Files") {
                    # For Files directory, copy all files (not just specific extensions)
                    $files = Get-ChildItem -Path $fullSourceDir -File

                    # Check if there are any files to copy
                    if ($files.Count -gt 0) {
                        # Copy all files
                        Copy-Item -Path (Join-Path $fullSourceDir "*.*") -Destination $targetDir -Force
                        Write-Host "  Copied $($files.Count) files from $sourceDir to MT5's $sourceDir directory" -ForegroundColor Green
                        $global:totalFilesCopied += $files.Count
                        $installationFilesCopied += $files.Count

                        # Also copy to MT5's Common Files directory for shared access
                        $commonDir = Join-Path (Split-Path $mt5Path -Parent) "Common"
                        $commonFilesDir = Join-Path $commonDir "Files"

                        # Create Common\Files directory if it doesn't exist
                        if (-not (Test-Path $commonDir)) {
                            New-Item -Path $commonDir -ItemType Directory -Force | Out-Null
                            Write-Host "  Created directory: $commonDir" -ForegroundColor Green
                            $global:totalDirectoriesCreated++
                            $installationDirsCreated++
                        }

                        if (-not (Test-Path $commonFilesDir)) {
                            New-Item -Path $commonFilesDir -ItemType Directory -Force | Out-Null
                            Write-Host "  Created directory: $commonFilesDir" -ForegroundColor Green
                            $global:totalDirectoriesCreated++
                            $installationDirsCreated++
                        }

                        # Copy files to Common\Files directory
                        Copy-Item -Path (Join-Path $fullSourceDir "*.*") -Destination $commonFilesDir -Force
                        Write-Host "  Copied $($files.Count) files from $sourceDir to MT5's Common\Files directory" -ForegroundColor Green
                        $global:totalFilesCopied += $files.Count
                        $installationFilesCopied += $files.Count
                    } else {
                        Write-Host "  No files found in $sourceDir directory" -ForegroundColor Yellow
                    }
                }
                elseif ($sourceDir -eq "Indicators") {
                    # For Indicators directory, copy .mq5, .ex5 and documentation files
                    # First, handle files in the root of the Indicators directory
                    $mq5Files = Get-ChildItem -Path (Join-Path $fullSourceDir "*.mq5") -ErrorAction SilentlyContinue
                    $ex5Files = Get-ChildItem -Path (Join-Path $fullSourceDir "*.ex5") -ErrorAction SilentlyContinue
                    $mdFiles = Get-ChildItem -Path (Join-Path $fullSourceDir "*.md") -ErrorAction SilentlyContinue

                    if ($mq5Files.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.mq5") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        $global:totalFilesCopied += $mq5Files.Count
                        $installationFilesCopied += $mq5Files.Count
                    }

                    if ($ex5Files.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.ex5") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        $global:totalFilesCopied += $ex5Files.Count
                        $installationFilesCopied += $ex5Files.Count
                    }

                    if ($mdFiles.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.md") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        $global:totalFilesCopied += $mdFiles.Count
                        $installationFilesCopied += $mdFiles.Count
                    }

                    $totalRootFiles = $mq5Files.Count + $ex5Files.Count + $mdFiles.Count

                    # Get subdirectories in Indicators folder
                    $subDirs = Get-ChildItem -Path $fullSourceDir -Directory
                    $subDirFilesCount = 0

                    # Copy each subdirectory to target
                    foreach ($subDir in $subDirs) {
                        $targetSubDir = Join-Path $targetDir $subDir.Name

                        # Check if target subdirectory exists
                        if (-not (Test-Path $targetSubDir)) {
                            # Create the directory if it doesn't exist
                            New-Item -Path $targetSubDir -ItemType Directory -Force | Out-Null
                            $global:totalDirectoriesCreated++
                            $installationDirsCreated++
                        }

                        # Count files in the subdirectory
                        $subDirFiles = Get-ChildItem -Path (Join-Path $subDir.FullName "*") -File -Recurse
                        $fileCount = $subDirFiles.Count
                        $subDirFilesCount += $fileCount

                        # Copy entire folder with all contents
                        $sourcePath = Join-Path $subDir.FullName "*"
                        Copy-Item -Path $sourcePath -Destination $targetSubDir -Recurse -Force
                        Write-Host "  Copied indicator subfolder $($subDir.Name) to MT5's Indicators directory ($fileCount files)" -ForegroundColor Green

                        $global:totalFilesCopied += $fileCount
                        $installationFilesCopied += $fileCount
                    }

                    $totalFiles = $totalRootFiles + $subDirFilesCount

                    if ($totalFiles -gt 0) {
                        Write-Host "  Copied $totalFiles indicator files and folders to MT5's Indicators directory" -ForegroundColor Green
                    } else {
                        Write-Host "  No indicator files found to copy" -ForegroundColor Yellow
                    }
                }
                else {
                    # For other directories (like Experts), copy .mq5 and .ex5 files
                    $mq5Files = Get-ChildItem -Path (Join-Path $fullSourceDir "*.mq5") -ErrorAction SilentlyContinue
                    $ex5Files = Get-ChildItem -Path (Join-Path $fullSourceDir "*.ex5") -ErrorAction SilentlyContinue

                    if ($mq5Files.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.mq5") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        $global:totalFilesCopied += $mq5Files.Count
                        $installationFilesCopied += $mq5Files.Count
                    }

                    if ($ex5Files.Count -gt 0) {
                        Copy-Item -Path (Join-Path $fullSourceDir "*.ex5") -Destination $targetDir -Force -ErrorAction SilentlyContinue
                        $global:totalFilesCopied += $ex5Files.Count
                        $installationFilesCopied += $ex5Files.Count
                    }

                    $totalFiles = $mq5Files.Count + $ex5Files.Count
                    if ($totalFiles -gt 0) {
                        Write-Host "  Copied $totalFiles files from $sourceDir to MT5's $sourceDir directory" -ForegroundColor Green
                    } else {
                        Write-Host "  No files found in $sourceDir directory to copy" -ForegroundColor Yellow
                    }
                }
            } else {
                Write-Host "  Warning: Target directory $targetDir not found" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  Directory $sourceDir not found in source location" -ForegroundColor Yellow
        }
    }

    # Display summary for this installation
    Write-Host "  Installation Summary: Copied $installationFilesCopied files, Created $installationDirsCreated directories" -ForegroundColor Yellow
    Write-Host "--------------------------------------------------------------------"
}

# Display final summary with progress bar animation
Write-Host "Finalizing installation..." -ForegroundColor Cyan
$progressChars = @('|', '/', '-', '\')
for ($i = 0; $i -lt 20; $i++) {
    $progress = $progressChars[$i % 4]
    Write-Host "`r  $progress Processing..." -NoNewline -ForegroundColor Yellow
    Start-Sleep -Milliseconds 100
}
Write-Host "`r  ✓ Processing complete!   " -ForegroundColor Green

Write-Host "`n====================================================================" -ForegroundColor Cyan
Write-Host "                      Installation Summary                          " -ForegroundColor Cyan
Write-Host "====================================================================" -ForegroundColor Cyan
Write-Host "Total MT5 installations processed: $global:installationCount" -ForegroundColor Green
Write-Host "Total files copied: $global:totalFilesCopied" -ForegroundColor Green
Write-Host "Total directories created: $global:totalDirectoriesCreated" -ForegroundColor Green
Write-Host "--------------------------------------------------------------------"
Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host "====================================================================" -ForegroundColor Cyan

# Pause at the end to let the user see the results
Write-Host "`nPress any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")