//+------------------------------------------------------------------+
//|                                                 EmailManager.mqh |
//|                                         Copyright 2024, Trae AI |
//|                                 邮件管理模块，用于处理EA中的邮件功能 |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 邮件管理类                                                         |
//+------------------------------------------------------------------+
class CEmailManager
{
private:
   // 邮件设置
   string            m_emailSubject;       // 邮件主题前缀
   bool              m_isEnabled;          // 是否启用邮件功能
   bool              m_isInitialized;      // 是否已初始化
   
   // 私有方法
   string            FormatEmailBody(string content);  // 格式化邮件内容
   bool              CaptureScreenshot(string filename); // 捕获屏幕截图
   
public:
   // 构造函数和析构函数
                     CEmailManager();
                    ~CEmailManager();
   
   // 初始化和设置方法
   bool              Initialize(string emailSubject, bool isEnabled);
   void              SetEnabled(bool isEnabled) { m_isEnabled = isEnabled; }
   bool              IsEnabled() const { return m_isEnabled && m_isInitialized; }
   
   // 邮件功能检查
   bool              CheckEmailSettings();
   
   // 邮件发送方法
   bool              SendTestEmail(string symbol, string timeframeStr, string additionalInfo="");
   bool              SendCrossAlert(string crossType, datetime crossTime, double crossPrice, 
                                   string symbol, string timeframeStr, string additionalInfo="");
   bool              SendCustomEmail(string subject, string body);
   bool              SendScreenshotEmail(string subject, string body, string additionalInfo=""); // 发送带截图的邮件
};

//+------------------------------------------------------------------+
//| 构造函数                                                           |
//+------------------------------------------------------------------+
CEmailManager::CEmailManager()
{
   m_emailSubject = "EA提醒";
   m_isEnabled = false;
   m_isInitialized = false;
}

//+------------------------------------------------------------------+
//| 析构函数                                                           |
//+------------------------------------------------------------------+
CEmailManager::~CEmailManager()
{
   // 析构函数中不需要特殊清理
}

//+------------------------------------------------------------------+
//| 初始化邮件管理器                                                    |
//+------------------------------------------------------------------+
bool CEmailManager::Initialize(string emailSubject, bool isEnabled)
{
   m_emailSubject = emailSubject;
   m_isEnabled = isEnabled;
   
   // 检查邮件设置
   bool emailSettingsOK = CheckEmailSettings();
   m_isInitialized = emailSettingsOK;
   
   return emailSettingsOK;
}

//+------------------------------------------------------------------+
//| 检查邮件设置是否已配置                                               |
//+------------------------------------------------------------------+
bool CEmailManager::CheckEmailSettings()
{
   //--- 检查MT5终端是否开启了邮件功能
   if(TerminalInfoInteger(TERMINAL_EMAIL_ENABLED) == 0)
   {
      Print("错误: MT5终端邮件功能未开启。请前往工具 > 选项 > 邮件设置进行配置");
      return false;
   }
   
   //--- 邮件功能已开启
   return true;
}

//+------------------------------------------------------------------+
//| 格式化邮件内容                                                      |
//+------------------------------------------------------------------+
string CEmailManager::FormatEmailBody(string content)
{
   // 将纯文本转换为HTML格式
   string htmlContent = "<!DOCTYPE html>\n";
   htmlContent += "<html>\n";
   htmlContent += "<head>\n";
   htmlContent += "<meta charset=\"UTF-8\">\n";
   htmlContent += "<style>\n";
   htmlContent += "body { font-family: Arial, sans-serif; line-height: 1.6; }\n";
   htmlContent += "h2 { color: #003366; border-bottom: 1px solid #cccccc; padding-bottom: 5px; }\n";
   htmlContent += ".section { margin: 15px 0; padding: 10px; background-color: #f9f9f9; border-radius: 5px; }\n";
   htmlContent += ".footer { margin-top: 20px; font-size: 12px; color: #666666; border-top: 1px solid #cccccc; padding-top: 10px; }\n";
   htmlContent += "</style>\n";
   htmlContent += "</head>\n";
   htmlContent += "<body>\n";
   
   // 处理文本内容
   string processedContent = content;
   
   // 先打印原始内容以便调试
   Print("Email content before processing: ", content);
   
   // 创建一个简单的HTML邮件，不进行复杂的字符串处理
   string htmlBody = "";
   
   // 将原始内容按行分割
   string lines[];
   StringSplit(content, '\n', lines);
   
   // 处理每一行
   string currentSection = "";
   bool inSection = false;
   
   for(int i = 0; i < ArraySize(lines); i++)
   {
       string line = lines[i];
       
       // 处理标题行
       if(StringFind(line, "===== ") == 0 && StringFind(line, " =====") > 0)
       {
           // 如果已经在一个段落中，先结束它
           if(inSection)
               htmlBody += "</div>\n";
               
           // 提取标题文本
           string title = StringSubstr(line, 6, StringLen(line) - 12);
           htmlBody += "<h2>" + title + "</h2>\n";
           htmlBody += "<div class=\"section\">\n";
           inSection = true;
       }
       // 处理空行
       else if(line == "")
       {
           htmlBody += "<br>\n";
       }
       // 处理普通文本行
       else
       {
           htmlBody += line + "<br>\n";
       }
   }
   
   // 如果还在段落中，结束它
   if(inSection)
       htmlBody += "</div>\n";
   
   // 添加到HTML内容
   htmlContent += htmlBody;
   
   // 添加页脚
   htmlContent += "<div class=\"footer\">";
   htmlContent += "此邮件由MetaTrader 5 EA自动发送<br>";
   htmlContent += "发送时间: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + "<br>";
   htmlContent += "</div>\n";
   
   // 关闭HTML标签
   htmlContent += "</body>\n</html>";
   
   return htmlContent;
}

//+------------------------------------------------------------------+
//| 发送测试邮件                                                        |
//+------------------------------------------------------------------+
bool CEmailManager::SendTestEmail(string symbol, string timeframeStr, string additionalInfo="")
{
   // 如果邮件功能未启用，直接返回
   if(!IsEnabled())
      return false;
      
   Print("正在发送测试邮件...");
   
   //--- 准备测试邮件主题
   string testSubject = StringFormat("[测试邮件] %s - %s", m_emailSubject, symbol);
   
   //--- 构建测试邮件正文
   string testBody = "";
   
   // 测试邮件标题
   testBody += "\n===== EA测试邮件 =====\n\n";
   testBody += "这是一封测试邮件，确认您的EA邮件功能正常工作。\n\n";
   
   // EA信息
   testBody += "===== EA信息 =====\n\n";
   testBody += StringFormat("交易品种: %s\n", symbol);
   testBody += StringFormat("时间周期: %s\n\n", timeframeStr);
   
   // 添加额外信息（如果有）
   if(additionalInfo != "")
   {
      testBody += "===== 额外信息 =====\n\n";
      testBody += additionalInfo + "\n\n";
   }
   
   // 其他信息
   testBody += "===== 其他信息 =====\n\n";
   testBody += "请注意：此邮件仅为测试邮件，用于确认邮件功能正常工作。\n";
   
   // 格式化邮件内容
   testBody = FormatEmailBody(testBody);
   
   //--- 发送测试邮件并检查结果
   bool success = SendMail(testSubject, testBody);
   
   //--- 记录发送结果
   if(success)
      Print("✅ 测试邮件已成功发送: ", testSubject);
   else
      Print("❌ 测试邮件发送失败，错误码: ", GetLastError());
      
   return success;
}

//+------------------------------------------------------------------+
//| 发送交叉提醒邮件                                                    |
//+------------------------------------------------------------------+
bool CEmailManager::SendCrossAlert(string crossType, datetime crossTime, double crossPrice, 
                                  string symbol, string timeframeStr, string additionalInfo="")
{
   // 如果邮件功能未启用，直接返回
   if(!IsEnabled())
      return false;
   
   //--- 准备邮件主题
   string subject = StringFormat("%s - %s (%s)", m_emailSubject, symbol, timeframeStr);
   
   //--- 构建邮件正文
   string body = "";
   
   // 添加交叉信息标题
   body += "\n===== EMA交叉提醒 =====\n\n";
   
   // 添加交叉信息
   body += StringFormat("交叉类型: %s\n", crossType);
   body += StringFormat("交易品种: %s\n", symbol);
   body += StringFormat("时间周期: %s\n", timeframeStr);
   body += StringFormat("交叉时间: %s\n", TimeToString(crossTime, TIME_DATE | TIME_MINUTES | TIME_SECONDS));
   body += StringFormat("交叉价格: %.5f\n\n", crossPrice);
   
   // 添加额外信息（如果有）
   if(additionalInfo != "")
   {
      body += "===== 指标参数 =====\n\n";
      body += additionalInfo + "\n\n";
   }
   
   // 格式化邮件内容
   body = FormatEmailBody(body);
   
   //--- 发送邮件并检查结果
   bool success = SendMail(StringFormat("%s",subject), StringFormat("%s",body));
   
   //--- 记录发送结果
   if(success)
      Print("✅ 邮件提醒已成功发送: ", subject);
   else
      Print("❌ 邮件发送失败，错误码: ", GetLastError());
      
   return success;
}

//+------------------------------------------------------------------+
//| 发送自定义邮件                                                      |
//+------------------------------------------------------------------+
bool CEmailManager::SendCustomEmail(string subject, string body)
{
   // 如果邮件功能未启用，直接返回
   if(!IsEnabled())
      return false;
      
   // 格式化邮件内容
   body = FormatEmailBody(body);
   
   //--- 发送邮件并检查结果
   bool success = SendMail(StringFormat("%s",subject), StringFormat("%s",body));
   
   //--- 记录发送结果
   if(success)
      Print("✅ 自定义邮件已成功发送: ", subject);
   else
      Print("❌ 自定义邮件发送失败，错误码: ", GetLastError());
      
   return success;
}

//+------------------------------------------------------------------+
//| 捕获屏幕截图                                                       |
//+------------------------------------------------------------------+
bool CEmailManager::CaptureScreenshot(string filename)
{
   // 确保文件名有效
   if(filename == "")
      filename = "screenshot_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES|TIME_SECONDS) + ".gif";
   
   // 替换文件名中的非法字符
   StringReplace(filename, ":", "-");
   StringReplace(filename, " ", "_");
   
   // 捕获当前图表的屏幕截图
   bool success = ChartScreenShot(0, filename, 1024, 768, ALIGN_RIGHT);
   
   if(!success)
      Print("❌ 截图失败，错误码: ", GetLastError());
   else
      Print("✅ 截图已保存: ", filename);
      
   return success;
}

//+------------------------------------------------------------------+
//| 发送带截图的邮件                                                    |
//+------------------------------------------------------------------+
bool CEmailManager::SendScreenshotEmail(string subject, string body, string additionalInfo="")
{
   // 如果邮件功能未启用，直接返回
   if(!IsEnabled())
      return false;
   
   // 生成截图文件名
   string filename = "screenshot_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES|TIME_SECONDS) + ".gif";
   StringReplace(filename, ":", "-");
   StringReplace(filename, " ", "_");
   
   // 捕获屏幕截图
   if(!CaptureScreenshot(filename))
      return false;
   
   // 添加额外信息（如果有）
   if(additionalInfo != "")
   {
      body += "\n===== 额外信息 =====\n\n";
      body += additionalInfo + "\n\n";
   }
   
   // 添加截图信息
   body += "\n===== 图表截图 =====\n\n";
   body += "已附加当前图表截图。\n";
   
   // 格式化邮件内容
   body = FormatEmailBody(body);
   
   // 注意：MQL5的SendMail函数不支持直接添加附件
   // 在邮件内容中添加截图文件路径信息
   body = StringConcatenate(body, "\n\n截图文件保存在终端目录: ", filename, "\n");
   // 如果需要发送带附件的邮件，需要使用外部DLL或其他方法
   
   //--- 发送邮件并检查结果（不含附件，仅包含截图路径信息）
   bool success = SendMail(StringFormat("%s",subject), StringFormat("%s",body));
   
   //--- 记录发送结果
   if(success)
      Print("✅ 带截图路径的邮件已成功发送: ", subject);
   else
      Print("❌ 邮件发送失败，错误码: ", GetLastError());
      
   return success;
}
