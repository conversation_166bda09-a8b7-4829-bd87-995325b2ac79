//+------------------------------------------------------------------+
//|                                                UIComponents.mqh |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#ifndef __UI_COMPONENTS_MQH__
#define __UI_COMPONENTS_MQH__

#include "Common.mqh"

//+------------------------------------------------------------------+
//| 面板标签创建辅助                                                 |
//+------------------------------------------------------------------+
void CreateBBLabels(string prefix, string title, int baseY, BBData &data)
{
   CreateLabel(prefix+"BarLabel", title, 30, baseY, Panel_Text_Color);

   int labelY = baseY + 25;
   CreateLabel(prefix+"UpperLabel", "上轨: ", 30, labelY, Panel_Text_Color);
   CreateLabel(prefix+"UpperValue", "N/A", 90, labelY, Panel_Text_Color);

   CreateLabel(prefix+"MiddleLabel", "中轨: ", 30, labelY+25, Panel_Text_Color);
   CreateLabel(prefix+"MiddleValue", "N/A", 90, labelY+25, Panel_Text_Color);

   CreateLabel(prefix+"LowerLabel", "下轨: ", 30, labelY+50, Panel_Text_Color);
   CreateLabel(prefix+"LowerValue", "N/A", 90, labelY+50, Panel_Text_Color);

   CreateLabel(prefix+"WidthLabel", "宽度: ", 30, labelY+80, Panel_Text_Color);
   CreateLabel(prefix+"WidthValue", "N/A", 90, labelY+80, Panel_Text_Color);

   // OHLC标签
   int xPos = 220;
   CreateLabel(prefix+"OpenValue", "O:"+DoubleToString(data.open,_Digits), xPos, labelY+0, Panel_Text_Color);
   CreateLabel(prefix+"HighValue", "H:"+DoubleToString(data.high,_Digits), xPos, labelY+20, Panel_Text_Color);
   CreateLabel(prefix+"LowValue", "L:"+DoubleToString(data.low,_Digits), xPos, labelY+40, Panel_Text_Color);
   CreateLabel(prefix+"CloseValue", "C:"+DoubleToString(data.close,_Digits), xPos, labelY+60, Panel_Text_Color);

   // 新增ATR和Volume标签
   xPos += 150;
   CreateLabel(prefix+"ATRLabel", "ATR: ", xPos, labelY+0, Panel_Text_Color);
   CreateLabel(prefix+"ATRValue", DoubleToString(data.atr,2), xPos+50, labelY+0, Panel_Text_Color);

   CreateLabel(prefix+"VolLabel", "VOL: ", xPos, labelY+20, Panel_Text_Color);
   CreateLabel(prefix+"VolValue", IntegerToString(data.volume), xPos+50, labelY+20, Panel_Text_Color);

   // 当前K线收线倒计时标签（前缀为空的是当前K线）
   if(prefix == "") {
      // CreateLabel(prefix+"CountdownLabel", "倒计时:", 220, 380, Panel_Text_Color);
      CreateLabel(prefix+"CountdownValue", "", 350, 30, clrYellow);
   }
}

#endif
