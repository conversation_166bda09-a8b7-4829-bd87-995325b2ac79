#property copyright "Copyright 2024, Trae AI"
#property version   "2.00"
#property strict

// 增强版输入参数
input double GrowthThreshold = 20.0;      // 基础增长率阈值(%)
input double Multiplier = 1.2;           // 历史速率乘数
input int HistoryDepth = 60;             // 历史数据存储深度(分钟)
input int ATR_Period = 14;               // ATR计算周期
input int PanelX = 20;                   // 面板X坐标
input int PanelY = 20;                   // 面板Y坐标
input color SignalColor = clrOrangeRed;  // 增强信号颜色

// 增强全局变量
struct HistoryRecord {
   datetime time;
   double growthRate;
   double atrValue;
};

HistoryRecord historyData[]; // 改为动态数组
int dataPointer = 0;
double currentGrowth = 0;
double historicalBaseline = 0;
int atrHandle;
long chartID;

// 初始化函数，负责指标初始化、历史数据预填充和面板创建
int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();
   
   // 初始化数组
   ArrayResize(historyData, HistoryDepth);
   ArraySetAsSeries(historyData, true);
   
   // 初始化ATR指标（带重试机制）
   int retryCount = 0;
   int maxRetries = 5;
   int retryInterval = 1000;
   
   while(retryCount < maxRetries) {
      atrHandle = iATR(_Symbol, PERIOD_M1, ATR_Period);
      if(atrHandle != INVALID_HANDLE) break;
      
      Print(StringFormat("ATR指标初始化失败，重试次数: %d/%d", retryCount+1, maxRetries));
      Sleep(retryInterval);
      retryCount++;
      retryInterval = (int)(retryInterval * 1.5);
   }
   
   if(atrHandle == INVALID_HANDLE){
      Alert("错误：无法加载ATR指标！请检查网络连接和市场数据可用性。");
      return(INIT_FAILED);
   }
   
   // 预填充历史数据
   InitializeHistoryData();
   
   return(INIT_SUCCEEDED);
}

// 新增函数声明
// 计算当前增长率，基于ATR值变化
void CalculateGrowthRate();

// 初始化历史数据，预填充60分钟ATR和增长率数据
void InitializeHistoryData()
{
   if(atrHandle == INVALID_HANDLE) return;
   
   datetime current = TimeCurrent();
   for(int i=0; i<ArraySize(historyData); i++){
      historyData[i].time = current - (60-i)*60;
      historyData[i].growthRate = 0;
      
      int barIndex = iBarShift(_Symbol, PERIOD_M1, historyData[i].time);
      if(barIndex != -1){
         int retryCount = 0;
         int maxRetries = 3;
         bool dataLoaded = false;
         
         while(retryCount < maxRetries && !dataLoaded){
            double tempArray[1];
            if(CopyBuffer(atrHandle, 0, barIndex, 1, tempArray) == 1){
               historyData[i].atrValue = tempArray[0];
               dataLoaded = true;
            }
            else {
               Print(StringFormat("历史ATR数据加载失败(尝试 %d/%d)，时间: %s 错误代码: %d",
                    retryCount+1, maxRetries, TimeToString(historyData[i].time), GetLastError()));
               Sleep(1000);
               retryCount++;
            }
         }
         if(!dataLoaded){
            Print(StringFormat("无法加载历史ATR数据，时间: %s", TimeToString(historyData[i].time)));
         }
      }
      else {
         Print(StringFormat("无法获取历史K线索引，时间: %s", TimeToString(historyData[i].time)));
      }
   }
   int filledCount = 0;
   for(int i=0; i<ArraySize(historyData); i++){
      if(historyData[i].time > 0) filledCount++;
   }
   // Print(StringFormat("历史数据初始化完成，深度: %d 成功填充: %d",
   //         ArraySize(historyData), filledCount));
}

// 主循环函数，每分钟执行一次，更新数据并检查波动率扩张
void OnTick()
{
   UpdateATRData();
   CalculateGrowthRate();
   UpdateHistoryData();
   CheckVolatilityExpansion();
   UpdateInfoPanel();
}

// 更新当前ATR数据，计算增长率
void UpdateATRData()
{
   if(atrHandle == INVALID_HANDLE) return;
   
   double atrValues[2];
   if(CopyBuffer(atrHandle, 0, 0, 2, atrValues) == 2){
      currentGrowth = (atrValues[0] - atrValues[1])/atrValues[1]*100;
      // Print(StringFormat("ATR更新 时间:%s 当前:%.5f 前值:%.5f 增长率:%.2f%%",
      //      TimeToString(TimeCurrent()), atrValues[0], atrValues[1], currentGrowth));
   }
   else {
      Print("ATR数据获取失败，错误代码:", GetLastError());
   }
}

// 计算当前增长率，基于ATR值变化
void CalculateGrowthRate()
{
   double atrValues[2];
   if(CopyBuffer(atrHandle, 0, 0, 2, atrValues) == 2){
      currentGrowth = (atrValues[0] - atrValues[1])/atrValues[1]*100;
      // Print(StringFormat("增长率计算完成：%.2f%%", currentGrowth));
   }
}

// 更新历史数据数组，循环存储最新数据
void UpdateHistoryData()
{
   historyData[dataPointer].time = TimeCurrent();
   historyData[dataPointer].growthRate = currentGrowth;
   double tempArray[1];
   if(CopyBuffer(atrHandle, 0, 0, 1, tempArray) == 1){
      historyData[dataPointer].atrValue = tempArray[0];
   }
   
   dataPointer = (dataPointer+1) % ArraySize(historyData);
   // Print(StringFormat("历史数据更新 指针位置:%d", dataPointer));
}

// 检查波动率扩张条件，满足条件时触发信号
// 检查波动率扩张条件
bool IsVolatilityExpansionSignal()
{
   double threshold = historicalBaseline * Multiplier;
   return currentGrowth > GrowthThreshold && 
          currentGrowth > threshold && 
          currentGrowth > historyData[(dataPointer+1)%ArraySize(historyData)].growthRate &&
          currentGrowth > historyData[(dataPointer+2)%ArraySize(historyData)].growthRate;
}

void CheckVolatilityExpansion()
{
   double sum = 0;
   int validCount = 0;
   
   // 计算前60分钟平均增长率
   for(int i=0; i<ArraySize(historyData); i++){
      if(historyData[i].time > TimeCurrent()-3600){
         sum += historyData[i].growthRate;
         validCount++;
      }
   }
   
   if(validCount > 0){
      historicalBaseline = sum / validCount;
      
      // 增强信号条件
      if(IsVolatilityExpansionSignal()){
         TriggerSignal();
      }
   }
}

// 触发信号，绘制箭头并发送警报
void TriggerSignal()
{
   DrawSignalArrow(233, SignalColor);
   Alert(StringFormat("波动率扩张警报！当前:%.2f%% 阈值:%.2f%%",
        currentGrowth, historicalBaseline*Multiplier));
}

// 复用面板模块并增强显示
// 创建信息显示面板
void CreateInfoPanel()
{
   // 背景面板
   ObjectCreate(chartID, "ProPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "ProPanelBG", OBJPROP_BGCOLOR, clrNavy);
   ObjectSetInteger(chartID, "ProPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "ProPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "ProPanelBG", OBJPROP_XSIZE, 350);
   ObjectSetInteger(chartID, "ProPanelBG", OBJPROP_YSIZE, 120);

   // 增强信息显示
   CreatePanelLabel("CurrentLabel", "当前增长率：", 10);
   CreatePanelLabel("HistoryLabel", "60分钟平均：", 35);
   CreatePanelLabel("ThresholdLabel", "动态阈值：", 60);
   CreatePanelLabel("SignalLabel", "信号状态：", 85);
}

// 更新面板显示增强版
// 更新面板显示内容
void UpdateInfoPanel()
{
   ObjectSetString(chartID, "CurrentLabel", OBJPROP_TEXT, "当前增长率："+DoubleToString(currentGrowth,1)+"%");
   ObjectSetString(chartID, "HistoryLabel", OBJPROP_TEXT, "60分钟平均："+DoubleToString(historicalBaseline,1)+"%");
   ObjectSetString(chartID, "ThresholdLabel", OBJPROP_TEXT, "动态阈值："+DoubleToString(historicalBaseline*Multiplier,1)+"%");
   
   string statusText = (currentGrowth > historicalBaseline*Multiplier) ? 
                      "ACTIVE ("+DoubleToString(currentGrowth/historicalBaseline*100,1)+"%)" : "Inactive";
   ObjectSetString(chartID, "SignalLabel", OBJPROP_TEXT, "信号状态："+statusText);
}

// 复用图形对象创建方法
// 创建面板标签
void CreatePanelLabel(string name, string text, int yOffset)
{
   ObjectCreate(chartID, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, name, OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, name, OBJPROP_YDISTANCE, PanelY+yOffset);
   ObjectSetInteger(chartID, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, name, OBJPROP_FONTSIZE, 9);
   ObjectSetString(chartID, name, OBJPROP_TEXT, text);
}

// 绘制信号箭头
void DrawSignalArrow(int code, color clr)
{
   string objName = "ProSignal_"+IntegerToString(GetTickCount());
   ObjectCreate(chartID, objName, OBJ_ARROW, 0, TimeCurrent(), iClose(_Symbol,PERIOD_M1,0));
   ObjectSetInteger(chartID, objName, OBJPROP_ARROWCODE, code);
   ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clr);
   ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 3);
}

// 清理函数，删除所有图形对象
void OnDeinit(const int reason)
{
   ObjectDelete(chartID, "ProPanelBG");
   ObjectDelete(chartID, "CurrentLabel");
   ObjectDelete(chartID, "HistoryLabel");
   ObjectDelete(chartID, "ThresholdLabel");
   ObjectDelete(chartID, "SignalLabel");
}