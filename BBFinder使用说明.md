# BBFinder_M5 布林通道检测器使用说明

## 功能概述

BBFinder_M5是一个专门用于检测符合特定条件的布林通道的MT5 EA（Expert Advisor）。当发现符合条件的布林通道时，EA会在终端日志中输出提醒信息，并将检测结果记录到CSV文件中。

## 主要特点

- 检测长度大于等于N的布林通道
- 筛选宽度小于等于BW的布林通道
- 支持TOL参数，允许一定数量的破例K线，防止漏检测
- 按日期自动分割CSV文件，防止单一文件过大
- 详细记录通道的开始时间、结束时间、长度和最大宽度

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| N | 6 | 布林通道最小长度（K线数量） |
| TOL | 2 | 允许破例K线数量（防止漏检测） |
| BW | 0.01 | 布林通道最大宽度（下轨减上轨的差值） |
| BB_Period | 20 | 布林带周期 |
| BB_Deviation | 2.0 | 布林带偏差 |
| PriceType | PRICE_CLOSE | 价格类型 |

## 安装方法

1. 将`BBFinder_M5.mq5`文件复制到MT5的`Experts`文件夹中
2. 重启MT5或刷新导航器窗口
3. 在导航器窗口中找到`BBFinder_M5`，将其拖放到图表上

## 输出文件

EA会在MT5的`Files`文件夹中创建CSV文件，文件命名规则为：

```
BBFind_品种名称_图表周期_信号归属服务器日期.csv
```

例如：`BBFind_XAUUSD_M5_2024.05.20.csv`

CSV文件包含以下列：
- 通道开始时间（服务器时间）
- 通道结束时间（服务器时间）
- 布林通道长度（最大）
- 布林通道宽度（最大）

## 使用建议

- 建议在M5时间周期上使用，可以根据需要调整到其他时间周期
- 对于不同品种，可能需要调整BW参数以适应其价格特性
- 可以结合其他指标或EA使用，作为交易信号的过滤条件

## 测试版本

项目中还包含`BBFinder_M5_Test.mq5`文件，这是一个带有测试功能的版本，可以生成测试数据验证EA的功能。测试版本增加了以下参数：

| 参数 | 默认值 | 说明 |
|------|--------|------|
| EnableTestMode | true | 启用测试模式 |

在测试模式下，EA会自动生成5条测试记录，方便验证CSV文件的生成和格式是否正确。