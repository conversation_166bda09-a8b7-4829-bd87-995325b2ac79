#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 市场状态检测EA - 整合震荡与趋势检测功能                          |
//+------------------------------------------------------------------+

// 数据结构定义

// ADX指标数据结构
struct SADX {
   double main[];    // ADX主线
   double plus[];    // +DI线
   double minus[];   // -DI线
   int handle;       // 指标句柄
};

// 布林带指标数据结构
struct SBollingerBands {
   double upper[];    // 上轨
   double middle[];   // 中轨
   double lower[];    // 下轨
   double width[];    // 带宽
   double widthMA[];  // 带宽移动平均
   int handle;        // 指标句柄
   int widthMAHandle; // 带宽移动平均句柄
};

// 移动平均线数据结构
struct SMovingAverage {
   double values[];    // 均线值
   int handle;        // 指标句柄
   int period;        // 周期
   ENUM_MA_METHOD method; // 平均方法
};

// 震荡检测参数
input group "===== 震荡检测参数 ====="
input int ADX_Period = 14;               // ADX周期
input double ADX_Threshold = 18.0;       // ADX趋势阈值
input int BB_Period = 20;                // 布林带周期
input double BB_Deviation = 2.0;         // 标准差倍数
input int RSI_Period = 14;               // RSI周期
input int ATR_Period = 14;               // ATR周期
input double PriceFluctuation = 0.0020;  // 价格波动幅度(当前品种报价比例)

// 趋势检测参数
input group "===== 趋势检测参数 ====="
input int FastMAPeriod = 50;             // 快速均线周期
input int SlowMAPeriod = 200;            // 慢速均线周期

// 通用参数
input group "===== 通用参数 ====="
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15; // 图表周期
input int AlertInterval = 3600;          // 警报间隔时间（秒）

// 面板参数
input group "===== 面板设置 ====="
input int PanelX = 20;                   // 面板X坐标
input int PanelY = 20;                   // 面板Y坐标
input color RangeColor = clrGoldenrod;   // 震荡颜色
input color BullColor = clrLime;         // 多头趋势颜色
input color BearColor = clrRed;          // 空头趋势颜色

// 全局变量 - 震荡检测相关
SADX adx;              // ADX指标数据
SBollingerBands bb;    // 布林带指标数据
int rsiHandle;         // RSI指标句柄
double rsiValues[];    // RSI值
int atrHandle;         // ATR指标句柄
double atrValues[];    // ATR数据
bool isConsolidation;  // 是否处于震荡状态
int consolidationCount = 0, totalCount = 0; // 统计计数器

// 全局变量 - 趋势检测相关
SMovingAverage fastMA;  // 快速移动平均线
SMovingAverage slowMA;  // 慢速移动平均线
bool isBullTrend;       // 是否多头趋势

// 通用全局变量
string trendDirection = "N/A";     // 趋势方向：多头趋势/空头趋势
string rangeCondition = "N/A";     // 震荡状态：震荡/非震荡
long chartID;
datetime lastAlertTime;
datetime lastBarTime;   // 记录上一次检测的K线时间

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();

   Print("开始初始化指标...");
   
   // 初始化震荡检测指标
   adx.handle = iADX(_Symbol, TimeFrame, ADX_Period);
   bb.handle = iBands(_Symbol, TimeFrame, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
   bb.widthMAHandle = iMA(_Symbol, TimeFrame, BB_Period, 0, MODE_SMA, PRICE_CLOSE);
   rsiHandle = iRSI(_Symbol, TimeFrame, RSI_Period, PRICE_CLOSE);
   atrHandle = iATR(_Symbol, TimeFrame, ATR_Period);
   
   // 初始化趋势检测指标
   fastMA.period = FastMAPeriod;
   fastMA.method = MODE_EMA;
   fastMA.handle = iMA(_Symbol, TimeFrame, fastMA.period, 0, fastMA.method, PRICE_CLOSE);
   
   slowMA.period = SlowMAPeriod;
   slowMA.method = MODE_EMA;
   slowMA.handle = iMA(_Symbol, TimeFrame, slowMA.period, 0, slowMA.method, PRICE_CLOSE);
   
   // 指标句柄验证
   if(adx.handle == INVALID_HANDLE || bb.handle == INVALID_HANDLE || 
      rsiHandle == INVALID_HANDLE || bb.widthMAHandle == INVALID_HANDLE ||
      fastMA.handle == INVALID_HANDLE || slowMA.handle == INVALID_HANDLE)
   {
      Alert("指标初始化失败，请检查参数设置");
      return(INIT_FAILED);
   }
   
   Print("指标句柄创建成功，等待指标缓冲区准备就绪...");
   
   // 等待指标缓冲区准备就绪 - 增加等待时间至15秒
   int waitTime = 0;
   int maxWaitTime = 15000; // 最多等待15秒
   
   while(!BarsCalculated(adx.handle) || !BarsCalculated(bb.handle) || 
         !BarsCalculated(rsiHandle) || !BarsCalculated(bb.widthMAHandle) || 
         !BarsCalculated(atrHandle) || !BarsCalculated(fastMA.handle) ||
         !BarsCalculated(slowMA.handle))
   {
      Sleep(200);
      waitTime += 200;
      
      // 每秒输出一次等待状态
      if(waitTime % 1000 == 0) {
         Print("已等待", waitTime/1000, "秒，指标缓冲区准备中...");
         // 输出每个指标的状态
         Print("ADX计算状态: ", BarsCalculated(adx.handle), 
               ", BB计算状态: ", BarsCalculated(bb.handle),
               ", RSI计算状态: ", BarsCalculated(rsiHandle));
      }
      
      if(waitTime > maxWaitTime)
      {
         Print("指标缓冲区准备超时，但将继续尝试加载数据");
         break; // 即使超时也继续尝试
      }
   }

   Print("设置数组为时间序列...");
   // 设置数组为时间序列
   ArraySetAsSeries(adx.main, true);
   ArraySetAsSeries(adx.plus, true);
   ArraySetAsSeries(adx.minus, true);
   ArraySetAsSeries(bb.width, true);
   ArraySetAsSeries(bb.widthMA, true);
   ArraySetAsSeries(bb.upper, true);
   ArraySetAsSeries(bb.middle, true);
   ArraySetAsSeries(bb.lower, true);
   ArraySetAsSeries(rsiValues, true);
   ArraySetAsSeries(atrValues, true);
   ArraySetAsSeries(fastMA.values, true);
   ArraySetAsSeries(slowMA.values, true);
   
   // 初始化最后K线时间
   datetime timeArray[1];
   CopyTime(_Symbol, TimeFrame, 0, 1, timeArray); // 获取最新K线时间
   lastBarTime = timeArray[0];
   
   Print("开始预加载历史数据...");
   // 预加载历史数据 - 即使加载失败也继续运行
   if(!InitializeIndicatorData(100))
   {
      Print("警告: 历史数据加载不完整，但EA将继续运行");
      // 不返回INIT_FAILED，允许EA继续运行
   }
   else
   {
      Print("历史数据加载完成");
   }
   
   // 初始化市场状态
   CalculateMarketCondition();
   UpdateInfoPanel();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 初始化指标数据                                                   |
//+------------------------------------------------------------------+
bool InitializeIndicatorData(int bars)
{
   // 预分配数组空间
   // ADX数据
   ArrayResize(adx.main, bars);
   ArrayResize(adx.plus, bars);
   ArrayResize(adx.minus, bars);
   
   // 布林带数据
   ArrayResize(bb.upper, bars);
   ArrayResize(bb.middle, bars);
   ArrayResize(bb.lower, bars);
   ArrayResize(bb.width, bars);
   ArrayResize(bb.widthMA, bars);
   
   // 其他指标数据
   ArrayResize(rsiValues, bars);
   ArrayResize(atrValues, bars);
   
   // 移动平均线数据
   ArrayResize(fastMA.values, MathMin(3, bars));
   ArrayResize(slowMA.values, MathMin(3, bars));
   
   // 等待指标缓冲区准备就绪 - 增加等待时间至10秒
   int waitTime = 0;
   int maxWaitTime = 10000; // 最多等待10秒
   Print("等待指标缓冲区准备就绪...");
   
   while(!BarsCalculated(adx.handle) || !BarsCalculated(bb.handle) || 
         !BarsCalculated(rsiHandle) || !BarsCalculated(bb.widthMAHandle) || 
         !BarsCalculated(atrHandle) || !BarsCalculated(fastMA.handle) ||
         !BarsCalculated(slowMA.handle))
   {
      Sleep(200); // 增加每次等待时间
      waitTime += 200;
      
      // 每秒输出一次等待状态
      if(waitTime % 1000 == 0) {
         Print("已等待", waitTime/1000, "秒，指标缓冲区准备中...");
      }
      
      if(waitTime > maxWaitTime)
      {
         Print("指标缓冲区准备超时，但将继续尝试加载数据");
         break; // 即使超时也继续尝试加载数据
      }
   }
   
   // 尝试加载历史数据 - 增加重试次数至10次
   int maxAttempts = 10;
   Print("开始加载历史数据...");
   
   for(int attempt=0; attempt < maxAttempts; attempt++)
   {
      // 如果是第一次尝试之后的尝试，先等待一段时间
      if(attempt > 0) {
         int wait_time = 500 * (attempt + 1);
         Print("第", attempt+1, "次尝试，等待", wait_time, "毫秒后重试...");
         Sleep(wait_time);
      }
      
      bool success = true;
      int loadedBars = 0;
      
      // ADX指标数据 - 使用更健壮的加载方式
      loadedBars = CopyBuffer(adx.handle, 0, 0, bars, adx.main);
      if(loadedBars != bars) {
         Print("ADX主线数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      loadedBars = CopyBuffer(adx.handle, 1, 0, bars, adx.plus);
      if(loadedBars != bars) {
         Print("+DI数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      loadedBars = CopyBuffer(adx.handle, 2, 0, bars, adx.minus);
      if(loadedBars != bars) {
         Print("-DI数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      // 布林带数据
      loadedBars = CopyBuffer(bb.handle, 0, 0, bars, bb.middle);
      if(loadedBars != bars) {
         Print("布林带中轨数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      loadedBars = CopyBuffer(bb.handle, 1, 0, bars, bb.upper);
      if(loadedBars != bars) {
         Print("布林带上轨数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      loadedBars = CopyBuffer(bb.handle, 2, 0, bars, bb.lower);
      if(loadedBars != bars) {
         Print("布林带下轨数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
         success = false;
      }
      
      // 如果基础数据加载成功，继续加载其他数据
      if(success) {
         // 自定义计算布林带宽度
         for(int i=0; i<bars; i++) {
            if(bb.middle[i] != 0) {
               bb.width[i] = (bb.upper[i] - bb.lower[i]) / bb.middle[i];
            } else {
               bb.width[i] = 0;
            }
         }
         
         // 计算布林带宽度的移动平均
         loadedBars = CopyBuffer(bb.widthMAHandle, 0, 0, bars, bb.widthMA);
         if(loadedBars != bars) {
            Print("布林带宽度MA数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
            success = false;
         }
         
         // RSI数据
         loadedBars = CopyBuffer(rsiHandle, 0, 0, bars, rsiValues);
         if(loadedBars != bars) {
            Print("RSI数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
            success = false;
         }
         
         // ATR数据
         loadedBars = CopyBuffer(atrHandle, 0, 0, bars, atrValues);
         if(loadedBars != bars) {
            Print("ATR数据加载不完整: 请求", bars, "根K线，实际加载", loadedBars);
            success = false;
         }
         
         // 移动平均线数据
         int maBarCount = MathMin(3, bars);
         loadedBars = CopyBuffer(fastMA.handle, 0, 0, maBarCount, fastMA.values);
         if(loadedBars != maBarCount) {
            Print("快速MA数据加载不完整: 请求", maBarCount, "根K线，实际加载", loadedBars);
            success = false;
         }
         
         loadedBars = CopyBuffer(slowMA.handle, 0, 0, maBarCount, slowMA.values);
         if(loadedBars != maBarCount) {
            Print("慢速MA数据加载不完整: 请求", maBarCount, "根K线，实际加载", loadedBars);
            success = false;
         }
         
         if(success) {
            Print("历史数据加载成功 (第", attempt+1, "次尝试)");
            return true;
         }
      }
   }
   
   // 即使加载失败，也尝试使用已有数据继续运行
   Print("警告: 历史数据加载不完整，已达到最大重试次数(", maxAttempts, ")，将使用已加载的数据继续运行");
   return true; // 返回true以允许EA继续运行
}

//+------------------------------------------------------------------+
//| OnTick函数                                                       |
//+------------------------------------------------------------------+
void OnTick()
{
   // 获取当前图表周期
   if(Period() != TimeFrame){
      Alert("请在", EnumToString(TimeFrame), "时间框架使用本EA");
      return;
   }

   // 检查是否有新K线形成
   datetime currentBarTime[1];
   if(CopyTime(_Symbol, TimeFrame, 0, 1, currentBarTime) != 1) {
      Print("无法获取当前K线时间，将在下一个Tick重试");
      return;
   }
   
   // 如果K线时间未变化（未生成新K线），直接返回
   if(currentBarTime[0] == lastBarTime) return;

   // 更新最后检测时间
   lastBarTime = currentBarTime[0];
   
   // 获取指标数据 - 增加重试次数和等待时间
   for(int attempt=0; attempt<5; attempt++) // 最多重试5次
   {
      // 如果不是第一次尝试，增加等待时间
      if(attempt > 0) {
         int wait_time = 100 * (attempt + 1);
         Print("第", attempt+1, "次尝试获取指标数据，等待", wait_time, "毫秒...");
         Sleep(wait_time);
      }
      
      // 使用更健壮的数据加载方式
      bool success = true;
      int loadedBars = 0;
      
      // ADX指标数据
      loadedBars = CopyBuffer(adx.handle, 0, 0, 3, adx.main);
      if(loadedBars != 3) {
         Print("ADX主线数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(adx.handle, 1, 0, 3, adx.plus);
      if(loadedBars != 3) {
         Print("+DI数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(adx.handle, 2, 0, 3, adx.minus);
      if(loadedBars != 3) {
         Print("-DI数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      // 布林带数据
      loadedBars = CopyBuffer(bb.handle, 0, 0, 3, bb.middle);
      if(loadedBars != 3) {
         Print("布林带中轨数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(bb.handle, 1, 0, 3, bb.upper);
      if(loadedBars != 3) {
         Print("布林带上轨数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(bb.handle, 2, 0, 3, bb.lower);
      if(loadedBars != 3) {
         Print("布林带下轨数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      // 其他指标数据
      loadedBars = CopyBuffer(rsiHandle, 0, 0, 3, rsiValues);
      if(loadedBars != 3) {
         Print("RSI数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(atrHandle, 0, 0, 3, atrValues);
      if(loadedBars != 3) {
         Print("ATR数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(fastMA.handle, 0, 0, 3, fastMA.values);
      if(loadedBars != 3) {
         Print("快速MA数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      loadedBars = CopyBuffer(slowMA.handle, 0, 0, 3, slowMA.values);
      if(loadedBars != 3) {
         Print("慢速MA数据加载不完整: ", loadedBars, "/3");
         success = false;
      }
      
      if(success)
      {
         // 计算布林带宽度
         for(int i=0; i<3; i++) {
            if(bb.middle[i] != 0) {
               bb.width[i] = (bb.upper[i] - bb.lower[i]) / bb.middle[i];
            } else {
               bb.width[i] = 0;
            }
         }
         
         // 计算市场状态
         CalculateMarketCondition();
         UpdateInfoPanel();
         return;
      }
   }
   
   Print("警告: 指标数据获取不完整，本次Tick跳过市场状态计算");
}

//+------------------------------------------------------------------+
//| 计算市场状态                                                     |
//+------------------------------------------------------------------+
void CalculateMarketCondition()
{
   string oldTrendDirection = trendDirection;
   string oldRangeCondition = rangeCondition;
   
   // 震荡检测
   isConsolidation = true;
   
   // ADX条件：主线低于阈值且+DI/-DI收敛
   bool adxCondition = adx.main[1] < ADX_Threshold && 
                      MathAbs(adx.plus[1] - adx.minus[1]) < (ADX_Threshold * 0.5);
   
   // 布林带宽度条件：宽度较小
   bool bbCondition = bb.width[1] < (bb.width[2] * 1.1) && bb.width[1] < 0.02;
   
   // RSI条件：在中间区域
   bool rsiCondition = rsiValues[1] > 40 && rsiValues[1] < 60;
   
   // 价格波动条件：当前K线波动较小
   double currentRange = (iHigh(_Symbol,TimeFrame,1) - iLow(_Symbol,TimeFrame,1)) / iClose(_Symbol,TimeFrame,1);
   bool priceRangeCondition = currentRange <= PriceFluctuation;
   
   // 综合判断
   if(!adxCondition || !priceRangeCondition) {
      isConsolidation = false;
   }
   
   // 趋势检测 - 始终基于双均线判断主趋势
   isBullTrend = fastMA.values[1] > slowMA.values[1];
   
   // 更新趋势方向和震荡状态
   trendDirection = isBullTrend ? "多头趋势" : "空头趋势";
   rangeCondition = isConsolidation ? "震荡" : "趋势";
   
   // 更新统计信息
   totalCount++;
   if(isConsolidation) consolidationCount++;
   
   // 触发警报 - 当趋势方向或震荡状态变化时
   if((oldTrendDirection != trendDirection || oldRangeCondition != rangeCondition) && 
      (TimeCurrent() - lastAlertTime) > AlertInterval) {
      lastAlertTime = TimeCurrent();
      
      string alertMessage = "";
      
      // 构建警报信息
      if(oldTrendDirection != trendDirection) {
         alertMessage += "趋势变化: " + trendDirection + " ";
      }
      
      if(oldRangeCondition != rangeCondition) {
         alertMessage += "状态变化: " + rangeCondition + " ";
      }
      
      if(alertMessage != "") {
         Alert(alertMessage, Symbol(), " ", EnumToString(TimeFrame));
      }
   }
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建面板背景
   ObjectCreate(chartID, "InfoPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XSIZE, 450);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YSIZE, 150);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   
   // 趋势方向标签
   ObjectCreate(chartID, "TrendDirection", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "TrendDirection", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "TrendDirection", OBJPROP_YDISTANCE, PanelY+10);
   ObjectSetInteger(chartID, "TrendDirection", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "TrendDirection", OBJPROP_COLOR, clrWhite);
   
   // 震荡状态标签
   ObjectCreate(chartID, "RangeCondition", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "RangeCondition", OBJPROP_XDISTANCE, PanelX+280);
   ObjectSetInteger(chartID, "RangeCondition", OBJPROP_YDISTANCE, PanelY+10);
   ObjectSetInteger(chartID, "RangeCondition", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "RangeCondition", OBJPROP_COLOR, clrWhite);
   
   // 双均线指标数据标签
   ObjectCreate(chartID, "MAData", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "MAData", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "MAData", OBJPROP_YDISTANCE, PanelY+40);
   ObjectSetInteger(chartID, "MAData", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "MAData", OBJPROP_COLOR, clrWhite);
   
   // 震荡指标数据标签
   ObjectCreate(chartID, "RangeData", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "RangeData", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "RangeData", OBJPROP_YDISTANCE, PanelY+70);
   ObjectSetInteger(chartID, "RangeData", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "RangeData", OBJPROP_COLOR, clrWhite);
   
   // 历史统计标签
   ObjectCreate(chartID, "ConsolidationStats", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_YDISTANCE, PanelY+100);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_COLOR, clrWhite);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   color panelColor;
   
   // 根据市场状态设置颜色
   if(isConsolidation) {
      panelColor = RangeColor;
   } else if(isBullTrend) {
      panelColor = BullColor;
   } else {
      panelColor = BearColor;
   }
   
   // 更新面板背景
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_COLOR, panelColor);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_WIDTH, 2);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BORDER_COLOR, panelColor);
   
   // 更新趋势方向文本
   ObjectSetString(chartID, "TrendDirection", OBJPROP_TEXT, "趋势方向：" + trendDirection);
   
   // 更新震荡状态文本
   ObjectSetString(chartID, "RangeCondition", OBJPROP_TEXT, "市场状态：" + rangeCondition);
   
   // 更新双均线指标数据
   string maText = StringFormat("快速均线(%d): %.4f  慢速均线(%d): %.4f  差值: %.4f", 
                               fastMA.period, fastMA.values[1], slowMA.period, slowMA.values[1], 
                               fastMA.values[1]-slowMA.values[1]);
   ObjectSetString(chartID, "MAData", OBJPROP_TEXT, maText);
   
   // 更新震荡指标数据
   string rangeText = StringFormat("ADX: %.1f  BB宽度: %.4f  RSI: %.1f", 
                                  adx.main[1], bb.width[1]*10000, rsiValues[1]);
   ObjectSetString(chartID, "RangeData", OBJPROP_TEXT, rangeText);
   
   // 更新统计信息
   double ratio = (totalCount > 0) ? (consolidationCount*100.0/totalCount) : 0;
   ObjectSetString(chartID, "ConsolidationStats", OBJPROP_TEXT, "震荡比例：" + DoubleToString(ratio,1) + "%");
   
   // 更新文本颜色
   color textColor = clrWhite;
   ObjectSetInteger(chartID, "TrendDirection", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "RangeCondition", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "MAData", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "RangeData", OBJPROP_COLOR, textColor);
   ObjectSetInteger(chartID, "ConsolidationStats", OBJPROP_COLOR, textColor);
}

//+------------------------------------------------------------------+
//| 终止函数                                                         |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除所有创建的图表对象
   ObjectDelete(chartID, "InfoPanelBG");
   ObjectDelete(chartID, "TrendDirection");
   ObjectDelete(chartID, "RangeCondition");
   ObjectDelete(chartID, "MAData");
   ObjectDelete(chartID, "RangeData");
   ObjectDelete(chartID, "ConsolidationStats");
   
   // 释放所有指标句柄
   if(adx.handle != INVALID_HANDLE) IndicatorRelease(adx.handle);
   if(bb.handle != INVALID_HANDLE) IndicatorRelease(bb.handle);
   if(bb.widthMAHandle != INVALID_HANDLE) IndicatorRelease(bb.widthMAHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   if(fastMA.handle != INVALID_HANDLE) IndicatorRelease(fastMA.handle);
   if(slowMA.handle != INVALID_HANDLE) IndicatorRelease(slowMA.handle);
   
   Print("EA已卸载，所有资源已释放");
}
