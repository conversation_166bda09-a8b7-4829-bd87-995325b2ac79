//+------------------------------------------------------------------+
//|                                        Install_ZigZag_2B_EA.mq5 |
//|                                    ZigZag 2B Pattern EA Installer |
//|                                         https://www.augment.com |
//+------------------------------------------------------------------+
#property copyright "Augment Agent"
#property version   "1.00"
#property description "ZigZag 2B Pattern EA Installation Script"
#property script_show_inputs

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("开始安装 ZigZag 2B Pattern EA...");
    
    // 检查文件结构
    if(!CheckFileStructure())
    {
        Print("文件结构检查失败！");
        return;
    }
    
    // 验证模块文件
    if(!ValidateModules())
    {
        Print("模块文件验证失败！");
        return;
    }
    
    // 创建示例配置
    CreateSampleConfiguration();
    
    Print("ZigZag 2B Pattern EA 安装完成！");
    Print("使用说明：");
    Print("1. 将 ZigZag_2B_EA 拖放到图表上");
    Print("2. 调整参数设置（ZigZag深度、容差等）");
    Print("3. 启用自动交易（如果需要通知功能）");
    Print("4. 快捷键：H-隐藏/显示面板，R-刷新检测，C-清理对象");
    Print("5. 查看信息面板了解检测到的2B形态");
}

//+------------------------------------------------------------------+
//| 检查文件结构                                                      |
//+------------------------------------------------------------------+
bool CheckFileStructure()
{
    Print("检查文件结构...");
    
    // 检查主EA文件
    if(!FileIsExist("Experts\\ZigZag_2B_EA.mq5"))
    {
        Print("错误：主EA文件不存在 - Experts\\ZigZag_2B_EA.mq5");
        return false;
    }
    
    // 检查Include目录
    string includeFiles[] = {
        "Include\\ZigZag_2B_EA\\Common.mqh",
        "Include\\ZigZag_2B_EA\\ZigZagProcessor.mqh",
        "Include\\ZigZag_2B_EA\\PatternDetector.mqh",
        "Include\\ZigZag_2B_EA\\VisualizationManager.mqh",
        "Include\\ZigZag_2B_EA\\PanelManager.mqh",
        "Include\\ZigZag_2B_EA\\NotificationManager.mqh"
    };
    
    for(int i = 0; i < ArraySize(includeFiles); i++)
    {
        if(!FileIsExist(includeFiles[i]))
        {
            Print("错误：模块文件不存在 - ", includeFiles[i]);
            return false;
        }
    }
    
    Print("文件结构检查通过");
    return true;
}

//+------------------------------------------------------------------+
//| 验证模块文件                                                      |
//+------------------------------------------------------------------+
bool ValidateModules()
{
    Print("验证模块文件...");
    
    // 这里可以添加更详细的模块验证逻辑
    // 例如检查关键函数是否存在等
    
    Print("模块文件验证通过");
    return true;
}

//+------------------------------------------------------------------+
//| 创建示例配置                                                      |
//+------------------------------------------------------------------+
void CreateSampleConfiguration()
{
    Print("创建示例配置文件...");
    
    string configContent = "";
    configContent += "# ZigZag 2B Pattern EA 配置示例\n";
    configContent += "# 此文件展示了EA的主要参数设置\n";
    configContent += "\n";
    configContent += "[ZigZag参数]\n";
    configContent += "深度 = 12          # ZigZag深度，控制摆动点的敏感度\n";
    configContent += "偏差 = 5           # ZigZag偏差，过滤小幅波动\n";
    configContent += "回退 = 3           # ZigZag回退，避免重复信号\n";
    configContent += "\n";
    configContent += "[2B形态参数]\n";
    configContent += "容差 = 0.0005      # 假突破容差，可以是点数或百分比\n";
    configContent += "最小距离 = 10      # 摆动点之间的最小K线距离\n";
    configContent += "严格模式 = true    # 是否启用严格的2B形态验证\n";
    configContent += "\n";
    configContent += "[可视化参数]\n";
    configContent += "看涨颜色 = 绿色    # 看涨2B形态的显示颜色\n";
    configContent += "看跌颜色 = 红色    # 看跌2B形态的显示颜色\n";
    configContent += "线条宽度 = 2       # 绘图线条的宽度\n";
    configContent += "\n";
    configContent += "[通知设置]\n";
    configContent += "警报 = true        # 启用声音警报\n";
    configContent += "邮件 = false       # 启用邮件通知\n";
    configContent += "推送 = false       # 启用手机推送\n";
    configContent += "\n";
    configContent += "[使用技巧]\n";
    configContent += "1. 较小的ZigZag深度会产生更多的摆动点，但可能包含噪音\n";
    configContent += "2. 较大的容差会检测到更多的2B形态，但准确性可能降低\n";
    configContent += "3. 严格模式会减少假信号，但可能错过一些机会\n";
    configContent += "4. 建议在不同时间周期上测试参数的有效性\n";
    
    string fileName = "ZigZag_2B_EA_Config_Sample.txt";
    int fileHandle = FileOpen(fileName, FILE_WRITE | FILE_TXT);
    
    if(fileHandle != INVALID_HANDLE)
    {
        FileWriteString(fileHandle, configContent);
        FileClose(fileHandle);
        Print("示例配置文件已创建：", fileName);
    }
    else
    {
        Print("无法创建配置文件");
    }
}

//+------------------------------------------------------------------+
//| 检查文件是否存在                                                  |
//+------------------------------------------------------------------+
bool FileIsExist(string fileName)
{
    int handle = FileOpen(fileName, FILE_READ);
    if(handle != INVALID_HANDLE)
    {
        FileClose(handle);
        return true;
    }
    return false;
}
