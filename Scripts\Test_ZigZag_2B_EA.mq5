//+------------------------------------------------------------------+
//|                                           Test_ZigZag_2B_EA.mq5 |
//|                                    ZigZag 2B Pattern EA Tester   |
//|                                         https://www.augment.com |
//+------------------------------------------------------------------+
#property copyright "Augment Agent"
#property version   "1.00"
#property description "ZigZag 2B Pattern EA Testing Script"
#property script_show_inputs

// 包含EA模块
#include "../Include/ZigZag_2B_EA/Common.mqh"
#include "../Include/ZigZag_2B_EA/ZigZagProcessor.mqh"
#include "../Include/ZigZag_2B_EA/PatternDetector.mqh"

// 测试参数
input int Test_ZigZag_Depth = 12;
input int Test_ZigZag_Deviation = 5;
input int Test_ZigZag_Backstep = 3;
input double Test_Pattern_Tolerance = 0.0005;
input int Test_Min_Swing_Distance = 10;
input bool Test_Strict_Mode = true;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("开始测试 ZigZag 2B Pattern EA 功能...");
    
    // 初始化测试环境
    if(!InitializeTestEnvironment())
    {
        Print("测试环境初始化失败！");
        return;
    }
    
    // 测试ZigZag处理器
    if(!TestZigZagProcessor())
    {
        Print("ZigZag处理器测试失败！");
        return;
    }
    
    // 测试形态检测器
    if(!TestPatternDetector())
    {
        Print("形态检测器测试失败！");
        return;
    }
    
    // 测试数据验证
    if(!TestDataValidation())
    {
        Print("数据验证测试失败！");
        return;
    }
    
    // 性能测试
    PerformanceTest();
    
    Print("ZigZag 2B Pattern EA 功能测试完成！");
    Print("所有测试均通过，EA可以正常使用。");
}

//+------------------------------------------------------------------+
//| 初始化测试环境                                                    |
//+------------------------------------------------------------------+
bool InitializeTestEnvironment()
{
    Print("初始化测试环境...");
    
    // 初始化统计信息
    InitializeStatistics();
    
    // 设置测试参数
    ZigZag_Depth = Test_ZigZag_Depth;
    ZigZag_Deviation = Test_ZigZag_Deviation;
    ZigZag_Backstep = Test_ZigZag_Backstep;
    Pattern_Tolerance = Test_Pattern_Tolerance;
    Min_Swing_Distance = Test_Min_Swing_Distance;
    Strict_Mode = Test_Strict_Mode;
    
    Print("测试环境初始化完成");
    return true;
}

//+------------------------------------------------------------------+
//| 测试ZigZag处理器                                                  |
//+------------------------------------------------------------------+
bool TestZigZagProcessor()
{
    Print("测试ZigZag处理器...");
    
    // 初始化ZigZag处理器
    if(!InitializeZigZag())
    {
        Print("ZigZag处理器初始化失败");
        return false;
    }
    
    // 更新ZigZag数据
    if(!UpdateZigZagData())
    {
        Print("ZigZag数据更新失败");
        return false;
    }
    
    // 获取摆动点
    SwingPoint points[];
    int pointCount = GetCurrentSwingPoints(points);
    
    Print("检测到 ", pointCount, " 个摆动点");
    
    // 验证摆动点数据
    for(int i = 0; i < MathMin(pointCount, 5); i++) // 只显示前5个
    {
        if(IsValidSwingPoint(points[i]))
        {
            Print("摆动点 ", i, ": 时间=", TimeToString(points[i].time), 
                  " 价格=", DoubleToString(points[i].price, _Digits),
                  " 类型=", (points[i].type == SWING_HIGH ? "高点" : "低点"));
        }
    }
    
    Print("ZigZag处理器测试通过");
    return true;
}

//+------------------------------------------------------------------+
//| 测试形态检测器                                                    |
//+------------------------------------------------------------------+
bool TestPatternDetector()
{
    Print("测试形态检测器...");
    
    // 初始化形态检测器
    if(!InitializePatternDetector())
    {
        Print("形态检测器初始化失败");
        return false;
    }
    
    // 检测形态
    DetectPatterns();
    
    // 获取检测到的形态
    Pattern2B patterns[];
    int patternCount = GetDetectedPatterns(patterns);
    
    Print("检测到 ", patternCount, " 个2B形态");
    
    // 验证形态数据
    for(int i = 0; i < MathMin(patternCount, 3); i++) // 只显示前3个
    {
        if(IsValidPattern(patterns[i]))
        {
            Print("形态 ", i, ": 类型=", GetPatternTypeString(patterns[i].type),
                  " 状态=", GetPatternStatusString(patterns[i].status),
                  " 关键位=", DoubleToString(patterns[i].keyLevel, _Digits),
                  " 强度=", DoubleToString(patterns[i].strength, 2));
        }
    }
    
    Print("形态检测器测试通过");
    return true;
}

//+------------------------------------------------------------------+
//| 测试数据验证                                                      |
//+------------------------------------------------------------------+
bool TestDataValidation()
{
    Print("测试数据验证功能...");
    
    // 创建测试摆动点
    SwingPoint testPoint;
    testPoint.time = TimeCurrent();
    testPoint.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    testPoint.barIndex = 0;
    testPoint.type = SWING_HIGH;
    testPoint.isValid = true;
    testPoint.strength = 1.0;
    
    // 验证有效摆动点
    if(!IsValidSwingPoint(testPoint))
    {
        Print("有效摆动点验证失败");
        return false;
    }
    
    // 测试无效摆动点
    testPoint.price = 0;
    if(IsValidSwingPoint(testPoint))
    {
        Print("无效摆动点验证失败");
        return false;
    }
    
    // 创建测试形态
    Pattern2B testPattern;
    testPattern.type = PATTERN_2B_BULLISH;
    testPattern.status = PATTERN_CONFIRMED;
    testPattern.keyLevel = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    testPattern.fakeBreakLevel = testPattern.keyLevel - 10 * _Point;
    testPattern.entryLevel = testPattern.keyLevel + 5 * _Point;
    
    // 设置有效的摆动点
    testPattern.swing1.time = TimeCurrent() - 3600;
    testPattern.swing1.price = testPattern.keyLevel;
    testPattern.swing1.isValid = true;
    
    testPattern.swing2.time = TimeCurrent() - 1800;
    testPattern.swing2.price = testPattern.fakeBreakLevel;
    testPattern.swing2.isValid = true;
    
    testPattern.swing3.time = TimeCurrent();
    testPattern.swing3.price = testPattern.entryLevel;
    testPattern.swing3.isValid = true;
    
    // 验证有效形态
    if(!IsValidPattern(testPattern))
    {
        Print("有效形态验证失败");
        return false;
    }
    
    Print("数据验证测试通过");
    return true;
}

//+------------------------------------------------------------------+
//| 性能测试                                                          |
//+------------------------------------------------------------------+
void PerformanceTest()
{
    Print("执行性能测试...");
    
    uint startTime = GetTickCount();
    
    // 执行多次检测来测试性能
    for(int i = 0; i < 10; i++)
    {
        UpdateZigZagData();
        DetectPatterns();
    }
    
    uint endTime = GetTickCount();
    uint duration = endTime - startTime;
    
    Print("性能测试结果：");
    Print("执行10次完整检测用时：", duration, " 毫秒");
    Print("平均每次检测用时：", duration / 10, " 毫秒");
    
    if(duration < 1000) // 少于1秒
    {
        Print("性能测试通过 - 执行速度良好");
    }
    else
    {
        Print("性能警告 - 执行速度较慢，可能需要优化");
    }
}

//+------------------------------------------------------------------+
//| 获取当前摆动点（测试用）                                          |
//+------------------------------------------------------------------+
int GetCurrentSwingPoints(SwingPoint &points[])
{
    // 这里应该调用实际的ZigZag处理器函数
    // 为了测试，我们创建一些模拟数据
    ArrayResize(points, 0);
    
    // 模拟一些摆动点数据
    for(int i = 0; i < 5; i++)
    {
        SwingPoint point;
        point.time = TimeCurrent() - i * 3600;
        point.price = SymbolInfoDouble(_Symbol, SYMBOL_BID) + (i % 2 == 0 ? 10 : -10) * _Point;
        point.barIndex = i;
        point.type = (i % 2 == 0) ? SWING_HIGH : SWING_LOW;
        point.isValid = true;
        point.strength = 1.0;
        
        int size = ArraySize(points);
        ArrayResize(points, size + 1);
        points[size] = point;
    }
    
    return ArraySize(points);
}

//+------------------------------------------------------------------+
//| 获取检测到的形态（测试用）                                        |
//+------------------------------------------------------------------+
int GetDetectedPatterns(Pattern2B &patterns[])
{
    // 这里应该调用实际的形态检测器函数
    // 为了测试，我们创建一些模拟数据
    ArrayResize(patterns, 0);
    
    // 模拟一个2B形态
    Pattern2B pattern;
    pattern.type = PATTERN_2B_BULLISH;
    pattern.status = PATTERN_CONFIRMED;
    pattern.formationTime = TimeCurrent() - 1800;
    pattern.keyLevel = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    pattern.fakeBreakLevel = pattern.keyLevel - 10 * _Point;
    pattern.entryLevel = pattern.keyLevel + 5 * _Point;
    pattern.stopLoss = pattern.fakeBreakLevel - 20 * _Point;
    pattern.takeProfit = pattern.entryLevel + 50 * _Point;
    pattern.strength = 0.75;
    pattern.isActive = true;
    
    // 设置摆动点
    pattern.swing1.time = TimeCurrent() - 3600;
    pattern.swing1.price = pattern.keyLevel;
    pattern.swing1.isValid = true;
    
    pattern.swing2.time = TimeCurrent() - 1800;
    pattern.swing2.price = pattern.fakeBreakLevel;
    pattern.swing2.isValid = true;
    
    pattern.swing3.time = TimeCurrent();
    pattern.swing3.price = pattern.entryLevel;
    pattern.swing3.isValid = true;
    
    ArrayResize(patterns, 1);
    patterns[0] = pattern;
    
    return 1;
}
