//+------------------------------------------------------------------+
//|                                               ZigZagPattern2B.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "EA for detecting and visualizing ZigZag patterns"
#property description "Identifies 2B patterns and draws elegant markers"
#property script_show_inputs

// 输入参数
input int      ZigZagDepth     = 12;       // ZigZag深度
input int      ZigZagDeviation = 5;        // ZigZag偏差
input int      ZigZagBackstep  = 3;        // ZigZag回步
input bool     ShowHistory     = true;     // 显示历史标记
input color    UpPatternColor  = clrDodgerBlue; // 上升形态颜色
input color    DownPatternColor= clrOrangeRed; // 下降形态颜色
input int      FontSize        = 9;        // 字体大小
input string   ObjectPrefix    = "Z2B_";   // 对象前缀
input bool     EnableDebug     = false;    // 启用调试输出
input int      MinPatternBars  = 5;        // 形态最小K线间隔

// 枚举
enum ENUM_PATTERN_TYPE {UP_PATTERN, DOWN_PATTERN};

// ZigZag点结构
struct ZigZagPoint
{
   datetime time;
   double price;
   int barIndex;
   bool isHigh;  // true为高点，false为低点
};

// 全局变量
int zigzagHandle;
datetime lastBarTime;
ZigZagPoint lastDetectedPoints[10];  // 缓存最近检测到的形态点
int lastPointsCount = 0;
string detectedPatterns[];  // 已检测形态的ID缓存
datetime lastPatternCheck = 0;  // 上次形态检查时间

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // 创建内置ZigZag指标句柄
   zigzagHandle = iCustom(_Symbol, _Period, "ZigZag",
                         ZigZagDepth, ZigZagDeviation, ZigZagBackstep);
   if(zigzagHandle == INVALID_HANDLE)
   {
      Print("Error creating ZigZag indicator: ", GetLastError());
      Print("Trying alternative ZigZag indicator...");

      // 尝试使用标准路径
      zigzagHandle = iCustom(_Symbol, _Period, "Market\\ZigZag",
                            ZigZagDepth, ZigZagDeviation, ZigZagBackstep);

      if(zigzagHandle == INVALID_HANDLE)
      {
         Print("Failed to create ZigZag indicator with alternative path: ", GetLastError());
         return(INIT_FAILED);
      }
   }

   // 等待指标数据准备就绪
   int attempts = 0;
   while(attempts < 10)
   {
      double testBuffer[1];
      if(CopyBuffer(zigzagHandle, 0, 0, 1, testBuffer) > 0)
         break;
      Sleep(100);
      attempts++;
   }

   if(attempts >= 10)
   {
      Print("ZigZag indicator data not ready after 10 attempts");
      return(INIT_FAILED);
   }

   lastBarTime = 0;
   Print("ZigZagPattern2B EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA去初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除指标句柄
   if(zigzagHandle != INVALID_HANDLE)
      IndicatorRelease(zigzagHandle);
      
   // 如果不需要历史标记，则清除所有对象
   if(!ShowHistory)
   {
      ObjectsDeleteAll(0, ObjectPrefix);
   }
}

//+------------------------------------------------------------------+
//| EA主函数                                                         |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime currentBarTime = iTime(_Symbol, _Period, 0);

   // 只在新K线时检测形态，避免频繁检测
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;

      // 限制检测频率：至少间隔5分钟
      if(TimeCurrent() - lastPatternCheck >= 300)
      {
         DetectPatterns();
         lastPatternCheck = TimeCurrent();
      }
   }

   // 检查现有形态的突破情况（每tick检查）
   CheckExistingPatternBreakouts();
}

//+------------------------------------------------------------------+
//| 检查现有形态的突破情况                                           |
//+------------------------------------------------------------------+
void CheckExistingPatternBreakouts()
{
   // 遍历所有已创建的水平线对象，检查突破
   for(int i = ObjectsTotal(0, -1, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i, -1, OBJ_HLINE);
      if(StringFind(objName, ObjectPrefix) == 0 && StringFind(objName, "_HLine") > 0)
      {
         // 提取形态ID
         string patternId = StringSubstr(objName, 0, StringFind(objName, "_HLine"));

         // 检查是否已经有突破标记
         if(ObjectFind(0, patternId + "_Break") < 0)
         {
            double level = ObjectGetDouble(0, objName, OBJPROP_PRICE);
            color lineColor = (color)ObjectGetInteger(0, objName, OBJPROP_COLOR);

            // 判断是上升还是下降形态
            if(StringFind(patternId, "UP_") > 0)
            {
               CheckBreakdown(patternId, level, TimeCurrent() - 3600, lineColor);
            }
            else if(StringFind(patternId, "DN_") > 0)
            {
               CheckBreakout(patternId, level, TimeCurrent() - 3600, lineColor);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检测形态函数                                                     |
//+------------------------------------------------------------------+
void DetectPatterns()
{
   // 验证指标句柄有效性
   if(zigzagHandle == INVALID_HANDLE)
   {
      Print("ZigZag indicator handle is invalid");
      return;
   }

   // 获取ZigZag点序列
   ZigZagPoint points[];
   int pointCount = GetZigZagPoints(points, 50);  // 减少点数以提高性能

   if(pointCount < 3)
   {
      if(pointCount == 0)
         Print("Warning: No ZigZag points found");
      return;
   }

   if(EnableDebug)
      Print("Found ", pointCount, " ZigZag points, detecting patterns...");

   // 检测2B形态
   Detect2BPatterns(points, pointCount);
}

//+------------------------------------------------------------------+
//| 获取ZigZag点序列                                                 |
//+------------------------------------------------------------------+
int GetZigZagPoints(ZigZagPoint &points[], int maxPoints)
{
   // 获取足够的历史数据
   int dataSize = MathMin(maxPoints * 10, 1000);  // 确保有足够数据来找到ZigZag点
   double zigzagValues[];
   datetime timeArray[];

   if(CopyBuffer(zigzagHandle, 0, 0, dataSize, zigzagValues) <= 0)
   {
      Print("Failed to copy ZigZag buffer: ", GetLastError());
      return 0;
   }

   if(CopyTime(_Symbol, _Period, 0, dataSize, timeArray) <= 0)
   {
      Print("Failed to copy time data: ", GetLastError());
      return 0;
   }

   ArraySetAsSeries(zigzagValues, true);
   ArraySetAsSeries(timeArray, true);

   // 提取非零ZigZag点
   ArrayResize(points, 0);
   int foundPoints = 0;

   for(int i = 0; i < dataSize && foundPoints < maxPoints; i++)
   {
      if(zigzagValues[i] != 0.0 && zigzagValues[i] != EMPTY_VALUE)
      {
         ZigZagPoint point;
         point.time = timeArray[i];
         point.price = zigzagValues[i];
         point.barIndex = i;

         // 判断是高点还是低点
         double high = iHigh(_Symbol, _Period, i);
         double low = iLow(_Symbol, _Period, i);
         point.isHigh = (MathAbs(zigzagValues[i] - high) < MathAbs(zigzagValues[i] - low));

         // 添加到数组
         ArrayResize(points, foundPoints + 1);
         points[foundPoints] = point;
         foundPoints++;
      }
   }

   return foundPoints;
}

//+------------------------------------------------------------------+
//| 检测2B形态                                                       |
//+------------------------------------------------------------------+
void Detect2BPatterns(ZigZagPoint &points[], int pointCount)
{
   if(pointCount < 3) return;

   // 检查最近的3个点是否形成2B形态
   for(int i = 0; i <= pointCount - 3; i++)
   {
      ZigZagPoint pointA = points[i + 2];  // 最早的点
      ZigZagPoint pointB = points[i + 1];  // 中间点
      ZigZagPoint pointC = points[i];      // 最新的点

      // 验证2B上升形态：高点A -> 低点B -> 高点C (C > A)
      if(pointA.isHigh && !pointB.isHigh && pointC.isHigh)
      {
         if(IsValid2BPattern(pointA, pointB, pointC, true))
         {
            VisualizePattern(UP_PATTERN, pointA.time, pointA.price,
                           pointB.time, pointB.price, pointC.time, pointC.price);
         }
      }
      // 验证2B下降形态：低点A -> 高点B -> 低点C (C < A)
      else if(!pointA.isHigh && pointB.isHigh && !pointC.isHigh)
      {
         if(IsValid2BPattern(pointA, pointB, pointC, false))
         {
            VisualizePattern(DOWN_PATTERN, pointA.time, pointA.price,
                           pointB.time, pointB.price, pointC.time, pointC.price);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 验证2B形态有效性                                                 |
//+------------------------------------------------------------------+
bool IsValid2BPattern(ZigZagPoint &pointA, ZigZagPoint &pointB, ZigZagPoint &pointC, bool isUpPattern)
{
   // 验证数据有效性
   if(pointA.price <= 0 || pointB.price <= 0 || pointC.price <= 0)
   {
      Print("Invalid price data in pattern validation");
      return false;
   }

   // 验证时间顺序
   if(pointA.time >= pointB.time || pointB.time >= pointC.time)
   {
      Print("Invalid time sequence in pattern");
      return false;
   }

   // 验证时间间隔不能太短
   int minBars = MinPatternBars;
   if(iBarShift(_Symbol, _Period, pointA.time) - iBarShift(_Symbol, _Period, pointB.time) < minBars ||
      iBarShift(_Symbol, _Period, pointB.time) - iBarShift(_Symbol, _Period, pointC.time) < minBars)
   {
      return false;
   }

   // 验证价格关系和最小波动幅度
   int spread = (int)SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
   double minMove = _Point * MathMax(50, spread * 2);

   if(isUpPattern)
   {
      // 上升2B：C > A，B < A，且有足够的波动幅度
      if(pointC.price <= pointA.price) return false;
      if(pointB.price >= pointA.price) return false;
      if(pointA.price - pointB.price < minMove) return false;
      if(pointC.price - pointA.price < minMove) return false;
   }
   else
   {
      // 下降2B：C < A，B > A，且有足够的波动幅度
      if(pointC.price >= pointA.price) return false;
      if(pointB.price <= pointA.price) return false;
      if(pointB.price - pointA.price < minMove) return false;
      if(pointA.price - pointC.price < minMove) return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 可视化形态                                                       |
//+------------------------------------------------------------------+
void VisualizePattern(ENUM_PATTERN_TYPE patternType,
                     datetime aTime, double aPrice,
                     datetime bTime, double bPrice,
                     datetime cTime, double cPrice)
{
   string id = ObjectPrefix + (patternType == UP_PATTERN ? "UP_" : "DN_") + (string)aTime;

   // 检查是否已经绘制过
   if(ObjectFind(0, id + "_HLine") >= 0)
      return;

   // 检查是否在缓存中
   if(IsPatternAlreadyDetected(id))
      return;

   // 添加到已检测形态缓存
   AddToDetectedPatterns(id);
   
   color patternColor = (patternType == UP_PATTERN) ? UpPatternColor : DownPatternColor;
   
   // 1. 绘制水平虚线（A点向右延伸）
   ObjectCreate(0, id + "_HLine", OBJ_HLINE, 0, 0, aPrice);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_COLOR, patternColor);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_STYLE, STYLE_DOT);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_BACK, true);
   
   // 2. 标记交点A
   CreateSymbol(id + "_PointA", aTime, aPrice, patternColor, 159, "A");
   
   // 3. 绘制关键点标记
   if(patternType == UP_PATTERN)
   {
      CreateSymbol(id + "_PointB", bTime, bPrice, patternColor, 162, "B");
      CreateSymbol(id + "_PointC", cTime, cPrice, patternColor, 233, "C");
   }
   else
   {
      CreateSymbol(id + "_PointB", bTime, bPrice, patternColor, 161, "B");
      CreateSymbol(id + "_PointC", cTime, cPrice, patternColor, 234, "C");
   }
   
   // 4. 绘制趋势线
   ObjectCreate(0, id + "_L1", OBJ_TREND, 0, bTime, bPrice, cTime, cPrice);
   ObjectSetInteger(0, id + "_L1", OBJPROP_COLOR, patternColor);
   ObjectSetInteger(0, id + "_L1", OBJPROP_BACK, true);
   
   // 5. 检查突破点
   if(patternType == UP_PATTERN)
   {
      CheckBreakdown(id, aPrice, cTime, patternColor);
   }
   else
   {
      CheckBreakout(id, aPrice, cTime, patternColor);
   }
}

//+------------------------------------------------------------------+
//| 创建标记符号                                                     |
//+------------------------------------------------------------------+
void CreateSymbol(string name, datetime time, double price, color clr, int code, string text="")
{
   // 创建标记
   ObjectCreate(0, name, OBJ_ARROW, 0, time, price);
   ObjectSetInteger(0, name, OBJPROP_ARROWCODE, code);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   
   // 创建文本标签
   if(text != "")
   {
      double offset = (code == 233 || code == 234) ? _Point * 400 : -(_Point * 400);
      ObjectCreate(0, name + "_Label", OBJ_TEXT, 0, time, price + offset);
      ObjectSetString(0, name + "_Label", OBJPROP_TEXT, text);
      ObjectSetInteger(0, name + "_Label", OBJPROP_COLOR, clr);
      ObjectSetInteger(0, name + "_Label", OBJPROP_FONTSIZE, FontSize);
   }
}

//+------------------------------------------------------------------+
//| 检查跌破点                                                       |
//+------------------------------------------------------------------+
void CheckBreakdown(string id, double supportLevel, datetime startTime, color clr)
{
   // 检查突破点是否已经存在
   if(ObjectFind(0, id + "_Break") >= 0)
      return;

   int startIndex = iBarShift(_Symbol, _Period, startTime);
   if(startIndex < 0) return;

   for(int i = startIndex; i >= 0; i--)
   {
      double low = iLow(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);
      datetime time = iTime(_Symbol, _Period, i);

      // 检查收盘价跌破支撑位（更可靠的突破确认）
      if(close < supportLevel)
      {
         // 在实际突破的价格位置创建标记
         CreateSymbol(id + "_Break", time, close, clr, 218, "跌破");

         // 绘制突破线
         ObjectCreate(0, id + "_BreakLine", OBJ_VLINE, 0, time, 0);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_COLOR, clr);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_STYLE, STYLE_DASH);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_BACK, true);

         if(EnableDebug)
            Print("检测到跌破：", _Symbol, " 时间：", TimeToString(time), " 价格：", DoubleToString(close, _Digits));
         break;
      }
   }
}

//+------------------------------------------------------------------+
//| 检查突破点                                                       |
//+------------------------------------------------------------------+
void CheckBreakout(string id, double resistanceLevel, datetime startTime, color clr)
{
   // 检查突破点是否已经存在
   if(ObjectFind(0, id + "_Break") >= 0)
      return;

   int startIndex = iBarShift(_Symbol, _Period, startTime);
   if(startIndex < 0) return;

   for(int i = startIndex; i >= 0; i--)
   {
      double high = iHigh(_Symbol, _Period, i);
      double close = iClose(_Symbol, _Period, i);
      datetime time = iTime(_Symbol, _Period, i);

      // 检查收盘价突破阻力位（更可靠的突破确认）
      if(close > resistanceLevel)
      {
         // 在实际突破的价格位置创建标记
         CreateSymbol(id + "_Break", time, close, clr, 217, "突破");

         // 绘制突破线
         ObjectCreate(0, id + "_BreakLine", OBJ_VLINE, 0, time, 0);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_COLOR, clr);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_STYLE, STYLE_DASH);
         ObjectSetInteger(0, id + "_BreakLine", OBJPROP_BACK, true);

         if(EnableDebug)
            Print("检测到突破：", _Symbol, " 时间：", TimeToString(time), " 价格：", DoubleToString(close, _Digits));
         break;
      }
   }
}

//+------------------------------------------------------------------+
//| 检查形态是否已经检测过                                           |
//+------------------------------------------------------------------+
bool IsPatternAlreadyDetected(string patternId)
{
   for(int i = 0; i < ArraySize(detectedPatterns); i++)
   {
      if(detectedPatterns[i] == patternId)
         return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 添加形态到已检测缓存                                             |
//+------------------------------------------------------------------+
void AddToDetectedPatterns(string patternId)
{
   int size = ArraySize(detectedPatterns);
   ArrayResize(detectedPatterns, size + 1);
   detectedPatterns[size] = patternId;

   // 限制缓存大小，避免内存过度使用
   if(size > 100)
   {
      // 移除最旧的50个记录
      for(int i = 0; i < 50; i++)
      {
         detectedPatterns[i] = detectedPatterns[i + 50];
      }
      ArrayResize(detectedPatterns, 50);
   }
}

//+------------------------------------------------------------------+