//+------------------------------------------------------------------+
//|                                               ZigZagPattern2B.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "EA for detecting and visualizing ZigZag patterns"
#property description "Identifies 2B patterns and draws elegant markers"
#property script_show_inputs

// 输入参数
input int      ZigZagDepth     = 12;       // ZigZag深度
input int      ZigZagDeviation = 5;        // ZigZag偏差
input int      ZigZagBackstep  = 3;        // ZigZag回步
input bool     ShowHistory     = true;     // 显示历史标记
input color    UpPatternColor  = clrDodgerBlue; // 上升形态颜色
input color    DownPatternColor= clrOrangeRed; // 下降形态颜色
input int      FontSize        = 9;        // 字体大小
input string   ObjectPrefix    = "Z2B_";   // 对象前缀

// 枚举
enum ENUM_PATTERN_TYPE {UP_PATTERN, DOWN_PATTERN};

// 全局变量
int zigzagHandle;
datetime lastBarTime;

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // 创建ZigZag指标句柄
   zigzagHandle = iCustom(_Symbol, _Period, "Examples\\ZigZag", 
                         ZigZagDepth, ZigZagDeviation, ZigZagBackstep);
   if(zigzagHandle == INVALID_HANDLE)
   {
      Print("Error creating ZigZag indicator: ", GetLastError());
      return(INIT_FAILED);
   }
   
   lastBarTime = 0;
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA去初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除指标句柄
   if(zigzagHandle != INVALID_HANDLE)
      IndicatorRelease(zigzagHandle);
      
   // 如果不需要历史标记，则清除所有对象
   if(!ShowHistory)
   {
      ObjectsDeleteAll(0, ObjectPrefix);
   }
}

//+------------------------------------------------------------------+
//| EA主函数                                                         |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      DetectPatterns();
   }
}

//+------------------------------------------------------------------+
//| 检测形态函数                                                     |
//+------------------------------------------------------------------+
void DetectPatterns()
{
   // 获取ZigZag数据
   double high[], low[], zigzag[];
   datetime time[];
   
   CopyTime(_Symbol, _Period, 0, 500, time);
   ArraySetAsSeries(time, true);
   
   // 获取ZigZag数据
   double zigzagValues[];
   CopyBuffer(zigzagHandle, 0, 0, 500, zigzagValues);
   ArraySetAsSeries(zigzagValues, true);

   // 寻找上升形态
   for(int i = 10; i < 300; i++)
   {
      if(zigzagValues[i] != 0 && zigzagValues[i+3] != 0)
      {
         // 寻找上升形态：高点A(点3) -> 低点B(点2) -> 高点C(点1) > A
         double A = zigzagValues[i+3];
         double B = zigzagValues[i+2];
         double C = zigzagValues[i+1];
         
         if(C > A && B < A)
         {
            VisualizePattern(UP_PATTERN, time[i+3], A, time[i+2], B, time[i+1], C);
            break;
         }
      }
   }
   
   // 寻找下降形态
   for(int i = 10; i < 300; i++)
   {
      if(zigzagValues[i] != 0 && zigzagValues[i+3] != 0)
      {
         // 寻找下降形态：低点A(点3) -> 高点B(点2) -> 低点C(点1) < A
         double A = zigzagValues[i+3];
         double B = zigzagValues[i+2];
         double C = zigzagValues[i+1];
         
         if(C < A && B > A)
         {
            VisualizePattern(DOWN_PATTERN, time[i+3], A, time[i+2], B, time[i+1], C);
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 可视化形态                                                       |
//+------------------------------------------------------------------+
void VisualizePattern(ENUM_PATTERN_TYPE patternType, 
                     datetime aTime, double aPrice,
                     datetime bTime, double bPrice,
                     datetime cTime, double cPrice)
{
   string id = ObjectPrefix + (patternType == UP_PATTERN ? "UP_" : "DN_") + (string)aTime;
   
   // 检查是否已经绘制过
   if(ObjectFind(0, id + "_HLine") >= 0)
      return;
   
   color patternColor = (patternType == UP_PATTERN) ? UpPatternColor : DownPatternColor;
   
   // 1. 绘制水平虚线（A点向右延伸）
   ObjectCreate(0, id + "_HLine", OBJ_HLINE, 0, 0, aPrice);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_COLOR, patternColor);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_STYLE, STYLE_DOT);
   ObjectSetInteger(0, id + "_HLine", OBJPROP_BACK, true);
   
   // 2. 标记交点A
   CreateSymbol(id + "_PointA", aTime, aPrice, patternColor, 159, "A");
   
   // 3. 绘制关键点标记
   if(patternType == UP_PATTERN)
   {
      CreateSymbol(id + "_PointB", bTime, bPrice, patternColor, 162, "B");
      CreateSymbol(id + "_PointC", cTime, cPrice, patternColor, 233, "C");
   }
   else
   {
      CreateSymbol(id + "_PointB", bTime, bPrice, patternColor, 161, "B");
      CreateSymbol(id + "_PointC", cTime, cPrice, patternColor, 234, "C");
   }
   
   // 4. 绘制趋势线
   ObjectCreate(0, id + "_L1", OBJ_TREND, 0, bTime, bPrice, cTime, cPrice);
   ObjectSetInteger(0, id + "_L1", OBJPROP_COLOR, patternColor);
   ObjectSetInteger(0, id + "_L1", OBJPROP_BACK, true);
   
   // 5. 检查突破点
   if(patternType == UP_PATTERN)
   {
      CheckBreakdown(id, aPrice, cTime, patternColor);
   }
   else
   {
      CheckBreakout(id, aPrice, cTime, patternColor);
   }
}

//+------------------------------------------------------------------+
//| 创建标记符号                                                     |
//+------------------------------------------------------------------+
void CreateSymbol(string name, datetime time, double price, color clr, int code, string text="")
{
   // 创建标记
   ObjectCreate(0, name, OBJ_ARROW, 0, time, price);
   ObjectSetInteger(0, name, OBJPROP_ARROWCODE, code);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   
   // 创建文本标签
   if(text != "")
   {
      double offset = (code == 233 || code == 234) ? _Point * 400 : -(_Point * 400);
      ObjectCreate(0, name + "_Label", OBJ_TEXT, 0, time, price + offset);
      ObjectSetString(0, name + "_Label", OBJPROP_TEXT, text);
      ObjectSetInteger(0, name + "_Label", OBJPROP_COLOR, clr);
      ObjectSetInteger(0, name + "_Label", OBJPROP_FONTSIZE, FontSize);
   }
}

//+------------------------------------------------------------------+
//| 检查跌破点                                                       |
//+------------------------------------------------------------------+
void CheckBreakdown(string id, double supportLevel, datetime startTime, color clr)
{
   // 检查突破点是否已经存在
   if(ObjectFind(0, id + "_Break") >= 0)
      return;
      
   int startIndex = iBarShift(_Symbol, _Period, startTime);
   
   for(int i = startIndex; i >= 0; i--)
   {
      double low = iLow(_Symbol, _Period, i);
      datetime time = iTime(_Symbol, _Period, i);
      
      if(low < supportLevel)
      {
         // 创建标记
         CreateSymbol(id + "_Break", time, supportLevel, clr, 218, "突破");
         break;
      }
   }
}

//+------------------------------------------------------------------+
//| 检查突破点                                                       |
//+------------------------------------------------------------------+
void CheckBreakout(string id, double resistanceLevel, datetime startTime, color clr)
{
   // 检查突破点是否已经存在
   if(ObjectFind(0, id + "_Break") >= 0)
      return;
      
   int startIndex = iBarShift(_Symbol, _Period, startTime);
   
   for(int i = startIndex; i >= 0; i--)
   {
      double high = iHigh(_Symbol, _Period, i);
      datetime time = iTime(_Symbol, _Period, i);
      
      if(high > resistanceLevel)
      {
         // 创建标记
         CreateSymbol(id + "_Break", time, resistanceLevel, clr, 217, "突破");
         break;
      }
   }
}
//+------------------------------------------------------------------+