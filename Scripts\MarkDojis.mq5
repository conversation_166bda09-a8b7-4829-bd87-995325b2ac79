//+------------------------------------------------------------------+
//| 标记十字星形态                                                   |
//+------------------------------------------------------------------+
void MarkDojis(int startBar = 1, int barsToCheck = 200, double threshold = 0.1) {
    // 删除旧标记避免重复
    ObjectsDeleteAll(0, "Doji_");
    
    for(int i = startBar; i < MathMin(barsToCheck, Bars(_Symbol, _Period)); i++) {
        double open = iOpen(_Symbol, _Period, i);
        double close = iClose(_Symbol, _Period, i);
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        
        // 计算实体大小和影线比例
        double bodySize = MathAbs(close - open);
        double totalRange = high - low;
        
        // 防止除零错误
        if(totalRange <= 0) continue;
        
        double bodyRatio = bodySize / totalRange;
        double upperShadow = high - MathMax(open, close);
        double lowerShadow = MathMin(open, close) - low;
        
        // 十字星识别条件
        bool isDoji = bodyRatio < threshold &&           // 实体很小
                      upperShadow > bodySize &&          // 上影线明显
                      lowerShadow > bodySize &&          // 下影线明显
                      totalRange > 10*Point();           // 排除极小波动
        
        if(isDoji) {
            // 在K线最高价上方绘制标记
            string name = "Doji_" + IntegerToString(i);
            double price = high + 3 * _Point;
            
            ObjectCreate(0, name, OBJ_ARROW_UP, 0, iTime(_Symbol, _Period, i), price);
            ObjectSetInteger(0, name, OBJPROP_COLOR, clrDodgerBlue);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
            ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
        }
    }
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 脚本入口函数                                                     |
//+------------------------------------------------------------------+
void OnStart() {
    MarkDojis(1, 500); // 检查最近500根K线
}