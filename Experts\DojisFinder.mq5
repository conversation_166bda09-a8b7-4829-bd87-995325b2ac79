//+------------------------------------------------------------------+
//| 十字星检测EA                                                    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property strict

#include <WeChatRobotManager.mqh>

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
input string   WebhookURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 微信机器人Webhook地址

input int      CheckBars = 200;              // 检测K线数量
input double   BodyRatioThreshold = 0.1;     // 实体比例阈值
input int      CheckInterval = 60;           // 检测间隔(秒)

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
CWeChatRobotManager weChat;           // 微信机器人实例
datetime lastCheckTime = 0;           // 上次检测时间

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化微信机器人
    if(WebhookURL == "") {
        Alert("请配置企业微信webhook地址！");
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    if(!weChat.Initialize(WebhookURL)) {
        Alert("微信机器人初始化失败！");
        return(INIT_FAILED);
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, "Doji_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取当前K线的开盘时间
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    
    // 如果不是新K线，直接返回
    if(currentBarTime == lastCheckTime) return;
    
    // 更新检测时间
    lastCheckTime = currentBarTime;
    
    // 执行十字星检测
    CheckDojis();
}

//+------------------------------------------------------------------+
//| 十字星检测函数                                                    |
//+------------------------------------------------------------------+
void CheckDojis()
{
    // 删除旧标记
    ObjectsDeleteAll(0, "Doji_");
    
    for(int i = 1; i < MathMin(CheckBars, Bars(_Symbol, _Period)); i++) {
        double open = iOpen(_Symbol, _Period, i);
        double close = iClose(_Symbol, _Period, i);
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        
        // 计算实体大小和影线比例
        double bodySize = MathAbs(close - open);
        double totalRange = high - low;
        
        // 防止除零错误
        if(totalRange <= 0) continue;
        
        double bodyRatio = bodySize / totalRange;
        double upperShadow = high - MathMax(open, close);
        double lowerShadow = MathMin(open, close) - low;
        
        // 十字星识别条件
        bool isDoji = bodyRatio < BodyRatioThreshold &&    // 实体很小
                      upperShadow > bodySize &&           // 上影线明显
                      lowerShadow > bodySize &&           // 下影线明显
                      totalRange > 10*Point();            // 排除极小波动
        
        if(isDoji) {
            // 在K线最高价上方绘制标记
            string name = "Doji_" + IntegerToString(i);
            double price = high + 3 * _Point;
            
            ObjectCreate(0, name, OBJ_ARROW_UP, 0, iTime(_Symbol, _Period, i), price);
            ObjectSetInteger(0, name, OBJPROP_COLOR, clrDodgerBlue);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
            ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
            
            // 调整图表显示
            ChartSetInteger(0, CHART_SHOW_DATE_SCALE, true);
            ChartSetInteger(0, CHART_SHOW_PRICE_SCALE, true);
            ChartNavigate(0, CHART_END, -30); // 显示最近30根K线
            ChartRedraw();
            
            // 发送文字通知
            string message = StringFormat(
                "发现十字星形态！\n"
                "品种：%s\n"
                "时间框架：%s\n"
                "时间：%s\n"
                "开盘价：%.5f\n"
                "最高价：%.5f\n"
                "最低价：%.5f\n"
                "收盘价：%.5f\n"
                "上影线高度：%.5f\n"
                "实体高度：%.5f\n"
                "下影线高度：%.5f\n"
                "K线总高度：%.5f",
                _Symbol,
                EnumToString((ENUM_TIMEFRAMES)_Period),
                TimeToString(iTime(_Symbol, _Period, i)),
                open,
                high,
                low,
                close,
                upperShadow,
                bodySize,
                lowerShadow,
                totalRange
            );
            weChat.SendMessage(message);
            
            // 生成并保存截图
            MqlDateTime timeStruct;
            TimeToStruct(TimeCurrent(), timeStruct);
            string fileName = StringFormat("Doji_%s_%s_%04d%02d%02d_%02d%02d%02d.png",
                _Symbol,
                EnumToString((ENUM_TIMEFRAMES)_Period),
                timeStruct.year, timeStruct.mon, timeStruct.day,
                timeStruct.hour, timeStruct.min, timeStruct.sec);
            
            // 确保目录存在
            string dirName = "DojisFinder_Screenshots";
            if(!FileIsExist(dirName, FILE_COMMON)) {
                if(!FolderCreate(dirName, FILE_COMMON)) {
                    Print("创建截图目录失败: ", GetLastError());
                } else {
                    Print("已创建截图目录: ", dirName);
                }
            }
            
            // 完整路径（包含目录）
            string filePath = dirName + "\\" + fileName;
            
            // 截图
            if(!ChartScreenShot(0, filePath, 1920, 1080, ALIGN_RIGHT)) {
                Print("截图失败: ", GetLastError());
            } else {
                Print("截图已生成: ", filePath);
                
                // 发送图片消息
                if(!weChat.SendImage(filePath)) {
                    Print("发送图片到微信失败");
                } else {
                    Print("图片已成功发送到微信");
                }
            }
        }
    }
    
    ChartRedraw();
}