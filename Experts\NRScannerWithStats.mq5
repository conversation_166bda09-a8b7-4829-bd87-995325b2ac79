//+------------------------------------------------------------------+
//|                                         NRScannerWithStats.mq5   |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs
#property indicator_chart_window

// 输入参数
input bool     EnableThresholdFilter = true; // 启用窄幅过滤
input double   Threshold = 6.5;          // 价格范围阈值 (H-L)
input int      BarsToCheck = 5;             // 检查的K线数量
input int      MaxLookback = 10;            // NR检测最大周期 (默认10)
input int      MarkerPosition = 2;          // 标记位置 (0=上,1=下,2=中间)
input color    MinNRColor = clrLightBlue;   // 最小NR值颜色
input color    MaxNRColor = clrDarkRed;     // 最大NR值颜色
input int      PanelCorner = 0;             // 面板位置 (0=左上,1=右上,2=左下,3=右下)
input int      FontSize = 8;               // 面板字体大小

// 全局变量
datetime lastBarTime;
MqlRates rates[];
double rangeValues[]; // 存储K线波动范围
int maxNRValue = 0;   // 当前检测周期中的最大NR值
int minNRValue = 0;   // 当前检测周期中的最小NR值
int nrStats[];        // NR形态统计数组
int totalNRCount = 0; // 检测到的总NR形态数量
string panelObjects[]; // 面板对象名称数组

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始获取第一根K线时间
   datetime currentTime[1];
   if(CopyTime(_Symbol, _Period, 0, 1, currentTime) < 1)
   {
      Print("初始化失败: 无法获取K线时间");
      return(INIT_FAILED);
   }
   
   lastBarTime = currentTime[0];
   
   // 重置统计数据
   ResetStats();
   
   // 创建统计面板
   CreateStatsPanel();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 重置统计数据函数                                                 |
//+------------------------------------------------------------------+
void ResetStats()
{
   // 重置NR值范围
   maxNRValue = 0;
   minNRValue = MaxLookback + 1;
   
   // 重置统计数组
   ArrayResize(nrStats, MaxLookback + 1);
   ArrayInitialize(nrStats, 0);
   
   // 重置总计数
   totalNRCount = 0;
   
   Print("统计数据已重置");
}

//+------------------------------------------------------------------+
//| EA取消初始化函数                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 输出统计概要
   PrintStatsSummary();
   
   // 删除所有标记对象
   ObjectsDeleteAll(0, "NRMarker_");
   // 删除所有面板对象
   DeleteStatsPanel();
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| 输出统计概要函数                                                 |
//+------------------------------------------------------------------+
void PrintStatsSummary()
{
   Print("========== NR形态统计概要 ===========");
   Print("品种: ", _Symbol, " 周期: ", EnumToString(_Period));
   Print("检测到的NR形态总数: ", totalNRCount);
   
   if(totalNRCount > 0)
   {
      Print("各NR形态分布:");
      for(int i = 2; i <= MaxLookback; i++)
      {
         if(i < ArraySize(nrStats) && nrStats[i] > 0)
         {
            double percentage = (double)nrStats[i] / totalNRCount * 100.0;
            PrintFormat("  NR%d: %d次 (%.2f%%)", i, nrStats[i], percentage);
         }
      }
      
      if(maxNRValue > 0 && minNRValue <= MaxLookback)
      {
         PrintFormat("最大NR值: %d, 最小NR值: %d", maxNRValue, minNRValue);
      }
   }
   else
   {
      Print("未检测到任何NR形态");
   }
   
   Print("=====================================");
}

//+------------------------------------------------------------------+
//| 主tick处理函数                                                   |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检测到新K线时执行检查
   if(IsNewBar())
   {
      CheckClosedBars();
      UpdateStatsPanel();
   }
}

//+------------------------------------------------------------------+
//| 检测新K线函数                                                   |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentTime[1];
   // 获取当前K线开盘时间
   if(CopyTime(_Symbol, _Period, 0, 1, currentTime) < 1)
      return false;
   
   // 比较当前K线与上次记录的K线时间
   if(currentTime[0] != lastBarTime)
   {
      lastBarTime = currentTime[0];  // 更新最后记录的时间
      return true;                   // 返回true表示发现新K线
   }
   return false;  // 无新K线
}

//+------------------------------------------------------------------+
//| 窄幅过滤函数 (独立功能)                                         |
//+------------------------------------------------------------------+
bool IsNarrowBar(int barIndex)
{
   // 如果不启用窄幅过滤，直接返回true
   if(!EnableThresholdFilter) return true;
   
   // 检查K线范围是否小于阈值
   return (rangeValues[barIndex] < Threshold);
}

//+------------------------------------------------------------------+
//| 检查已收盘K线并标记                                              |
//+------------------------------------------------------------------+
void CheckClosedBars()
{
   // 计算需要获取的K线数量
   int neededBars = MathMax(BarsToCheck, MaxLookback) + 1;
   
   // 获取所需K线数据
   if(CopyRates(_Symbol, _Period, 0, neededBars, rates) < neededBars)
   {
      Print("错误: 无法获取足够K线数据");
      return;
   }
   
   // 计算每根K线的波动范围
   ArrayResize(rangeValues, neededBars);
   for(int i = 0; i < neededBars; i++)
   {
      rangeValues[i] = rates[i].high - rates[i].low;
   }

   // 重置极值（仅用于当前检测周期）
   int currentMaxNRValue = 0;
   int currentMinNRValue = MaxLookback + 1;
   
   // 先清除旧标记
   ObjectsDeleteAll(0, "NRMarker_");
   
   // 第一遍：检测所有符合条件的NR值
   int nrValues[];  // 存储每个K线的NR值
   ArrayResize(nrValues, neededBars);
   ArrayInitialize(nrValues, 0);
   
   for(int i = 1; i <= BarsToCheck && i < neededBars; i++)
   {
      // 应用窄幅过滤（如果启用）
      if(IsNarrowBar(i))
      {
         nrValues[i] = DetectNRType(i);
         
         // 更新统计信息
         if(nrValues[i] > 0)
         {
            // 确保统计数组足够大
            if(nrValues[i] >= ArraySize(nrStats))
               ArrayResize(nrStats, nrValues[i] + 1);
            
            nrStats[nrValues[i]]++; // 增加该NR值的计数
            totalNRCount++;         // 增加总计数
            
            // 更新当前检测周期的最大和最小NR值
            if(nrValues[i] > currentMaxNRValue) currentMaxNRValue = nrValues[i];
            if(nrValues[i] < currentMinNRValue) currentMinNRValue = nrValues[i];
            
            // 更新全局最大和最小NR值
            if(nrValues[i] > maxNRValue) maxNRValue = nrValues[i];
            if(nrValues[i] < minNRValue) minNRValue = nrValues[i];
         }
      }
   }
   
   // 第二遍：标记所有符合条件的K线
   for(int i = 1; i <= BarsToCheck && i < neededBars; i++)
   {
      if(nrValues[i] > 0)
      {
         // 计算颜色渐变值
         color markerColor = CalculateGradientColor(nrValues[i]);
         
         // 创建标记文本
         string markerText = "NR" + IntegerToString(nrValues[i]);
         
         // 打印信号
         PrintFormat("%s [%s %s]: 柱 %d (时间 %s) 范围=%.5f",
                     markerText,
                     _Symbol, EnumToString(_Period),
                     i, 
                     TimeToString(rates[i].time), 
                     rangeValues[i]);
         
         // 在图表上标记该K线
         MarkBar(rates[i].time, rates[i].high, rates[i].low, markerText, markerColor);
      }
   }
   
   // 打印检测到的极值
   if(currentMaxNRValue > 0 && currentMinNRValue <= MaxLookback)
   {
      PrintFormat("检测周期内: 最大NR值=%d, 最小NR值=%d", currentMaxNRValue, currentMinNRValue);
   }
   
   // 刷新图表显示
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| 检测NR形态函数                                                   |
//+------------------------------------------------------------------+
int DetectNRType(int barIndex)
{
   int maxNR = 0;
   
   // 检查从2到MaxLookback的所有可能NR形态
   for(int lookback = 2; lookback <= MaxLookback; lookback++)
   {
      // 确保有足够的历史K线
      if(barIndex + lookback - 1 >= ArraySize(rangeValues))
         continue;
      
      bool isNarrowest = true;
      double currentRange = rangeValues[barIndex];
      
      // 检查当前K线在最近的lookback根K线中是否是最窄的
      for(int j = barIndex; j < barIndex + lookback; j++)
      {
         // 如果发现更窄的K线，则不是NR形态
         if(rangeValues[j] < currentRange - 0.000001) // 避免浮点误差
         {
            isNarrowest = false;
            break;
         }
      }
      
      // 如果当前K线是最近lookback根K线中最窄的
      if(isNarrowest)
      {
         maxNR = lookback; // 记录最大NR值
      }
   }
   
   return maxNR; // 返回检测到的最大NR值
}

//+------------------------------------------------------------------+
//| 计算渐变颜色函数                                                 |
//+------------------------------------------------------------------+
color CalculateGradientColor(int nrValue)
{
   // 如果没有检测到有效NR值，返回中性色
   if(nrValue < minNRValue || nrValue > maxNRValue || minNRValue == maxNRValue)
      return clrGray;
   
   // 计算颜色插值因子 (0.0-1.0)
   double factor = (double)(nrValue - minNRValue) / (double)(maxNRValue - minNRValue);
   
   // 提取起始颜色分量
   int minR = (MinNRColor >> 16) & 0xFF;
   int minG = (MinNRColor >> 8) & 0xFF;
   int minB = MinNRColor & 0xFF;
   
   // 提取结束颜色分量
   int maxR = (MaxNRColor >> 16) & 0xFF;
   int maxG = (MaxNRColor >> 8) & 0xFF;
   int maxB = MaxNRColor & 0xFF;
   
   // 计算插值颜色
   int r = (int)(minR + factor * (maxR - minR));
   int g = (int)(minG + factor * (maxG - minG));
   int b = (int)(minB + factor * (maxB - minB));
   
   // 确保颜色值在有效范围内
   r = MathMin(MathMax(r, 0), 255);
   g = MathMin(MathMax(g, 0), 255);
   b = MathMin(MathMax(b, 0), 255);
   
   // 返回组合后的颜色
   return (color)((r<<16)|(g<<8)|b);
}

//+------------------------------------------------------------------+
//| 在K线上创建标记                                                  |
//+------------------------------------------------------------------+
void MarkBar(datetime barTime, double high, double low, string text, color markerColor)
{
   string objName = "NRMarker_" + IntegerToString(barTime) + "_" + text;
   double markerPrice = 0;
   
   // 确定标记位置
   switch(MarkerPosition)
   {
      case 0:  // 上方
         markerPrice = high + 5 * _Point;
         break;
      case 1:  // 下方
         markerPrice = low - 5 * _Point;
         break;
      default: // 中间
         markerPrice = (high + low) / 2;
         break;
   }
   
   // 创建箭头对象
   if(ObjectCreate(0, objName, OBJ_ARROW_LEFT_PRICE, 0, barTime, markerPrice))
   {
      ObjectSetInteger(0, objName, OBJPROP_COLOR, markerColor);
      ObjectSetInteger(0, objName, OBJPROP_WIDTH, 2);
      ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_CENTER);
      ObjectSetInteger(0, objName, OBJPROP_BACK, true);
      
      // 创建文本标签
      string textName = "NRText_" + IntegerToString(barTime) + "_" + text;
      if(ObjectCreate(0, textName, OBJ_TEXT, 0, barTime, markerPrice))
      {
         ObjectSetString(0, textName, OBJPROP_TEXT, text);
         ObjectSetInteger(0, textName, OBJPROP_COLOR, markerColor);
         ObjectSetInteger(0, textName, OBJPROP_ANCHOR, ANCHOR_LOWER);
         ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, 10);
         ObjectSetInteger(0, textName, OBJPROP_BACK, true);
      }
   }
}

//+------------------------------------------------------------------+
//| 创建统计面板                                                     |
//+------------------------------------------------------------------+
void CreateStatsPanel()
{
   // 删除旧面板
   DeleteStatsPanel();
   
   // 面板位置参数
   int x = 10;
   int y = 20;
   int width = 150;
   int lineHeight = 20;
   
   // 根据面板角落位置调整坐标
   long chartWidth = (long)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
   long chartHeight = (long)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
   
   if(PanelCorner == 1 || PanelCorner == 3) // 右上或右下
      x = (int)chartWidth - width - 10;
   if(PanelCorner == 2 || PanelCorner == 3) // 左下或右下
      y = (int)chartHeight - 200;
   
   // 创建面板背景
   string bgName = "NRPanel_BG";
   if(ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, x - 5);
      ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, y - 5);
      ObjectSetInteger(0, bgName, OBJPROP_XSIZE, width);
      ObjectSetInteger(0, bgName, OBJPROP_YSIZE, 300);
      ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, clrWhite);
      ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
      ObjectSetInteger(0, bgName, OBJPROP_BORDER_COLOR, clrGray);
      ObjectSetInteger(0, bgName, OBJPROP_BACK, false);
      ArrayPush(panelObjects, bgName);
   }
   
   // 面板标题
   string titleName = "NRPanel_Title";
   if(ObjectCreate(0, titleName, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, titleName, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, titleName, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, titleName, OBJPROP_TEXT, "NR形态统计");
      ObjectSetInteger(0, titleName, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, titleName, OBJPROP_FONTSIZE, FontSize + 2);
      ArrayPush(panelObjects, titleName);
   }
   y += lineHeight;
   
   // 列标题
   string header1 = "NRPanel_Header1";
   if(ObjectCreate(0, header1, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, header1, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, header1, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, header1, OBJPROP_TEXT, "NR值");
      ObjectSetInteger(0, header1, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, header1, OBJPROP_FONTSIZE, FontSize);
      ArrayPush(panelObjects, header1);
   }
   
   string header2 = "NRPanel_Header2";
   if(ObjectCreate(0, header2, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, header2, OBJPROP_XDISTANCE, x + 70);
      ObjectSetInteger(0, header2, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, header2, OBJPROP_TEXT, "出现次数");
      ObjectSetInteger(0, header2, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, header2, OBJPROP_FONTSIZE, FontSize);
      ArrayPush(panelObjects, header2);
   }
   y += lineHeight;
   
   // 创建分隔线
   string sepName = "NRPanel_Sep";
   if(ObjectCreate(0, sepName, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, sepName, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, sepName, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, sepName, OBJPROP_TEXT, "-------------------");
      ObjectSetInteger(0, sepName, OBJPROP_COLOR, clrGray);
      ObjectSetInteger(0, sepName, OBJPROP_FONTSIZE, FontSize);
      ArrayPush(panelObjects, sepName);
   }
   y += lineHeight;
   
   // 创建统计行（将在更新时填充内容）
   for(int i = 2; i <= MaxLookback; i++)
   {
      string labelName = "NRPanel_Label_" + IntegerToString(i);
      string valueName = "NRPanel_Value_" + IntegerToString(i);
      
      // NR值标签
      if(ObjectCreate(0, labelName, OBJ_LABEL, 0, 0, 0))
      {
         ObjectSetInteger(0, labelName, OBJPROP_XDISTANCE, x);
         ObjectSetInteger(0, labelName, OBJPROP_YDISTANCE, y);
         ObjectSetString(0, labelName, OBJPROP_TEXT, "NR" + IntegerToString(i));
         ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrBlack);
         ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, FontSize);
         ObjectSetInteger(0, labelName, OBJPROP_HIDDEN, true);
         ArrayPush(panelObjects, labelName);
      }
      
      // NR值计数
      if(ObjectCreate(0, valueName, OBJ_LABEL, 0, 0, 0))
      {
         ObjectSetInteger(0, valueName, OBJPROP_XDISTANCE, x + 70);
         ObjectSetInteger(0, valueName, OBJPROP_YDISTANCE, y);
         ObjectSetString(0, valueName, OBJPROP_TEXT, "0");
         ObjectSetInteger(0, valueName, OBJPROP_COLOR, clrBlack);
         ObjectSetInteger(0, valueName, OBJPROP_FONTSIZE, FontSize);
         ObjectSetInteger(0, valueName, OBJPROP_HIDDEN, true);
         ArrayPush(panelObjects, valueName);
      }
      
      y += lineHeight;
   }
   
   // 总计行
   string totalLabel = "NRPanel_TotalLabel";
   if(ObjectCreate(0, totalLabel, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, totalLabel, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, totalLabel, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, totalLabel, OBJPROP_TEXT, "总计:");
      ObjectSetInteger(0, totalLabel, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, totalLabel, OBJPROP_FONTSIZE, FontSize);
      ArrayPush(panelObjects, totalLabel);
   }
   
   string totalValue = "NRPanel_TotalValue";
   if(ObjectCreate(0, totalValue, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, totalValue, OBJPROP_XDISTANCE, x + 70);
      ObjectSetInteger(0, totalValue, OBJPROP_YDISTANCE, y);
      ObjectSetString(0, totalValue, OBJPROP_TEXT, "0");
      ObjectSetInteger(0, totalValue, OBJPROP_COLOR, clrBlack);
      ObjectSetInteger(0, totalValue, OBJPROP_FONTSIZE, FontSize);
      ArrayPush(panelObjects, totalValue);
   }
}

//+------------------------------------------------------------------+
//| 删除统计面板                                                     |
//+------------------------------------------------------------------+
void DeleteStatsPanel()
{
   for(int i = 0; i < ArraySize(panelObjects); i++)
   {
      ObjectDelete(0, panelObjects[i]);
   }
   ArrayResize(panelObjects, 0);
}

//+------------------------------------------------------------------+
//| 更新统计面板                                                     |
//+------------------------------------------------------------------+
void UpdateStatsPanel()
{
   int total = 0;
   
   // 更新统计行
   for(int i = 2; i <= MaxLookback; i++)
   {
      string labelName = "NRPanel_Label_" + IntegerToString(i);
      string valueName = "NRPanel_Value_" + IntegerToString(i);
      
      // 只显示有统计数据的行
      if(i < ArraySize(nrStats) && nrStats[i] > 0)
      {
         ObjectSetInteger(0, labelName, OBJPROP_HIDDEN, false);
         ObjectSetInteger(0, valueName, OBJPROP_HIDDEN, false);
         ObjectSetString(0, valueName, OBJPROP_TEXT, IntegerToString(nrStats[i]));
         total += nrStats[i];
      }
      else
      {
         ObjectSetInteger(0, labelName, OBJPROP_HIDDEN, true);
         ObjectSetInteger(0, valueName, OBJPROP_HIDDEN, true);
      }
   }
   
   // 更新总计
   string totalValue = "NRPanel_TotalValue";
   ObjectSetString(0, totalValue, OBJPROP_TEXT, IntegerToString(total));
}

//+------------------------------------------------------------------+
//| 向数组添加元素                                                   |
//+------------------------------------------------------------------+
void ArrayPush(string &array[], string value)
{
   int size = ArraySize(array);
   ArrayResize(array, size + 1);
   array[size] = value;
}