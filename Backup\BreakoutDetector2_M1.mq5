#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

// 外部参数
input int ConsolidationBars = 20;     // 整理K线数量
input double BreakThreshold = 0.0002; // 突破阈值
input int ADX_Period = 14;          // ADX周期
input double ADX_Threshold = 25.0;  // ADX趋势阈值
input double RSI_Threshold = 70.0;       // RSI超买超卖阈值
input double ATR_GrowthRate = 20.0;     // ATR小时增长率阈值(%)
input double ATR_Multiplier = 1.2;  // ATR波动倍数
input double VolumeThreshold = 3.0;    // 成交量阈值倍数(300%)
input int ConfirmationBars = 2;       // 价格确认K线数量
input int FastMAPeriod = 50;        // 快速均线周期
input int SlowMAPeriod = 200;       // 慢速均线周期
input int PanelX = 20;               // 面板X坐标
input int PanelY = 20;               // 面板Y坐标
input int PanelSpacing = 30;        // 标签行间距
input color BullColor = clrGold;     // 多头标记颜色
input color BearColor = clrRed;      // 空头标记颜色
input int AlertInterval = 60;        // 警报间隔(秒)

// 全局变量
double consolidationHigh, consolidationLow;
bool isBullBreak = false, isBearBreak = false;
long chartID;
datetime lastAlertTime;
int adxHandle, atrHandle, fastMAHandle, slowMAHandle, rsiHandle, volumeMAHandle;
double adxValues[], diPlus[], diMinus[], atrValues[], fastMA[], slowMA[], rsiValues[], volumeMA[];
string dotNames[];  // 添加圆点管理数组声明
int maTrend; // 均线趋势方向 1:上涨 -1:下跌
double atrGrowthRate, currentVolume; // 新增全局变量

int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();
   
   // 初始化指标句柄
   adxHandle = iADX(_Symbol, PERIOD_M1, ADX_Period);
   atrHandle = iATR(_Symbol, PERIOD_H1, 14); // 改为小时周期
   rsiHandle = iRSI(_Symbol, PERIOD_M1, 3, PRICE_CLOSE);
   // 移除volumeMA指标初始化
   fastMAHandle = iMA(_Symbol, PERIOD_M1, FastMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, PERIOD_M1, SlowMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   
   // 设置数组为时间序列
   ArraySetAsSeries(adxValues, true);
   ArraySetAsSeries(diPlus, true);
   ArraySetAsSeries(diMinus, true);
   ArraySetAsSeries(atrValues, true);
   ArraySetAsSeries(fastMA, true);
   ArraySetAsSeries(slowMA, true);
   ArraySetAsSeries(rsiValues, true);
   ArraySetAsSeries(volumeMA, true);
   
   return(INIT_SUCCEEDED);
}

void OnTick()
{
   // 计算最近N根K线的高低点
   CalculateConsolidationLevels();
   
   // 检测突破条件
   DetectBreakouts();
   
   // 更新信息面板
   UpdateInfoPanel();
}

void CalculateConsolidationLevels()
{
   consolidationHigh = -DBL_MAX;
   consolidationLow = DBL_MAX;
   
   for(int i=0; i<ConsolidationBars; i++)
   {
      consolidationHigh = MathMax(consolidationHigh, iHigh(_Symbol,PERIOD_M1,i));
      consolidationLow = MathMin(consolidationLow, iLow(_Symbol,PERIOD_M1,i));
   }
}

void DetectBreakouts()
{
   currentVolume = (double)iVolume(_Symbol, PERIOD_M1, 0);
   double prevVolume = (double)iVolume(_Symbol, PERIOD_M1, 1);
   
   // 统一获取指标数据
   if(!FetchIndicatorData()) return;
   
   // 计算均线趋势
   maTrend = (fastMA[0] > slowMA[0] && fastMA[1] > slowMA[1]) ? 1 : 
             (fastMA[0] < slowMA[0] && fastMA[1] < slowMA[1]) ? -1 : 0;

   double currentHigh = iHigh(_Symbol,PERIOD_M1,0);
   double currentLow = iLow(_Symbol,PERIOD_M1,0);
   
   // 波动性过滤条件
   // 计算小时ATR增长率
   atrGrowthRate = (atrValues[0] - atrValues[1])/atrValues[1]*100;
   
   // 获取RSI值
   CopyBuffer(rsiHandle, 0, 0, 2, rsiValues);
   // 计算前10次tick的成交量平均值
double volumeSum = 0.0;
for(int i=1; i<=10; i++){
   volumeSum += (double)iVolume(_Symbol, PERIOD_M1, i);
}
double avgVolume = volumeSum / 10.0;
   
   // 三重过滤条件
   bool volatilityFilter = (atrGrowthRate > ATR_GrowthRate) && 
                          (currentVolume > avgVolume * VolumeThreshold) &&
                          (rsiValues[0] > RSI_Threshold);
   
   // 上破检测
   if( (currentHigh > consolidationHigh + BreakThreshold) 
   && volatilityFilter
   && IsPriceConfirmed(ConfirmationBars, true) )
   {
      isBullBreak = true;
      DrawBreakoutArrow(241, BullColor);
      LogSignal(StringFormat("Bullish Breakout ADX=%.1f MA=%.5f", adxValues[0], fastMA[0]));
   }
   
   // 下破检测
   if( (currentLow < consolidationLow - BreakThreshold) 
   && volatilityFilter
   && IsPriceConfirmed(ConfirmationBars, false) )
   {
      isBearBreak = true;
      DrawBreakoutArrow(242, BearColor);
      LogSignal(StringFormat("Bearish Breakout ADX=%.1f MA=%.5f", adxValues[0], fastMA[0]));
   }
}

void DrawBreakoutArrow(int arrowCode, color clr)
{
   string objName = "BreakArrow_"+TimeToString(TimeCurrent());
   ObjectCreate(chartID, objName, OBJ_ARROW, 0, TimeCurrent(), iClose(_Symbol,PERIOD_M1,0));
   ObjectSetInteger(chartID, objName, OBJPROP_ARROWCODE, arrowCode);
   ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clr);
   ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 2);
}

// 统一指标数据获取
bool FetchIndicatorData()
{
   return CopyBuffer(adxHandle, 0, 0, 3, adxValues) &&
          CopyBuffer(adxHandle, 1, 0, 3, diPlus) &&
          CopyBuffer(adxHandle, 2, 0, 3, diMinus) &&
          CopyBuffer(atrHandle, 0, 0, 3, atrValues) &&
          CopyBuffer(fastMAHandle, 0, 0, 2, fastMA) &&
          CopyBuffer(slowMAHandle, 0, 0, 2, slowMA);
}

// 价格确认函数
bool IsPriceConfirmed(int barsToCheck, bool isBull)
{
   for(int i=0; i<ConfirmationBars; i++)
   {
      if(isBull && iClose(_Symbol, PERIOD_M1, i) < consolidationHigh)
         return false;
      if(!isBull && iClose(_Symbol, PERIOD_M1, i) > consolidationLow)
         return false;
   }
   return true;
}

void LogSignal(string message)
{
   if((TimeCurrent() - lastAlertTime) > AlertInterval)
   {
      Print(TimeToString(TimeCurrent())," ",message, " High:",DoubleToString(consolidationHigh,_Digits), " Low:",DoubleToString(consolidationLow,_Digits));
      Alert(message);
      lastAlertTime = TimeCurrent();
   }
}

// 创建信息面板
void CreateInfoPanel()
{
   // 背景矩形
   ObjectCreate(chartID, "BreakPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_YSIZE, 100 + PanelSpacing*5);

   // 使用数组循环创建标签
   string labels[5] = {"HighLabel", "LowLabel", "ADXLabel", "MAStatus", "StatusLabel"};
   string texts[5] = {"当前区间高:", "当前区间低:", "ADX值:", "均线趋势:", "突破状态:"};
   
   for(int i=0; i<5; i++)
   {
      CreatePanelLabel(labels[i], texts[i], 10 + PanelSpacing*i);
   }
}

void CreatePanelLabel(string name, string text, int yOffset)
{
   if(!ObjectCreate(chartID, name, OBJ_LABEL, 0, 0, 0))
      Print("创建标签失败: ", name);
   
   ObjectSetInteger(chartID, name, OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, name, OBJPROP_YDISTANCE, PanelY+yOffset);
   ObjectSetInteger(chartID, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, name, OBJPROP_FONTSIZE, 8);
   ObjectSetString(chartID, name, OBJPROP_TEXT, text);
}

void UpdateInfoPanel()
{
   ObjectSetString(chartID, "HighLabel", OBJPROP_TEXT, "当前区间高: "+DoubleToString(consolidationHigh,_Digits));
   ObjectSetString(chartID, "LowLabel", OBJPROP_TEXT, "当前区间低: "+DoubleToString(consolidationLow,_Digits));
   ObjectSetString(chartID, "ADXLabel", OBJPROP_TEXT, "ADX值: "+DoubleToString(adxValues[0],1));
   
   string maStatus = "均线趋势: ";
   switch((int)maTrend)
   {
      case 1: maStatus += "▲多头"; break;
      case -1: maStatus += "▼空头"; break;
      default: maStatus += "-震荡-";
   }
   ObjectSetString(chartID, "MAStatus", OBJPROP_TEXT, maStatus);
   
   string statusText = "突破状态: ";
   if(isBullBreak) statusText += "▲突破高点";
   else if(isBearBreak) statusText += "▼跌破低点";
   else statusText += "等待信号";
   
   ObjectSetString(chartID, "StatusLabel", OBJPROP_TEXT, statusText);
   ObjectSetString(chartID, "ADXLabel", OBJPROP_TEXT, "ATR增长: "+DoubleToString(atrGrowthRate,1)+"%  RSI: "+DoubleToString(rsiValues[0],1)+" 成交量: "+DoubleToString(currentVolume,0));
}

void OnDeinit(const int reason)
{
   ObjectDelete(chartID, "BreakPanelBG");
   ObjectDelete(chartID, "HighLabel");
   ObjectDelete(chartID, "LowLabel");
   ObjectDelete(chartID, "StatusLabel");
}