//+------------------------------------------------------------------+
//|                                                      NR4_EA.mq5 |
//|                                  模块化NR4突破策略EA               |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "2.00"
#property description "模块化NR4突破策略EA，支持动态止损止盈和移动止损"

// 包含所有模块
#include <NR4_EA/Common.mqh>
#include <NR4_EA/Parameters.mqh>
#include <NR4_EA/SignalDetector.mqh>
#include <NR4_EA/RiskManager.mqh>
#include <NR4_EA/TradeManager.mqh>
#include <NR4_EA/TrailingStop.mqh>
#include <NR4_EA/DisplayManager.mqh>
#include <NR4_EA/Utils.mqh>

// 全局对象实例
CTrade g_trade;
CPositionInfo g_positionInfo;
COrderInfo g_orderInfo;

// 全局变量
string g_tradeComment = __FILE__;
datetime g_lastTradeTime = 0;
string g_rectangleNames[];
int g_rectangleCount = 0;
TrailingStopData g_trailingData[];
int g_trailingDataCount = 0;

// 全局参数变量定义（供extern声明使用）
int g_inputMagicNum;
ENUM_TIMEFRAMES g_inputPeriod;
int g_inputNarrowRangeCount;
double g_inputLotSize;
ENUM_SL_TP_MODE g_inputSLTPMode;
double g_inputStopLoss;
double g_inputTakeProfit;
double g_inputATRMultiplierSL;
double g_inputATRMultiplierTP;
int g_inputATRPeriod;
double g_inputRiskRewardRatio;
bool g_inputUseVolatilityFilter;
double g_inputVolatilityThreshold;
int g_inputMinBarsSinceLastTrade;
bool g_inputEnableTrailingStop;
double g_inputTrailingStart;
double g_inputTrailingStep;
bool g_inputShowRectangles;
color g_inputRectangleColor;
int g_inputMaxRectangles;

// 模块对象
CSignalDetector* g_signalDetector;
CRiskManager* g_riskManager;
CTradeManager* g_tradeManager;
CTrailingStopManager* g_trailingStopManager;
CDisplayManager* g_displayManager;

//+------------------------------------------------------------------+
//| 输入参数定义                                                      |
//+------------------------------------------------------------------+
input group "基本参数";
input int inputMagicNum = 888888;
input ENUM_TIMEFRAMES PERIOD = PERIOD_H4;
input int inputNarrowRangeCount = 4;
input double inputLotSize = 1;

input group "止损止盈设置";
input ENUM_SL_TP_MODE inputSLTPMode = SL_TP_ATR;
input double inputStopLoss = 100;
input double inputTakeProfit = 100;
input double inputATRMultiplierSL = 2.0;
input double inputATRMultiplierTP = 3.0;
input int inputATRPeriod = 14;
input double inputRiskRewardRatio = 1.5;
input bool inputUseVolatilityFilter = true;
input double inputVolatilityThreshold = 1.5;
input int inputMinBarsSinceLastTrade = 5;

input group "移动止损参数";
input bool inputEnableTrailingStop = true;
input double inputTrailingStart = 50;
input double inputTrailingStep = 20;

input group "显示参数";
input bool inputShowRectangles = true;
input color inputRectangleColor = clrMediumAquamarine;
input int inputMaxRectangles = 50;

//+------------------------------------------------------------------+
//| EA初始化函数                                                      |
//+------------------------------------------------------------------+
int OnInit() {
    // 初始化参数
    InitializeParameters(
        inputMagicNum, PERIOD, inputNarrowRangeCount, inputLotSize,
        inputSLTPMode, inputStopLoss, inputTakeProfit,
        inputATRMultiplierSL, inputATRMultiplierTP, inputATRPeriod,
        inputRiskRewardRatio, inputUseVolatilityFilter, inputVolatilityThreshold,
        inputMinBarsSinceLastTrade, inputEnableTrailingStop,
        inputTrailingStart, inputTrailingStep,
        inputShowRectangles, inputRectangleColor, inputMaxRectangles
    );

    // 创建模块对象
    g_signalDetector = new CSignalDetector(_Symbol, PERIOD, inputNarrowRangeCount);
    g_riskManager = new CRiskManager(_Symbol, PERIOD);
    g_tradeManager = new CTradeManager(_Symbol, inputMagicNum);
    g_trailingStopManager = new CTrailingStopManager(_Symbol, inputMagicNum);
    g_displayManager = new CDisplayManager(_Symbol, PERIOD, inputNarrowRangeCount);

    // 初始化模块
    g_trailingStopManager.Initialize();
    g_displayManager.Initialize();
    g_displayManager.CreateInfoPanel();

    Print("NR4_EA 模块化版本初始化成功");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| EA反初始化函数                                                    |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 清理显示对象
    if(g_displayManager != NULL) {
        g_displayManager.ClearAllRectangles();
        g_displayManager.DeleteInfoPanel();
        delete g_displayManager;
    }

    // 删除模块对象
    if(g_signalDetector != NULL) delete g_signalDetector;
    if(g_riskManager != NULL) delete g_riskManager;
    if(g_tradeManager != NULL) delete g_tradeManager;
    if(g_trailingStopManager != NULL) delete g_trailingStopManager;

    Print("NR4_EA 模块化版本已清理");
}

//+------------------------------------------------------------------+
//| EA主要逻辑函数                                                    |
//+------------------------------------------------------------------+
void OnTick() {
    // 处理移动止损（每个tick都检查）
    g_trailingStopManager.ProcessTrailingStop();

    // 检查是否为新K线
    if(!g_signalDetector.IsNewBar()) return;

    // 更新信息面板
    g_displayManager.UpdateInfoPanel();

    // 检测信号
    int signal = g_signalDetector.DetectSignal();

    if(signal == 1) {
        // 检查开单频率限制
        if(!g_riskManager.CanPlaceNewTrade()) {
            Print("开单频率限制：距离上次开单时间不足");
            return;
        }

        // 关闭所有现有持仓
        g_tradeManager.CloseAllPositions();

        // 获取信号价格
        double high, low;
        g_signalDetector.GetSignalPrices(high, low);

        // 计算动态止损止盈
        double slDistance, tpDistance;
        g_riskManager.CalculateSLTP(slDistance, tpDistance, high, low);

        // 获取过期时间
        datetime expiration = g_signalDetector.GetExpirationTime();

        // 执行交易
        bool tradeResult = g_tradeManager.ExecuteNR4Trade(high, low, slDistance, tpDistance, expiration);

        if(tradeResult) {
            // 更新上次开单时间
            g_riskManager.UpdateLastTradeTime();

            // 绘制矩形标记
            g_displayManager.DrawNR4Rectangle();

            // 显示交易信号
            // g_displayManager.ShowTradeSignal(high, true, g_signalDetector.GetCurrentBarTime());
            // g_displayManager.ShowTradeSignal(low, false, g_signalDetector.GetCurrentBarTime());

            Print("NR4信号触发，已下单");
        } else {
            Print("NR4信号触发，但下单失败：", g_tradeManager.GetLastTradeResult());
        }
    }
}

//+------------------------------------------------------------------+
//| 模块化NR4_EA - 所有功能已拆分到独立模块中                         |
//| 主要模块：                                                        |
//| - SignalDetector: 信号检测                                       |
//| - RiskManager: 风险管理和动态止损止盈                             |
//| - TradeManager: 交易执行                                         |
//| - TrailingStopManager: 移动止损管理                              |
//| - DisplayManager: 图表显示管理                                   |
//| - Utils: 工具函数                                                |
//+------------------------------------------------------------------+