//+------------------------------------------------------------------+
//|                                                  BB_Width_EA.mq5 |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "ChatGPT/DeepSeek"
#property version   "1.00"
#property description "Bollinger Bands Width Monitor with Info Panel"

//--- 输入参数
input int      BB_Period = 20;           // 布林带周期
input double   BB_Deviation = 2.0;       // 标准差倍数
input int      BB_Shift = 0;             // 指标平移
input ENUM_APPLIED_PRICE APP_Price = PRICE_CLOSE; // 应用价格
input ENUM_TIMEFRAMES BB_TF = PERIOD_CURRENT; // 时间框架

//--- 面板参数
input int      Panel_Corner = 0;         // 面板位置（0-3）
input color    Panel_Text_Color = clrWhite; // 文本颜色
input color    Panel_Back_Color = clrSteelBlue; // 背景颜色
input string   Panel_Font = "Consolas";  // 面板字体

//--- 信号参数
input double   Threshold_Width = 8.0;    // 宽度阈值
input color    Arrow_Color = clrGoldenrod; // 箭头颜色

//--- 布林带数据结构体
struct BBData {
   double upper;    // 上轨
   double middle;   // 中轨
   double lower;    // 下轨
   double width;    // 带宽
   double open;     // 开盘价
   double high;     // 最高价
   double low;      // 最低价
   double close;    // 收盘价
   
   void Calculate(double u, double m, double l, double o, double h, double l_price, double c) {
      upper = u;
      middle = m;
      lower = l;
      width = upper - lower;
      open = o;
      high = h;
      low = l_price;
      close = c;
   }
};

//--- 全局变量
int bbHandle;
BBData currentBB;    // 当前K线数据
BBData prevBB;       // 前一K线数据
BBData prev2BB;      // 前二K线数据
string panelName = "BB_Width_Panel";

//--- 连续状态跟踪
int lastConsecutive = 0;     // 最近一次连续次数
int currentConsecutive = 0;  // 当前连续次数
int maxConsecutive = 0;      // 最长连续次数

double averageConsecutive = 0; // 平均连续次数
int totalCycles = 0;         // 总周期数
datetime lastBarTime = 0;    // K线时间戳

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化连续状态
   lastConsecutive = 0;
   currentConsecutive = 0;
   maxConsecutive = 0;
   lastBarTime = iTime(Symbol(), BB_TF, 0);

   // 创建布林带指标句柄
   bbHandle = iBands(Symbol(), BB_TF, BB_Period, BB_Shift, BB_Deviation, APP_Price);
   if(bbHandle == INVALID_HANDLE) {
      Print("创建布林带指标失败");
      return(INIT_FAILED);
   }
   
   // 创建信息面板
   CreateInfoPanel();
   
   // 设置定时器（1秒更新）
   EventSetTimer(1);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 定时器事件处理函数                                               |
//+------------------------------------------------------------------+
void OnTimer()
{
   // 优先更新数据
   ProcessMarketData();
   
   datetime currentBarTime = iTime(Symbol(), BB_TF, 0);

   // 检测新K线开始
   if(currentBarTime != lastBarTime)
   {
      // 检查前一K线条件
      bool previousConditionMet = (prevBB.width > 0 && prevBB.width < Threshold_Width);

      if(previousConditionMet) {
         currentConsecutive++;
         maxConsecutive = MathMax(currentConsecutive, maxConsecutive);
         
      } else {
         // 保存最后一次有效连续次数
         if(currentConsecutive > 0) {
            lastConsecutive = currentConsecutive;
            totalCycles++;
            averageConsecutive = (averageConsecutive*(totalCycles-1) + currentConsecutive)/totalCycles;
         }
         currentConsecutive = 0;
      }
      lastBarTime = currentBarTime;
   }

   UpdateInfoPanel();
   
   // 清除旧箭头
   ObjectsDeleteAll(0, 0, OBJ_ARROW_CHECK);
   
   // 绘制信号箭头（基于前一K线）
   if(ValidateConditions()) {
      datetime time = iTime(Symbol(), BB_TF, 1);
      CreateSignalArrow(time, prevBB.lower);
   }
}

//+------------------------------------------------------------------+
//| 创建信号箭头                                                    |
//+------------------------------------------------------------------+
void CreateSignalArrow(datetime time, double price)
{
   string arrowName = "BB_Width_Arrow_" + TimeToString(time);
   ObjectCreate(0, arrowName, OBJ_ARROW_CHECK, 0, time, price);
   ObjectSetInteger(0, arrowName, OBJPROP_COLOR, Arrow_Color);
   ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, arrowName, OBJPROP_ANCHOR, ANCHOR_CENTER);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                     |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 面板背景
   ObjectCreate(0, panelName+"_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_YDISTANCE, 40);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_XSIZE, 480);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_YSIZE, 550);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_BGCOLOR, Panel_Back_Color);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_CORNER, Panel_Corner);

   // 标题文本
   CreateLabel("Title", "Bollinger Bands Monitor", 30, 30, Panel_Text_Color);

   // 各时间段布林带数据
   CreateBBLabels("Prev2", "前二K线:", 80, prev2BB);
   CreateBBLabels("Prev", "前一K线:", 190, prevBB);
   CreateBBLabels("", "当前K线:", 300, currentBB);

   // 连续次数显示区域
   int startY = 400;
   CreateLabel("ThresholdLabel", "当前阈值: ", 30, startY+30, Panel_Text_Color);
   CreateLabel("ThresholdValue", DoubleToString(Threshold_Width,_Digits), 140, startY+30, Panel_Text_Color);
   
   CreateLabel("CurrentConsecLabel", "当前连续: ", 30, startY+60, Panel_Text_Color);
   CreateLabel("CurrentConsecValue", "0", 140, startY+60, Panel_Text_Color);
   
   CreateLabel("LastConsecLabel", "最近连续: ", 30, startY+90, Panel_Text_Color);
   CreateLabel("LastConsecValue", "0", 140, startY+90, Panel_Text_Color);
   
   CreateLabel("MaxConsecLabel", "最长连续: ", 30, startY+120, Panel_Text_Color);
   CreateLabel("MaxConsecValue", "0", 140, startY+120, Panel_Text_Color);

   CreateLabel("AvgConsecLabel", "平均连续: ", 30, startY+150, Panel_Text_Color);
   CreateLabel("AvgConsecValue", "0", 140, startY+150, Panel_Text_Color);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                     |
//+------------------------------------------------------------------+
void CreateLabel(const string name, const string text, int x, int y, color clr)
{
   ObjectCreate(0, panelName+"_"+name, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, panelName+"_"+name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_CORNER, Panel_Corner);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_FONTSIZE, 8);
   ObjectSetString(0, panelName+"_"+name, OBJPROP_FONT, Panel_Font);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                     |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   // 更新布林带数据
   UpdateBBValues("", currentBB);
   UpdateBBValues("Prev", prevBB);
   UpdateBBValues("Prev2", prev2BB);
   
   // 更新阈值状态颜色
   color statusColor = (prevBB.width < Threshold_Width) ? clrLime : clrRed;
   ObjectSetInteger(0, panelName+"_ThresholdValue", OBJPROP_COLOR, statusColor);
   
   // 更新连续次数显示
   ObjectSetString(0, panelName+"_CurrentConsecValue", OBJPROP_TEXT, IntegerToString(currentConsecutive));
   ObjectSetString(0, panelName+"_LastConsecValue", OBJPROP_TEXT, IntegerToString(lastConsecutive));
   ObjectSetString(0, panelName+"_MaxConsecValue", OBJPROP_TEXT, IntegerToString(maxConsecutive));
   ObjectSetString(0, panelName+"_AvgConsecValue", OBJPROP_TEXT, DoubleToString(averageConsecutive,1));
   
   // 设置颜色
   ObjectSetInteger(0, panelName+"_CurrentConsecValue", OBJPROP_COLOR, (currentConsecutive>0)?clrLime:clrSilver);
   ObjectSetInteger(0, panelName+"_LastConsecValue", OBJPROP_COLOR, (lastConsecutive>0)?clrGold:clrSilver);
   ObjectSetInteger(0, panelName+"_MaxConsecValue", OBJPROP_COLOR, clrRed);
}

//+------------------------------------------------------------------+
//| 更新布林带数值                                                   |
//+------------------------------------------------------------------+
void UpdateBBValues(string prefix, BBData &data)
{
   ObjectSetString(0, panelName+"_"+prefix+"UpperValue", OBJPROP_TEXT, DoubleToString(data.upper, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"MiddleValue", OBJPROP_TEXT, DoubleToString(data.middle, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"LowerValue", OBJPROP_TEXT, DoubleToString(data.lower, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"WidthValue", OBJPROP_TEXT, DoubleToString(data.width, _Digits));
   
   // 更新OHLC
   ObjectSetString(0, panelName+"_"+prefix+"OpenValue", OBJPROP_TEXT, "O:"+DoubleToString(data.open, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"HighValue", OBJPROP_TEXT, "H:"+DoubleToString(data.high, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"LowValue", OBJPROP_TEXT, "L:"+DoubleToString(data.low, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"CloseValue", OBJPROP_TEXT, "C:"+DoubleToString(data.close, _Digits));
}

//+------------------------------------------------------------------+
//| 数据获取函数                                                     |
//+------------------------------------------------------------------+
bool GetBollingerBandsData(double &upper[], double &middle[], double &lower[]) {
   if(CopyBuffer(bbHandle, 1, 0, 3, upper) < 0 ||
      CopyBuffer(bbHandle, 0, 0, 3, middle) < 0 ||
      CopyBuffer(bbHandle, 2, 0, 3, lower) < 0) {
      Print("获取布林带数据失败");
      return false;
   }
   return true;
}

void ProcessMarketData()
{
   double upper[3], middle[3], lower[3];
   double open[3], high[3], low[3], close[3];

   if(!GetBollingerBandsData(upper, middle, lower)) return;

   CopyOHLC(open, high, low, close);

   UpdateBBStructure(currentBB, upper[2], middle[2], lower[2], open[2], high[2], low[2], close[2]);
   UpdateBBStructure(prevBB, upper[1], middle[1], lower[1], open[1], high[1], low[1], close[1]);
   UpdateBBStructure(prev2BB, upper[0], middle[0], lower[0], open[0], high[0], low[0], close[0]);
}

//+------------------------------------------------------------------+
//| 辅助函数                                                        |
//+------------------------------------------------------------------+
void UpdateBBStructure(BBData &bb, double u, double m, double l, 
                      double o, double h, double l_price, double c) {
   if(u != EMPTY_VALUE && l != EMPTY_VALUE)
      bb.Calculate(u, m, l, o, h, l_price, c);
}

void CopyOHLC(double &open[], double &high[], double &low[], double &close[]) {
   CopyOpen(Symbol(), BB_TF, 0, 3, open);
   CopyHigh(Symbol(), BB_TF, 0, 3, high);
   CopyLow(Symbol(), BB_TF, 0, 3, low);
   CopyClose(Symbol(), BB_TF, 0, 3, close);
}

bool ValidateConditions() {
   return prevBB.width > 0 && prevBB.width < Threshold_Width;
}

//+------------------------------------------------------------------+
//| 面板标签创建辅助                                                 |
//+------------------------------------------------------------------+
void CreateBBLabels(string prefix, string title, int baseY, BBData &data)
{
   CreateLabel(prefix+"BarLabel", title, 30, baseY, Panel_Text_Color);
   
   int labelY = baseY + 25;
   CreateLabel(prefix+"UpperLabel", "上轨: ", 30, labelY, Panel_Text_Color);
   CreateLabel(prefix+"UpperValue", "N/A", 90, labelY, Panel_Text_Color);
   
   CreateLabel(prefix+"MiddleLabel", "中轨: ", 30, labelY+25, Panel_Text_Color);
   CreateLabel(prefix+"MiddleValue", "N/A", 90, labelY+25, Panel_Text_Color);
   
   CreateLabel(prefix+"LowerLabel", "下轨: ", 30, labelY+50, Panel_Text_Color);
   CreateLabel(prefix+"LowerValue", "N/A", 90, labelY+50, Panel_Text_Color);
   
   CreateLabel(prefix+"WidthLabel", "宽度: ", 220, labelY, Panel_Text_Color);
   CreateLabel(prefix+"WidthValue", "N/A", 280, labelY, Panel_Text_Color);
   
   // OHLC标签
   CreateLabel(prefix+"OpenValue", "O:"+DoubleToString(data.open,_Digits), 360, labelY+0, Panel_Text_Color);
   CreateLabel(prefix+"HighValue", "H:"+DoubleToString(data.high,_Digits), 360, labelY+20, Panel_Text_Color);
   CreateLabel(prefix+"LowValue", "L:"+DoubleToString(data.low,_Digits), 360, labelY+40, Panel_Text_Color);
   CreateLabel(prefix+"CloseValue", "C:"+DoubleToString(data.close,_Digits), 360, labelY+60, Panel_Text_Color);
}
//+------------------------------------------------------------------+