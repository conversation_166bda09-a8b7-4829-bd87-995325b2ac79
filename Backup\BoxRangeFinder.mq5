//+------------------------------------------------------------------+
//|                                           BoxRangeFinder.mq5 |
//|                        Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "横盘形态检测EA（基于布林带和价格波动分析）"
#include <Arrays\ArrayObj.mqh>

//--- 输入参数
input int      ScanPeriod        = 20;     // 扫描周期（K线数量）
input double   MaxRangePercent   = 1.5;    // 最大波动范围（百分比）
input int      MinBarCount       = 10;     // 最小K线数量
input double   BBPeriod          = 20;     // 布林带周期
input double   BBDeviation       = 2.0;    // 布林带标准差
input double   BBWidthThreshold  = 0.008;  // 布林带宽度阈值
input color    RangeColor        = clrDodgerBlue;  // 横盘区域颜色
input int      Transparency      = 60;     // 透明度(0-100)
input int      MaxRanges         = 50;     // 最大显示区域数

//--- 全局变量
int bbHandle;
double bbUpper[], bbMiddle[], bbLower[];
datetime lastBarTime;
CArrayObj RangeList;

//+------------------------------------------------------------------+
//| 横盘区域对象类                                                    |
//+------------------------------------------------------------------+
class RangeObject : public CObject
{
public:
    datetime    startTime;
    datetime    endTime;
    double      upperPrice;
    double      lowerPrice;
    double      avgPrice;
    string      objName;
    bool        isActive;
    int         barCount;
    
    RangeObject(datetime start, double high, double low)
    {
        startTime = start;
        endTime = start;
        upperPrice = high;
        lowerPrice = low;
        avgPrice = (high + low) / 2;
        isActive = true;
        barCount = 1;
        objName = "Range_" + TimeToString(start) + "_" + IntegerToString(ChartID());
    }
    
    void Update(datetime time, double high, double low)
    {
        endTime = time;
        upperPrice = MathMax(upperPrice, high);
        lowerPrice = MathMin(lowerPrice, low);
        avgPrice = (upperPrice + lowerPrice) / 2;
        barCount++;
        UpdateVisual();
    }
    
    bool IsInRange(double price, double maxRange)
    {
        double range = upperPrice - lowerPrice;
        double allowedRange = avgPrice * maxRange / 100;
        return (range <= allowedRange) && (price >= lowerPrice) && (price <= upperPrice);
    }
    
    void CreateVisual()
    {
        if(ObjectFind(0, objName) < 0)
        {
            ObjectCreate(0, objName, OBJ_RECTANGLE, 0, startTime, upperPrice, endTime, lowerPrice);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, ColorToARGB(RangeColor, (uchar)(255*(100-Transparency)/100)));
            ObjectSetInteger(0, objName, OBJPROP_BACK, true);
            ObjectSetInteger(0, objName, OBJPROP_FILL, false);
            
            // 添加标签
            string labelName = objName + "_Label";
            ObjectCreate(0, labelName, OBJ_TEXT, 0, endTime, upperPrice);
            ObjectSetString(0, labelName, OBJPROP_TEXT, 
                StringFormat("区间: %.4f\n持续: %d根", upperPrice - lowerPrice, barCount));
            ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_RIGHT_UPPER);
        }
    }
    
    void UpdateVisual()
    {
        if(ObjectFind(0, objName) >= 0)
        {
            ObjectSetInteger(0, objName, OBJPROP_TIME, 1, endTime);
            ObjectSetDouble(0, objName, OBJPROP_PRICE, 0, upperPrice);
            ObjectSetDouble(0, objName, OBJPROP_PRICE, 1, lowerPrice);
            
            string labelName = objName + "_Label";
            if(ObjectFind(0, labelName) >= 0)
            {
                ObjectSetInteger(0, labelName, OBJPROP_TIME, 0, endTime);
                ObjectSetDouble(0, labelName, OBJPROP_PRICE, 0, upperPrice);
                ObjectSetString(0, labelName, OBJPROP_TEXT, 
                    StringFormat("区间: %.4f\n持续: %d根", upperPrice - lowerPrice, barCount));
            }
        }
    }
    
    void PrintInfo()
    {
        Print(StringFormat("横盘区域: 开始=%s, 结束=%s, 上限=%.4f, 下限=%.4f, 持续=%d根",
            TimeToString(startTime), TimeToString(endTime),
            upperPrice, lowerPrice, barCount));
    }
};

//+------------------------------------------------------------------+
//| 检测横盘形态                                                      |
//+------------------------------------------------------------------+
void CheckRangeFormation()
{
    if(CopyBuffer(bbHandle, 1, 1, 3, bbUpper) <= 0 ||
       CopyBuffer(bbHandle, 0, 1, 3, bbMiddle) <= 0 ||
       CopyBuffer(bbHandle, 2, 1, 3, bbLower) <= 0) return;
    
    datetime currentTime = iTime(_Symbol, _Period, 1);
    double currentClose = iClose(_Symbol, _Period, 1);
    double currentHigh = iHigh(_Symbol, _Period, 1);
    double currentLow = iLow(_Symbol, _Period, 1);
    
    // 检查布林带宽度和价格是否在横盘条件内
    bool isRangeNow = (bbUpper[0] - bbLower[0]) / bbMiddle[0] < BBWidthThreshold &&
                      currentClose > bbLower[0] && currentClose < bbUpper[0];
    
    bool activeRangeFound = false;
    
    // 更新现有横盘区域
    for(int i = RangeList.Total() - 1; i >= 0; i--)
    {
        RangeObject* range = RangeList.At(i);
        if(range == NULL) continue;
        
        if(range.isActive)
        {
            if(isRangeNow && range.IsInRange(currentClose, MaxRangePercent))
            {
                range.Update(currentTime, currentHigh, currentLow);
                activeRangeFound = true;
                
                if(range.barCount >= MinBarCount && ObjectFind(0, range.objName) < 0)
                {
                    range.CreateVisual();
                }
            }
            else
            {
                range.isActive = false;
                if(range.barCount < MinBarCount)
                {
                    ObjectDelete(0, range.objName);
                    ObjectDelete(0, range.objName + "_Label");
                    RangeList.Delete(i);
                    delete range;
                }
                else
                {
                    range.PrintInfo();
                }
            }
        }
    }
    
    // 创建新的横盘区域
    if(isRangeNow && !activeRangeFound)
    {
        RangeObject* newRange = new RangeObject(currentTime, currentHigh, currentLow);
        if(newRange != NULL) RangeList.Add(newRange);
        
        // 清理过多的区域
        while(RangeList.Total() > MaxRanges)
        {
            RangeObject* oldRange = RangeList.At(0);
            if(oldRange != NULL)
            {
                ObjectDelete(0, oldRange.objName);
                ObjectDelete(0, oldRange.objName + "_Label");
                delete oldRange;
            }
            RangeList.Delete(0);
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化函数                                                        |
//+------------------------------------------------------------------+
int OnInit()
{
    bbHandle = iBands(_Symbol, _Period, BBPeriod, 0, BBDeviation, PRICE_CLOSE);
    if(bbHandle == INVALID_HANDLE) return INIT_FAILED;
    
    ArraySetAsSeries(bbUpper, true);
    ArraySetAsSeries(bbMiddle, true);
    ArraySetAsSeries(bbLower, true);
    
    // 清理图表对象
    ObjectsDeleteAll(0, "Range_");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 主循环函数                                                        |
//+------------------------------------------------------------------+
void OnTick()
{
    datetime currentBarTime[];
    if(CopyTime(_Symbol, _Period, 0, 1, currentBarTime) != 1) return;
    
    if(currentBarTime[0] == lastBarTime) return;
    
    lastBarTime = currentBarTime[0];
    CheckRangeFormation();
}

//+------------------------------------------------------------------+
//| 清理函数                                                          |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    for(int i = RangeList.Total() - 1; i >= 0; i--)
    {
        RangeObject* range = RangeList.At(i);
        if(range != NULL)
        {
            ObjectDelete(0, range.objName);
            ObjectDelete(0, range.objName + "_Label");
            delete range;
        }
    }
    RangeList.Clear();
    Comment("");
}