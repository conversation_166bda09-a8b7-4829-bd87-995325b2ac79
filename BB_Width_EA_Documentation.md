# BB_Width_EA 使用文档

## 1. 简介

BB_Width_EA（布林带宽度监控EA）是一个专为MetaTrader 5平台开发的专家顾问，用于监控布林带宽度并在满足特定条件时发出信号。该EA能够识别布林带宽度低于设定阈值的情况，这通常表示市场波动性降低，可能即将出现重要的价格突破。

## 2. 主要功能

- **布林带宽度监控**：实时监控布林带宽度，当宽度低于设定阈值时发出信号
- **连续信号跟踪**：记录并显示连续信号的次数，包括当前连续、最近连续和历史最高连续次数
- **信号可视化**：在图表上使用箭头标记信号位置
- **实时信息面板**：显示当前、前一和前二K线的布林带数据、OHLC价格、ATR和成交量
- **K线倒计时**：显示当前K线收盘倒计时
- **微信通知**：通过企业微信机器人发送文本和图表截图通知
- **数据记录**：将信号数据保存到CSV文件中，按周自动分割
- **预设配置系统**：根据交易品种和时间周期自动应用最佳参数
- **资源监控**：定期记录EA的资源使用情况

## 3. 安装步骤

1. 将`Experts\BB_Width_EA.mq5`文件复制到MT5的`Experts`目录
2. 将`Include\BB_Width_EA`文件夹及其所有文件复制到MT5的`Include`目录
3. 将`Include\WeChatRobotManager.mqh`文件复制到MT5的`Include`目录
4. 在MetaTrader 5中编译BB_Width_EA.mq5文件
5. 将编译后的EA附加到所需的图表上

**快速安装（使用安装脚本）**：
1. 打开PowerShell
2. 执行以下命令允许脚本执行：
   ```powershell
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   ```
3. 运行安装脚本：
   ```powershell
   .\install.ps1
   ```

## 4. 参数配置

### 布林带参数
- **BB_Period** (默认: 20)：布林带周期
- **BB_Deviation** (默认: 2.0)：标准差倍数
- **BB_Shift** (默认: 0)：指标平移
- **APP_Price** (默认: PRICE_CLOSE)：应用价格
- **BB_TF** (默认: PERIOD_CURRENT)：时间框架

### 面板参数
- **Panel_Corner** (默认: 0)：面板位置（0-3）
- **Panel_Text_Color** (默认: clrWhite)：文本颜色
- **Panel_Back_Color** (默认: clrSteelBlue)：背景颜色
- **Panel_Font** (默认: "Consolas")：面板字体

### 信号参数
- **Threshold_Width_Input** (默认: 8.0)：宽度阈值
- **Arrow_Color** (默认: clrGoldenrod)：箭头颜色

### 通知参数
- **WeChat_Enabled** (默认: true)：启用微信提醒
- **WeChat_Consec_Trigger_Input** (默认: 5)：微信通知连续次数阈值
- **WeChat_Webhook**：微信机器人Webhook地址

### 资源监控参数
- **ResourceMonitor_Enabled_Input** (默认: false)：启用资源监控

## 5. 用户界面

BB_Width_EA提供了一个信息丰富的面板，显示在图表上。面板包含以下信息：

### 布林带数据区域
- **前二K线**：显示前二K线的布林带上轨、中轨、下轨和宽度，以及OHLC价格、ATR和成交量
- **前一K线**：显示前一K线的布林带数据和价格信息
- **当前K线**：显示当前K线的布林带数据和价格信息，以及K线收盘倒计时

### 连续状态区域
- **当前阈值**：显示当前使用的宽度阈值（绿色表示当前宽度低于阈值，红色表示高于阈值）
- **当前连续**：当前连续信号次数
- **最近连续**：最近一次连续信号次数
- **历史最高**：历史最高连续信号次数
- **平均连续**：平均连续信号次数
- **总周期数**：总信号周期数

### K线倒计时
- 显示当前K线收盘倒计时，格式根据剩余时间自动调整：
  - 大于1小时：HH:MM:SS
  - 小于1小时：MM:SS
  - 剩余时间少于总时间的20%时显示为红色

## 6. 信号生成和通知

### 信号条件
当布林带宽度（上轨减下轨的差值）低于设定的阈值时，EA会生成信号。

### 信号可视化
- 在图表上使用箭头标记信号位置（位于下轨处）
- 最多保留最近的500个箭头，超过时会自动删除最旧的

### 微信通知
当连续信号次数达到设定的阈值（默认为5次）时，EA会发送微信通知：
1. **信号开始通知**：当连续信号次数首次达到阈值时发送
2. **信号结束通知**：当连续信号结束（宽度不再低于阈值）时发送

通知内容包括：
- 信号类型（开始/结束）
- 交易品种和时间周期
- 当前时间
- 参数设置（周期数、偏移量、阈值宽度）
- 连续状态（当前连续、历史最高、最近连续、平均连续）
- 实时数据（上轨、中轨、下轨、宽度）
- 图表截图

## 7. 配置系统

BB_Width_EA包含一个预设配置系统，可以根据交易品种和时间周期自动应用最佳参数。

### 默认配置
- XAUUSD, M5: 宽度阈值 = 6.5, 连续次数 = 5
- EURUSD, M15: 宽度阈值 = 0.00050, 连续次数 = 5
- EURUSD, H1: 宽度阈值 = 0.00080, 连续次数 = 3
- GBPUSD, M15: 宽度阈值 = 0.00060, 连续次数 = 5
- USDJPY, H1: 宽度阈值 = 0.080, 连续次数 = 3

### 自定义配置
用户可以通过创建或编辑`BB_Width_Configs.csv`文件来添加自定义配置。该文件位于MT5的公共文件目录（`%APPDATA%\MetaQuotes\Terminal\Common\Files\`）。

CSV文件格式：
```
Symbol,Timeframe,Threshold,Consecutive
XAUUSD,M5,6.5,5
EURUSD,M15,0.00050,5
```

### 品种匹配
配置系统支持带后缀的品种名称匹配，例如"EURUSD.a"会匹配"EURUSD"的配置。

## 8. 数据记录

### CSV文件
EA会将信号数据保存到CSV文件中，文件位于MT5的公共文件目录（`%APPDATA%\MetaQuotes\Terminal\Common\Files\`）。

文件命名格式：
```
BB_Width_Data_[品种]_[时间周期]_Week[周数]_[周起始日期]_[周结束日期].csv
```

CSV文件包含以下列：
- Time: 信号时间
- Price: 信号价格
- Upper: 布林带上轨
- Middle: 布林带中轨
- Lower: 布林带下轨
- Width: 布林带宽度
- Open: 开盘价
- High: 最高价
- Low: 最低价
- Close: 收盘价
- ATR: 平均真实波幅
- Volume: 成交量
- Timestamp: 时间戳

### 截图保存
EA会在发送微信通知时保存图表截图。截图保存在与CSV文件同名的目录中（不带后缀）。

## 9. 资源监控

当启用资源监控功能时，EA会每小时记录一次资源使用情况，包括：
- 内存使用情况（总计、已用、百分比）
- 对象数量（总计、箭头数量）
- 指标句柄状态
- 终端信息（DLL允许、最大K线数、代码页）
- EA运行时间

资源使用情况会记录在MT5的日志中。

## 10. 故障排除和常见问题

### 微信通知不工作
- 检查WeChat_Enabled参数是否设置为true
- 确认WeChat_Webhook地址是否正确
- 检查网络连接是否正常
- 查看MT5日志中是否有相关错误信息

### 找不到信号
- 检查Threshold_Width_Input参数是否设置合适
- 确认布林带参数（BB_Period, BB_Deviation）是否适合当前市场
- 尝试使用预设配置系统为特定品种和时间周期设置参数

### 截图保存失败
- 确保MT5有足够的权限创建目录和文件
- 检查磁盘空间是否充足
- 查看MT5日志中的错误信息

## 11. 版本历史

### 版本 1.00
- 初始版本
- 基本布林带宽度监控功能
- 信息面板显示
- 微信通知功能
- CSV数据记录

## 12. 联系与支持

如有任何问题或建议，请联系开发者：
- 邮箱：[<EMAIL>](mailto:<EMAIL>)
- 网站：[https://www.example.com](https://www.example.com)
