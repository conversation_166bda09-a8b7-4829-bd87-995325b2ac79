//+------------------------------------------------------------------+
//|                                            AdvancedDojiAlert.mq5 |
//|                                Copyright 2024, Your Company Name |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.deepseek.com"
#property version   "1.00"

#include <WeChatRobotManager.mqh>

input double   BodyRatioThreshold = 0.1;     // 实体比例阈值 (0-1)
input int      MinPoints = 10;               // 最小波动点数
input color    MarkerColor = clrDodgerBlue;   // 标记颜色
input int      ArrowSize = 2;                // 箭头大小

CWeChatRobotManager weChat;           // 微信机器人实例
input string   WebhookURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 微信机器人Webhook地址


datetime lastBarTime;  // 最后检测的K线时间

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
        // 初始化微信机器人
    if(WebhookURL == "") {
        Alert("请配置企业微信webhook地址！");
        return(INIT_PARAMETERS_INCORRECT);
    }
    
    if(!weChat.Initialize(WebhookURL)) {
        Alert("微信机器人初始化失败！");
        return(INIT_FAILED);
    }
    // 清空旧标记
    ObjectsDeleteAll(0, "Doji_");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA去初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, "Doji_");
}

//+------------------------------------------------------------------+
//| 新K线检测主逻辑                                                  |
//+------------------------------------------------------------------+
void OnTick()
{
    // 获取当前图表信息
    datetime currentTime = iTime(_Symbol, _Period, 0);
    
    // 仅在全新K线时执行检测
    if(currentTime == lastBarTime) return;
    
    // 更新最后检测时间
    lastBarTime = currentTime;
    
    // 检测前一根闭合K线
    CheckPreviousBar();
}

//+------------------------------------------------------------------+
//| 检测前一闭合K线是否为十字星                                      |
//+------------------------------------------------------------------+
void CheckPreviousBar()
{
    // 获取前一根K线数据（索引1）
    int      barIndex = 1;
    datetime barTime  = iTime(_Symbol, _Period, barIndex);
    double   open     = iOpen(_Symbol, _Period, barIndex);
    double   close    = iClose(_Symbol, _Period, barIndex);
    double   high     = iHigh(_Symbol, _Period, barIndex);
    double   low      = iLow(_Symbol, _Period, barIndex);
    
    // 计算波动参数
    double bodySize = MathAbs(close - open);
    double totalRange = high - low;
    
    // 排除无效数据
    if(totalRange <= 0) return;
    
    // 计算各组成部分
    double upperShadow = high - MathMax(open, close);
    double lowerShadow = MathMin(open, close) - low;
    double bodyRatio = bodySize / totalRange;
    
    // 十字星识别条件
    bool isDoji = bodyRatio < BodyRatioThreshold &&          // 实体比例小
                  upperShadow > bodySize &&                   // 上影线明显
                  lowerShadow > bodySize &&                   // 下影线明显
                  totalRange > (MinPoints * _Point);          // 排除极小区间
    
    if(isDoji)
    {
        CreateMarker(barTime, high);


                        // 发送文字通知
            string message = StringFormat(
                "发现十字星形态！\n"
                "品种：%s\n"
                "时间框架：%s\n"
                "开盘价：%.5f\n"
                "最高价：%.5f\n"
                "最低价：%.5f\n"
                "收盘价：%.5f\n"
                "上影线高度：%.5f\n"
                "实体高度：%.5f\n"
                "下影线高度：%.5f\n"
                "K线总高度：%.5f",
                _Symbol,
                EnumToString((ENUM_TIMEFRAMES)_Period),
                open,
                high,
                low,
                close,
                upperShadow,
                bodySize,
                lowerShadow,
                totalRange
            );
            weChat.SendMessage(message);
            
            // 生成并保存截图
            MqlDateTime timeStruct;
            TimeToStruct(TimeCurrent(), timeStruct);
            string fileName = StringFormat("Doji_%s_%s_%04d%02d%02d_%02d%02d%02d.png",
                _Symbol,
                EnumToString((ENUM_TIMEFRAMES)_Period),
                timeStruct.year, timeStruct.mon, timeStruct.day,
                timeStruct.hour, timeStruct.min, timeStruct.sec);
            
            // 确保目录存在
            string dirName = "DojisFinder_Screenshots";
            if(!FileIsExist(dirName, FILE_COMMON)) {
                if(!FolderCreate(dirName, FILE_COMMON)) {
                    Print("创建截图目录失败: ", GetLastError());
                } else {
                    Print("已创建截图目录: ", dirName);
                }
            }
            
            // 完整路径（包含目录）
            string filePath = dirName + "\\" + fileName;
            
            // 截图
            if(!ChartScreenShot(0, filePath, 1920, 1080, ALIGN_RIGHT)) {
                Print("截图失败: ", GetLastError());
            } else {
                Print("截图已生成: ", filePath);
                
                // 发送图片消息
                if(!weChat.SendImage(filePath)) {
                    Print("发送图片到微信失败");
                } else {
                    Print("图片已成功发送到微信");
                }
            }
    }
}

//+------------------------------------------------------------------+
//| 在指定位置创建标记                                                |
//+------------------------------------------------------------------+
void CreateMarker(datetime barTime, double priceLevel)
{
    string objName = "Doji_" + _Symbol + "_" + IntegerToString(barTime);
    
    // 删除旧标记
    if(ObjectFind(0, objName) >= 0) {
        ObjectDelete(0, objName);
    }
    
    // 创建新箭头
    if(!ObjectCreate(0, objName, OBJ_ARROW_UP, 0, barTime, priceLevel))
    {
        Print("创建标记失败: ", GetLastError());
        return;
    }
    
    // 设置箭头属性
    ObjectSetInteger(0, objName, OBJPROP_COLOR, MarkerColor);
    ObjectSetInteger(0, objName, OBJPROP_WIDTH, ArrowSize);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
    ObjectSetDouble(0, objName, OBJPROP_PRICE, priceLevel + 3 * _Point);
    
    // 自动调整图表视图
    ChartNavigate(0, CHART_END, -30);
    ChartRedraw();


}