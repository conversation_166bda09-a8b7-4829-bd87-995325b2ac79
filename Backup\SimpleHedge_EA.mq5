//+------------------------------------------------------------------+
//|                                               SimpleHedge_EA.mq5 |
//|                              简化版对冲策略 - 只认赚不认赔核心版本  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property description "简化版布林带对冲策略，严格按照原始聊天记录逻辑实现"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
input group "基本设置";
input int MagicNumber = 888888;             // 魔术号
input double LotSize = 0.25;                // 交易手数（25%仓位）
input int MaxPositions = 2;                 // 最大持仓数（一多一空）

input group "布林带设置";
input int BB_Period = 20;                   // 布林带周期
input double BB_Deviation = 2.0;            // 布林带偏差
input ENUM_TIMEFRAMES Timeframe = PERIOD_H1; // 时间周期（1小时）

input group "止盈设置";
input double TakeProfit_Points = 500;       // 止盈点数
input bool Use_ATR_TP = false;              // 使用ATR动态止盈
input double ATR_Multiplier = 2.0;          // ATR倍数

input group "风险控制";
input bool Enable_Manual_Lock = true;       // 启用手动锁仓提醒
input double Lock_Distance = 5000;          // 锁仓提醒距离（点）

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
CTrade trade;
CPositionInfo pos;
datetime lastBarTime = 0;

//+------------------------------------------------------------------+
//| EA初始化                                                          |
//+------------------------------------------------------------------+
int OnInit() {
    trade.SetExpertMagicNumber(MagicNumber);
    
    Print("=== 简化版对冲策略EA启动 ===");
    Print("核心逻辑：布林上轨开空，下轨开多");
    Print("风险控制：只认赚不认赔，永不加仓");
    Print("手数设置：", LotSize, " (建议25%仓位)");
    Print("时间周期：", EnumToString(Timeframe));
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 主要交易逻辑                                                      |
//+------------------------------------------------------------------+
void OnTick() {
    // 只在新K线时执行
    if(!IsNewBar()) return;
    
    // 获取布林带数据
    double upper[], lower[], middle[];
    if(!GetBollingerBands(upper, lower, middle)) return;
    
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double askPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    // 检查开仓条件
    CheckBuySignal(askPrice, lower[1]);   // 下轨开多
    CheckSellSignal(currentPrice, upper[1]); // 上轨开空
    
    // 检查止盈
    CheckTakeProfit();
    
    // 检查锁仓提醒
    if(Enable_Manual_Lock) {
        CheckLockAlert();
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                   |
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime currentBarTime = iTime(_Symbol, Timeframe, 0);
    if(lastBarTime != currentBarTime) {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 获取布林带数据                                                    |
//+------------------------------------------------------------------+
bool GetBollingerBands(double &upper[], double &lower[], double &middle[]) {
    ArraySetAsSeries(upper, true);
    ArraySetAsSeries(lower, true);
    ArraySetAsSeries(middle, true);
    
    int bbHandle = iBands(_Symbol, Timeframe, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
    if(bbHandle == INVALID_HANDLE) return false;
    
    if(CopyBuffer(bbHandle, 1, 0, 3, upper) <= 0) return false;  // 上轨
    if(CopyBuffer(bbHandle, 2, 0, 3, lower) <= 0) return false;  // 下轨
    if(CopyBuffer(bbHandle, 0, 0, 3, middle) <= 0) return false; // 中轨
    
    return true;
}

//+------------------------------------------------------------------+
//| 检查买入信号（下轨开多）                                           |
//+------------------------------------------------------------------+
void CheckBuySignal(double askPrice, double lowerBand) {
    // 如果已有多头持仓，不再开仓
    if(HasPosition(POSITION_TYPE_BUY)) return;
    
    // 如果达到最大持仓数，不再开仓
    if(GetPositionCount() >= MaxPositions) return;
    
    // 价格触及或低于下轨时开多
    if(askPrice <= lowerBand) {
        double tp = CalculateTakeProfit(true, askPrice);
        
        if(trade.Buy(LotSize, _Symbol, askPrice, 0, tp, "下轨开多")) {
            Print("✅ 下轨开多成功 - 价格:", askPrice, " 下轨:", lowerBand, " 止盈:", tp);
        } else {
            Print("❌ 下轨开多失败 - 错误:", trade.ResultRetcode());
        }
    }
}

//+------------------------------------------------------------------+
//| 检查卖出信号（上轨开空）                                           |
//+------------------------------------------------------------------+
void CheckSellSignal(double bidPrice, double upperBand) {
    // 如果已有空头持仓，不再开仓
    if(HasPosition(POSITION_TYPE_SELL)) return;
    
    // 如果达到最大持仓数，不再开仓
    if(GetPositionCount() >= MaxPositions) return;
    
    // 价格触及或高于上轨时开空
    if(bidPrice >= upperBand) {
        double tp = CalculateTakeProfit(false, bidPrice);
        
        if(trade.Sell(LotSize, _Symbol, bidPrice, 0, tp, "上轨开空")) {
            Print("✅ 上轨开空成功 - 价格:", bidPrice, " 上轨:", upperBand, " 止盈:", tp);
        } else {
            Print("❌ 上轨开空失败 - 错误:", trade.ResultRetcode());
        }
    }
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit(bool isBuy, double entryPrice) {
    double tpDistance = TakeProfit_Points * _Point;
    
    // 使用ATR动态止盈
    if(Use_ATR_TP) {
        int atrHandle = iATR(_Symbol, Timeframe, 14);
        double atrBuffer[];
        ArraySetAsSeries(atrBuffer, true);
        if(CopyBuffer(atrHandle, 0, 1, 1, atrBuffer) > 0) {
            tpDistance = atrBuffer[0] * ATR_Multiplier;
        }
    }
    
    if(isBuy) {
        return entryPrice + tpDistance;
    } else {
        return entryPrice - tpDistance;
    }
}

//+------------------------------------------------------------------+
//| 检查是否有指定类型持仓                                             |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_POSITION_TYPE posType) {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && 
               pos.Magic() == MagicNumber &&
               pos.PositionType() == posType) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 获取持仓数量                                                      |
//+------------------------------------------------------------------+
int GetPositionCount() {
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 检查止盈（只认赚不认赔）                                           |
//+------------------------------------------------------------------+
void CheckTakeProfit() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber) {
                double profit = pos.Profit();
                
                // 核心逻辑：只有盈利才平仓
                if(profit > 0) {
                    if(trade.PositionClose(pos.Ticket())) {
                        Print("💰 止盈平仓成功 - 盈利: $", DoubleToString(profit, 2));
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查锁仓提醒                                                      |
//+------------------------------------------------------------------+
void CheckLockAlert() {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == MagicNumber) {
                double openPrice = pos.PriceOpen();
                double currentPrice = (pos.PositionType() == POSITION_TYPE_BUY) ?
                                    SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                    SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                
                double lossPoints = 0;
                if(pos.PositionType() == POSITION_TYPE_BUY) {
                    lossPoints = (openPrice - currentPrice) / _Point;
                } else {
                    lossPoints = (currentPrice - openPrice) / _Point;
                }
                
                // 如果亏损超过锁仓距离，发出提醒
                if(lossPoints > Lock_Distance) {
                    Print("⚠️ 锁仓提醒 - 持仓亏损:", DoubleToString(lossPoints, 0), "点，建议考虑手动锁仓");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| EA停止                                                            |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("=== 简化版对冲策略EA已停止 ===");
    Print("记住：做时间的朋友，耐心等待盈利！");
}
