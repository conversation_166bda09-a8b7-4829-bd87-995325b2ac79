//+------------------------------------------------------------------+
//|                                 ForexEngulfingEA.mq5             |
//|                                  Copyright 2023, ForexMaster Pro |
//|                                             https://www.forexmaster.pro |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, ForexMaster Pro"
#property link      "https://www.forexmaster.pro"
#property version   "2.5"
#property description "外汇市场专用吞没信号检测EA"
#property description "考虑外汇市场连续交易特性"

// 输入参数
input int      MA_Period = 50;         // 趋势判断MA周期
input double   MinBodyRatio = 1.5;     // 吞没形态最小实体比例
input bool     ShowArrows = true;      // 在图表上显示信号箭头
input color    BullishColor = clrGreen; // 看涨信号颜色
input color    BearishColor = clrRed;   // 看跌信号颜色
input bool     StrictMode = false;     // 严格模式(要求完全吞没影线)

// 全局变量
int handleMA;
double maBuffer[];
datetime lastBarTime;
string commentText = "";
string engulfingText = "";

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化移动平均线指标
   handleMA = iMA(_Symbol, PERIOD_CURRENT, MA_Period, 0, MODE_SMA, PRICE_CLOSE);
   if(handleMA == INVALID_HANDLE)
   {
      Print("MA指标初始化失败!");
      return(INIT_FAILED);
   }
   
   // 设置数组为时间序列
   ArraySetAsSeries(maBuffer, true);
   
   // 初始化最后K线时间
   lastBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA主函数                                                         |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查新K线
   if(!IsNewBar()) return;
   
   // 更新指标数据
   UpdateIndicators();
   
   // 检测吞没形态(外汇专用逻辑)
   DetectEngulfingPattern();
   
   // 更新图表注释
   UpdateComment();
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                  |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 更新指标数据                                                     |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
   // 复制MA数据
   if(CopyBuffer(handleMA, 0, 0, 3, maBuffer) < 3)
   {
      Print("MA数据获取失败!");
      return;
   }
}

//+------------------------------------------------------------------+
//| 检测吞没形态(外汇市场专用逻辑)                                    |
//+------------------------------------------------------------------+
void DetectEngulfingPattern()
{
   // 获取K线数据
   double open1 = iOpen(_Symbol, PERIOD_CURRENT, 2);
   double high1 = iHigh(_Symbol, PERIOD_CURRENT, 2);
   double low1 = iLow(_Symbol, PERIOD_CURRENT, 2);
   double close1 = iClose(_Symbol, PERIOD_CURRENT, 2);
   
   double open0 = iOpen(_Symbol, PERIOD_CURRENT, 1);
   double high0 = iHigh(_Symbol, PERIOD_CURRENT, 1);
   double low0 = iLow(_Symbol, PERIOD_CURRENT, 1);
   double close0 = iClose(_Symbol, PERIOD_CURRENT, 1);

//    PrintFormat("1 - OHCL数据: %f, %f, %f ,%f", open1, high1, low1, close1);
//    PrintFormat("0 - OHCL数据: %f, %f, %f ,%f", open0, high0, low0, close0);
   
   // 计算实体大小
   double body1 = MathAbs(close1 - open1);
   double body0 = MathAbs(close0 - open0);
   
   // 确定当前趋势
   string trend = "震荡";
   double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   if(currentPrice > maBuffer[0] && maBuffer[0] > maBuffer[1])
      trend = "上涨";
   else if(currentPrice < maBuffer[0] && maBuffer[0] < maBuffer[1])
      trend = "下跌";
   
   commentText = trend;

   // 检测看涨吞没
   bool bullishEngulfing = false;
   if(close1 < open1 &&  // 第一根阴线
      close0 > open0 &&  // 第二根阳线
      body0 > body1 * MinBodyRatio) // 实体比例符合要求
   {
      // 外汇市场特殊条件(开盘价通常等于前收盘价)
      bool condition1 = (open0 <= close1) && (close0 >= open1); // 基本吞没条件
      bool condition2 = (low0 <= low1) && (high0 >= high1); // 完全吞没影线
      
      if(StrictMode ? condition2 : condition1)
      {
         bullishEngulfing = true;
        engulfingText = "看涨吞没";
         
         if(ShowArrows)
            CreateArrow(1, iHigh(_Symbol, PERIOD_CURRENT, 1), BullishColor, 241);
      }
   }
   
   // 检测看跌吞没
   bool bearishEngulfing = false;
   if(close1 > open1 &&  // 第一根阳线
      close0 < open0 &&  // 第二根阴线
      body0 > body1 * MinBodyRatio) // 实体比例符合要求
   {
      // 外汇市场特殊条件(开盘价通常等于前收盘价)
      bool condition1 = (open0 >= close1) && (close0 <= open1); // 基本吞没条件
      bool condition2 = (high0 >= high1) && (low0 <= low1); // 完全吞没影线
      
      if(StrictMode ? condition2 : condition1)
      {
         bearishEngulfing = true;
        engulfingText = "看跌吞没";
         
         if(ShowArrows)
            CreateArrow(1, iHigh(_Symbol, PERIOD_CURRENT, 1), BearishColor, 242);
      }
   }

      //   bool bullishEngulfing = (close0 > open0) && (open0 < close1) && (close0 > open1);
      //   bool bearishEngulfing = (close0 < open0) && (open0 > close1) && (close0 < open1);
}

//+------------------------------------------------------------------+
//| 创建信号箭头                                                     |
//+------------------------------------------------------------------+
void CreateArrow(int barIndex, double price, color clr, int code)
{
   string objName = "EngulfingArrow_" + IntegerToString(barIndex) + "_" + TimeToString(TimeCurrent());
   
   // 删除旧对象
    // if(ObjectFind(0, objName) >= 0) ObjectDelete(0, objName);
   
   // 创建新箭头
   ObjectCreate(0, objName, OBJ_ARROW, 0, iTime(_Symbol, PERIOD_CURRENT, barIndex), price);
   ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, code);
   ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, objName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   ObjectSetInteger(0, objName, OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
}

//+------------------------------------------------------------------+
//| 更新图表注释                                                     |
//+------------------------------------------------------------------+
void UpdateComment()
{
   string comment = "=== 外汇吞没信号检测系统 ===\n";
   comment += "当前品种: " + _Symbol + "\n";
   comment += "当前时间: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + "\n";
   comment += "当前趋势: " + commentText + "\n";
   comment += "吞噬形态: " + engulfingText + "\n\n";
   
   comment += "外汇特性: 新K线开盘价=前K线收盘价\n";
   comment += "趋势判断: " + IntegerToString(MA_Period) + "周期SMA\n";
   comment += "看涨吞没条件: 阴线+阳线实体吞没(比例>" + DoubleToString(MinBodyRatio, 1) + ")\n";
   comment += "看跌吞没条件: 阳线+阴线实体吞没(比例>" + DoubleToString(MinBodyRatio, 1) + ")\n";
   comment += "模式: " + (StrictMode ? "严格(吞没影线)" : "标准(吞没实体)");
   
   Comment(comment);
}

//+------------------------------------------------------------------+
//| EA去初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Comment(""); // 清除图表注释
   ObjectsDeleteAll(0, -1, OBJ_ARROW); // 删除所有箭头
}