//+------------------------------------------------------------------+
//|                                              NR4_Detector_Pro.mq5|
//|                                Copyright 2024, DeepSeek Trading  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, DeepSeek Trading"
#property link      "https://www.mql5.com"
#property version   "2.10"

//--- 输入参数
input group "基本参数"
input int      NR_Period = 4;                // NR周期（检测前N根）
input double   MinRangeRatio = 0.85;         // 最小波动比例阈值
input int      MinPoints = 15;               // 最小有效点数

input group "K线实体高度过滤"
input double   BodyHeightThreshold = 6500.0;    // 实体高度阈值(点)
input color    ExceedColor = clrGray;         // 超标标记颜色

//--- 全局变量
datetime lastBarTime;       // 最后检测的K线时间
double   lastNR4Range;      // 最后一次NR4波动范围

//+------------------------------------------------------------------+
//| EA初始化函数                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   CleanOldMarkers();
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA去初始化函数                                                   |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   CleanOldMarkers();
}

//+------------------------------------------------------------------+
//| 清理旧标记                                                       |
//+------------------------------------------------------------------+
void CleanOldMarkers()
{
   ObjectsDeleteAll(0, "NR4_");
   Comment("");
}

//+------------------------------------------------------------------+
//| 主Tick处理函数                                                   |
//+------------------------------------------------------------------+
void OnTick()
{
   if(IsNewBar())
   {
      DetectNR4Pattern();
   }
}

//+------------------------------------------------------------------+
//| 检测新K线生成                                                    |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentTime = iTime(_Symbol, (ENUM_TIMEFRAMES)_Period, 0);
   if(currentTime != lastBarTime)
   {
      lastBarTime = currentTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| NR4形态检测核心逻辑                                              |
//+------------------------------------------------------------------+
void DetectNR4Pattern()
{
   // 确保有足够的历史数据
   if(Bars(_Symbol, (ENUM_TIMEFRAMES)_Period) < NR_Period + 1) return;

   double currentRange = GetCandleRange(1);  // 前一根闭合K线
   double ranges[];

   // 收集前NR_Period根K线的波动范围
   ArrayResize(ranges, NR_Period);
   for(int i = 0; i < NR_Period; i++)
   {
      ranges[i] = GetCandleRange(i + 2); // 从当前K线前第2根开始
   }
   ArrayReverse(ranges);
   IsNR4(currentRange, ranges, 1);
}

//+------------------------------------------------------------------+
//| 获取指定K线的波动范围                                            |
//+------------------------------------------------------------------+
double GetCandleRange(int barIndex)
{
   return iHigh(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex) 
        - iLow(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex);
}

//+------------------------------------------------------------------+
//| NR4形态验证逻辑                                                  |
//+------------------------------------------------------------------+
bool IsNR4(double currentRange, double &previousRanges[], int barIndex)
{
   // 基本波动条件
   if(currentRange < MinPoints * _Point) return false;
   
   // 波动比例验证
   double maxPreviousRange = previousRanges[ArrayMaximum(previousRanges)];
   if(currentRange > maxPreviousRange * MinRangeRatio) return false;

   // 严格模式：当前波动小于所有前序波动
   for(int i = 0; i < ArraySize(previousRanges); i++)
   {
      if(currentRange >= previousRanges[i]) return false;
   }
   
   // 实体高度过滤
   double bodyHeight = GetCandleBodyHeight(barIndex);
   if(bodyHeight > BodyHeightThreshold * _Point) {
      // Print("bodyHeight:", bodyHeight);
      // Print("BodyHeightThreshold:", BodyHeightThreshold);
      // Print("_Point:", _Point);
      CreateExceedMarker(barIndex);  // 创建超标标记
   }else{
      CreateMarker(barIndex);
   }

   return true;
}

//+------------------------------------------------------------------+
//| 获取K线实体高度                                                  |
//+------------------------------------------------------------------+
double GetCandleBodyHeight(int barIndex)
{
    return MathAbs(iClose(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex) 
                 - iOpen(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex));
}

//+------------------------------------------------------------------+
//| 创建超标标记                                                     |
//+------------------------------------------------------------------+
void CreateExceedMarker(int barIndex)
{
    datetime barTime = iTime(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex);
    double priceLevel = iHigh(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex) 
                      + 5 * _Point;

    string objName = StringFormat("EXCEED_%s_%d", _Symbol, barTime);
    
    // 删除旧标记
   //  if(ObjectFind(0, objName) >= 0) ObjectDelete(0, objName);
    
    // 创建X标记
    if(!ObjectCreate(0, objName, OBJ_ARROW_STOP, 0, barTime, priceLevel)) {
        Print("创建超标标记失败: ", GetLastError());
        return;
    }
    
    // 设置标记属性
    ObjectSetInteger(0, objName, OBJPROP_COLOR, ExceedColor);
    ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
}

//+------------------------------------------------------------------+
//| 在图表创建标记                                                   |
//+------------------------------------------------------------------+
void CreateMarker(int barIndex)
{
   datetime barTime = iTime(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex);
   double priceLevel = iLow(_Symbol, (ENUM_TIMEFRAMES)_Period, barIndex);

   string objName = StringFormat("NR4_%s_%d", _Symbol, barTime);
   
   // 创建箭头对象
   if(!ObjectCreate(0, objName, OBJ_ARROW_CHECK, 0, barTime, priceLevel))
   {
      Print("创建标记失败: ", GetLastError());
      return;
   }
}

//+------------------------------------------------------------------+
//| 更新图表注释                                                     |
//+------------------------------------------------------------------+
void UpdateChartComment()
{
   string comment = StringFormat("NR4 Detector Pro\n" +
                                 "当前品种: %s\n" +
                                 "时间框架: %s\n" +
                                 "最后一次NR4波动: %.5f\n" +
                                 "检测时间: %s",
                                 _Symbol, 
                                 EnumToString((ENUM_TIMEFRAMES)_Period),
                                 lastNR4Range,
                                 TimeToString(lastBarTime));
   Comment(comment);
}

//+------------------------------------------------------------------+
//| 触发警报                                                         |
//+------------------------------------------------------------------+
void TriggerAlert(double range)
{
   string alertMsg = StringFormat("NR4形态警报！品种：%s 周期：%s\n" +
                                  "波动范围：%.5f\n时间：%s",
                                  _Symbol, 
                                  EnumToString((ENUM_TIMEFRAMES)_Period),
                                  range,
                                  TimeToString(TimeCurrent()));
   Alert(alertMsg);
}
//+------------------------------------------------------------------+