//+------------------------------------------------------------------+
//|                                           SignalProcessor.mqh    |
//+------------------------------------------------------------------+
#ifndef __SIGNAL_PROCESSOR_MQH__
#define __SIGNAL_PROCESSOR_MQH__

#include "Common.mqh"
#include "SignalDetector.mqh"
#include "NotificationManager.mqh"

// 处理新K线信号
void ProcessNewBarSignal()
{
   // 获取当前K线时间
   datetime currentBarTime = iTime(Symbol(), BB_TF, 0);
   
   // 检测新K线开始
   if(currentBarTime != lastBarTime)
   {
      // 检查前一K线条件
      bool previousConditionMet = (prevBB.width > 0 && prevBB.width < Threshold_Width);
      
      if(previousConditionMet) {
         currentConsecutive++;
         maxConsecutive = MathMax(currentConsecutive, maxConsecutive);
         
         // 检查是否达到通知触发条件
         if(currentConsecutive == WeChat_Consec_Trigger) {
            SendBBWidthAlert(false); // false表示这是信号开始的通知
         }
         
      } else {
         // 保存最后一次有效连续次数
         if(currentConsecutive >= WeChat_Consec_Trigger) {
            // 如果之前的连续次数已经达到或超过了触发阈值，发送信号结束通知
            SendBBWidthAlert(true); // true表示这是信号结束的通知
         }
         
         if(currentConsecutive > 0) {
            totalOccurrences++;  // 累计总次数
            lastConsecutive = currentConsecutive;
            totalCycles++;
            averageConsecutive = (averageConsecutive*(totalCycles-1) + currentConsecutive)/totalCycles;
         }
         currentConsecutive = 0;
      }
      lastBarTime = currentBarTime;
   }
}

// 检查并创建信号箭头
void CheckAndCreateSignalArrow(datetime currentTime, datetime &lastSignalCheckTime)
{
   // 绘制信号箭头（基于前一K线）- 限制频率，至少间隔10秒
   if(currentTime - lastSignalCheckTime >= 10)
   {
      if(ValidateConditions()) {
         datetime time = iTime(Symbol(), BB_TF, 1);
         CreateSignalArrow(time, prevBB.lower);
      }
      lastSignalCheckTime = currentTime;
   }
}

#endif
