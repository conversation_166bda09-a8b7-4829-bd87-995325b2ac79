//+------------------------------------------------------------------+
//|                                                   VolatilityEA.mq5 |
//|                                   Copyright 2024, Trae AI Assistant |
//|                                             https://www.example.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Trae AI Assistant"
#property version   "1.00"
#property strict

// 输入参数
input int    LookbackHours = 5;        // 波动率计算回溯小时数
input double Threshold = 20.0;         // 波动率扩张阈值（%）
input int    SignalDisplayTime = 60;    // 信号显示时间（分钟）

// 全局变量
double prevVolatility[];
datetime lastBarTime;
int arrowCounter = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化波动率数组
   ArraySetAsSeries(prevVolatility, true);
   ArrayResize(prevVolatility, LookbackHours);
   
   // 加载历史数据
   LoadHistoricalVolatility();
   
   // 设置定时器（每分钟检查一次）
   EventSetTimer(60);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   EventKillTimer();
   DeleteAllSignals();
}

//+------------------------------------------------------------------+
//| Timer事件处理函数                                                |
//+------------------------------------------------------------------+
void OnTimer()
{
   CheckNewBar();
}

//+------------------------------------------------------------------+
//| 波动率计算函数                                                   |
//+------------------------------------------------------------------+
double CalculateVolatility(int shift)
{
   // 获取指定小时的数据
   datetime time = iTime(_Symbol, PERIOD_H1, shift);
   double high = iHigh(_Symbol, PERIOD_H1, shift);
   double low = iLow(_Symbol, PERIOD_H1, shift);
   
   // 返回波动幅度（点数）
   return (high - low) / _Point;
}

//+------------------------------------------------------------------+
//| 加载历史波动率数据                                               |
//+------------------------------------------------------------------+
void LoadHistoricalVolatility()
{
   for(int i = 0; i < LookbackHours; i++)
   {
      prevVolatility[i] = CalculateVolatility(i + 1); // 跳过当前未闭合的K线
   }
}

//+------------------------------------------------------------------+
//| 检测新K线                                                       |
//+------------------------------------------------------------------+
void CheckNewBar()
{
   datetime currentTime = iTime(_Symbol, PERIOD_H1, 0);
   
   if(currentTime != lastBarTime)
   {
      // 更新波动率数据
      UpdateVolatilityArray();
      
      // 计算当前波动率
      double currentVol = CalculateVolatility(0);
      
      // 计算平均历史波动率
      double avgVol = ArrayGetAverage(prevVolatility);
      
      // 计算扩张速率
      double expansionRate = 0;
      if(avgVol > 0)
      {
         expansionRate = ((currentVol - avgVol) / avgVol) * 100.0;
      }
      
      // 触发信号逻辑
      if(expansionRate > Threshold)
      {
         string message = StringFormat("波动率扩张 %.2f%% > %.2f%% 阈值", expansionRate, Threshold);
         DisplaySignal(message, currentVol);
         Alert(message);
      }
      
      lastBarTime = currentTime;
   }
}

//+------------------------------------------------------------------+
//| 更新波动率数组                                                   |
//+------------------------------------------------------------------+
void UpdateVolatilityArray()
{
   // 将数组元素向左移动
   for(int i = LookbackHours - 1; i > 0; i--)
   {
      prevVolatility[i] = prevVolatility[i - 1];
   }
   
   // 添加最新波动率（使用前一根已闭合的K线）
   prevVolatility[0] = CalculateVolatility(1);
}

//+------------------------------------------------------------------+
//| 显示图表信号                                                     |
//+------------------------------------------------------------------+
void DisplaySignal(string text, double volValue)
{
   arrowCounter++;
   
   // 创建箭头对象
   string arrowName = StringFormat("SignalArrow_%d", arrowCounter);
   ObjectCreate(0, arrowName, OBJ_ARROW_UP, 0, iTime(_Symbol, PERIOD_H1, 0), iLow(_Symbol, PERIOD_H1, 0) - 50 * _Point);
   
   // 设置箭头属性
   ObjectSetInteger(0, arrowName, OBJPROP_COLOR, clrLime);
   ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 3);
   ObjectSetInteger(0, arrowName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   
   // 创建文本标签
   string labelName = StringFormat("SignalLabel_%d", arrowCounter);
   ObjectCreate(0, labelName, OBJ_TEXT, 0, iTime(_Symbol, PERIOD_H1, 0), iHigh(_Symbol, PERIOD_H1, 0) + 50 * _Point);
   ObjectSetString(0, labelName, OBJPROP_TEXT, text);
   ObjectSetInteger(0, labelName, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
   
   // 设置自动删除时间
   ObjectSetInteger(0, arrowName, OBJPROP_TIMEFRAMES, OBJ_ALL_PERIODS);
   ObjectSetInteger(0, labelName, OBJPROP_TIMEFRAMES, OBJ_ALL_PERIODS);
}

//+------------------------------------------------------------------+
//| 自定义数组平均值计算                                             |
//+------------------------------------------------------------------+
double ArrayGetAverage(double &arr[])
{
   double sum = 0.0;
   for(int i = 0; i < ArraySize(arr); i++)
   {
      sum += arr[i];
   }
   return sum / ArraySize(arr);
}

//+------------------------------------------------------------------+
//| 删除所有信号对象                                                 |
//+------------------------------------------------------------------+
void DeleteAllSignals()
{
   ObjectsDeleteAll(0, "SignalArrow_");
   ObjectsDeleteAll(0, "SignalLabel_");
}