#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

// 外部参数
input int ConsolidationBars = 20;     // 整理K线数量
input double BreakThreshold = 0.0002; // 突破阈值
input int ADX_Period = 14;          // ADX周期
input double ADX_Threshold = 25.0;  // ADX趋势阈值
input double ATR_Multiplier = 1.2;  // ATR波动倍数
input double VolumeThreshold = 1.5;    // 成交量阈值倍数
input int ConfirmationBars = 2;       // 价格确认K线数量
input int FastMAPeriod = 50;        // 快速均线周期
input int SlowMAPeriod = 200;       // 慢速均线周期
input int PanelX = 20;               // 面板X坐标
input int PanelY = 20;               // 面板Y坐标
input int PanelSpacing = 30;        // 标签行间距
input color BullColor = clrGold;     // 多头标记颜色
input color BearColor = clrRed;      // 空头标记颜色
input int AlertInterval = 60;        // 警报间隔(秒)

// 全局变量
double consolidationHigh, consolidationLow;
bool isBullBreak = false, isBearBreak = false;
long chartID;
datetime lastAlertTime;
int adxHandle, atrHandle, fastMAHandle, slowMAHandle;
double adxValues[], atrValues[], fastMA[], slowMA[];
string dotNames[];  // 添加圆点管理数组声明
int maTrend; // 均线趋势方向 1:上涨 -1:下跌

int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();
   
   // 初始化指标句柄
   adxHandle = iADX(_Symbol, PERIOD_M1, ADX_Period);
   atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   fastMAHandle = iMA(_Symbol, PERIOD_M1, FastMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, PERIOD_M1, SlowMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   
   // 设置数组为时间序列
   ArraySetAsSeries(adxValues, true);
   ArraySetAsSeries(atrValues, true);
   ArraySetAsSeries(fastMA, true);
   ArraySetAsSeries(slowMA, true);
   
   return(INIT_SUCCEEDED);
}

void OnTick()
{
   // 计算最近N根K线的高低点
   CalculateConsolidationLevels();
   
   // 检测突破条件
   DetectBreakouts();
   
   // 更新信息面板
   UpdateInfoPanel();
}

void CalculateConsolidationLevels()
{
   consolidationHigh = -DBL_MAX;
   consolidationLow = DBL_MAX;
   
   for(int i=0; i<ConsolidationBars; i++)
   {
      consolidationHigh = MathMax(consolidationHigh, iHigh(_Symbol,PERIOD_M1,i));
      consolidationLow = MathMin(consolidationLow, iLow(_Symbol,PERIOD_M1,i));
   }
}

void DetectBreakouts()
{
   // 获取实时成交量
   double currentVolume = (double)iVolume(_Symbol, PERIOD_M1, 0);
   double prevVolume = (double)iVolume(_Symbol, PERIOD_M1, 1);
   
   // 获取指标数据
   CopyBuffer(adxHandle, 0, 0, 3, adxValues);
   CopyBuffer(atrHandle, 0, 0, 3, atrValues);
   CopyBuffer(fastMAHandle, 0, 0, 2, fastMA);
   CopyBuffer(slowMAHandle, 0, 0, 2, slowMA);
   
   // 计算均线趋势
   maTrend = (fastMA[0] > slowMA[0] && fastMA[1] > slowMA[1]) ? 1 : 
             (fastMA[0] < slowMA[0] && fastMA[1] < slowMA[1]) ? -1 : 0;

   double currentHigh = iHigh(_Symbol,PERIOD_M1,0);
   double currentLow = iLow(_Symbol,PERIOD_M1,0);
   
   // 波动性过滤条件
   bool volatilityFilter = (adxValues[0] < ADX_Threshold) && 
                          (atrValues[0] < (atrValues[1] * ATR_Multiplier)) &&
                          (currentVolume > prevVolume * VolumeThreshold);
   
   // 上破检测（需满足趋势方向）
   if( (currentHigh > consolidationHigh + BreakThreshold) 
   && volatilityFilter 
   && (maTrend >= 0)
   && IsPriceConfirmed(ConsolidationBars, true) )
   {
      isBullBreak = true;
      DrawBreakoutArrow(241, BullColor);
      LogSignal(StringFormat("Bullish Breakout ADX=%.1f MA=%.5f", adxValues[0], fastMA[0]));
   }
   
   // 下破检测（需满足趋势方向）
   if( (currentLow < consolidationLow - BreakThreshold) 
   && volatilityFilter 
   && (maTrend <= 0)
   && IsPriceConfirmed(ConsolidationBars, false) )
   {
      isBearBreak = true;
      DrawBreakoutArrow(242, BearColor);
      LogSignal(StringFormat("Bearish Breakout ADX=%.1f MA=%.5f", adxValues[0], fastMA[0]));
   }
}

void DrawBreakoutArrow(int arrowCode, color clr)
{
   string objName = "BreakArrow_"+TimeToString(TimeCurrent());
   ObjectCreate(chartID, objName, OBJ_ARROW, 0, TimeCurrent(), iClose(_Symbol,PERIOD_M1,0));
   ObjectSetInteger(chartID, objName, OBJPROP_ARROWCODE, arrowCode);
   ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clr);
   ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 2);
}

// 价格确认函数
bool IsPriceConfirmed(int barsToCheck, bool isBull)
{
   for(int i=0; i<ConfirmationBars; i++)
   {
      if(isBull && iClose(_Symbol, PERIOD_M1, i) < consolidationHigh)
         return false;
      if(!isBull && iClose(_Symbol, PERIOD_M1, i) > consolidationLow)
         return false;
   }
   return true;
}

void LogSignal(string message)
{
   if((TimeCurrent() - lastAlertTime) > AlertInterval)
   {
      Print(TimeToString(TimeCurrent())," ",message, " High:",DoubleToString(consolidationHigh,_Digits), " Low:",DoubleToString(consolidationLow,_Digits));
      Alert(message);
      lastAlertTime = TimeCurrent();
   }
}

// 创建信息面板
void CreateInfoPanel()
{
   // 背景矩形
   ObjectCreate(chartID, "BreakPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(chartID, "BreakPanelBG", OBJPROP_YSIZE, 100 + PanelSpacing*3);
   
   // 添加信息标签
   int baseOffset = 10;
   CreatePanelLabel("HighLabel", "当前区间高:", baseOffset);
   CreatePanelLabel("LowLabel", "当前区间低:", baseOffset + PanelSpacing);
   CreatePanelLabel("ADXLabel", "ADX值:", baseOffset + PanelSpacing*2);
   CreatePanelLabel("MAStatus", "均线趋势:", baseOffset + PanelSpacing*3);
   CreatePanelLabel("StatusLabel", "突破状态:", baseOffset + PanelSpacing*4);
}

void CreatePanelLabel(string name, string text, int yOffset)
{
   ObjectCreate(chartID, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, name, OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, name, OBJPROP_YDISTANCE, PanelY+yOffset);
   ObjectSetInteger(chartID, name, OBJPROP_COLOR, clrWhite);
   ObjectSetString(chartID, name, OBJPROP_TEXT, text);
}

void UpdateInfoPanel()
{
   ObjectSetString(chartID, "HighLabel", OBJPROP_TEXT, "当前区间高: "+DoubleToString(consolidationHigh,_Digits));
   ObjectSetString(chartID, "LowLabel", OBJPROP_TEXT, "当前区间低: "+DoubleToString(consolidationLow,_Digits));
   ObjectSetString(chartID, "ADXLabel", OBJPROP_TEXT, "ADX值: "+DoubleToString(adxValues[0],1));
   
   string maStatus = "均线趋势: ";
   switch((int)maTrend)
   {
      case 1: maStatus += "▲多头"; break;
      case -1: maStatus += "▼空头"; break;
      default: maStatus += "-震荡-";
   }
   ObjectSetString(chartID, "MAStatus", OBJPROP_TEXT, maStatus);
   
   string statusText = "突破状态: ";
   if(isBullBreak) statusText += "▲突破高点";
   else if(isBearBreak) statusText += "▼跌破低点";
   else statusText += "等待信号";
   
   ObjectSetString(chartID, "StatusLabel", OBJPROP_TEXT, statusText);
}

void OnDeinit(const int reason)
{
   ObjectDelete(chartID, "BreakPanelBG");
   ObjectDelete(chartID, "HighLabel");
   ObjectDelete(chartID, "LowLabel");
   ObjectDelete(chartID, "StatusLabel");
}