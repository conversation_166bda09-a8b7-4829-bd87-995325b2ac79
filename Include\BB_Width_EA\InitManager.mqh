//+------------------------------------------------------------------+
//|                                              InitManager.mqh     |
//+------------------------------------------------------------------+
#ifndef __INIT_MANAGER_MQH__
#define __INIT_MANAGER_MQH__

#include "Common.mqh"
#include "ConfigManager.mqh"

// 初始化EA参数
bool InitializeEAParameters()
{
   // 初始化参数变量（使用输入值作为默认值）
   Threshold_Width = Threshold_Width_Input;
   WeChat_Consec_Trigger = WeChat_Consec_Trigger_Input;
   
   // 检查是否有匹配的预设配置
   BBWidthConfig config;
   string currentSymbol = Symbol();
   
   // 获取基本品种名称（去除后缀）
   string baseSymbol = currentSymbol;
   int dotPos = StringFind(currentSymbol, ".");
   if(dotPos > 0) {
      baseSymbol = StringSubstr(currentSymbol, 0, dotPos);
      Print("当前品种带有后缀: ", currentSymbol, ", 基本名称: ", baseSymbol);
   }
   
   // 尝试查找匹配的配置
   bool configFound = false;
   
   // 遍历所有配置
   for(int i=0; i<g_configManager.GetCount(); i++) {
      BBWidthConfig cfg = g_configManager.GetConfig(i);
      // 使用配置结构体的Matches方法检查是否匹配
      if(cfg.Matches(currentSymbol, _Period)) {
         config = cfg;
         configFound = true;
         break;
      }
   }
   
   // 如果找到匹配的配置，应用配置参数
   if(configFound) {
      Print("===== BB_Width_EA 配置匹配结果 =====");
      Print("找到匹配的预设配置: ", config.ToString());
      
      if(dotPos > 0) {
         Print("当前品种带有后缀: ", currentSymbol);
         Print("匹配基本名称: ", baseSymbol);
      }
      
      Print("原始输入参数 - 宽度阈值: ", DoubleToString(Threshold_Width_Input, 5), 
            ", 连续次数: ", IntegerToString(WeChat_Consec_Trigger_Input));
      
      // 应用配置参数
      Threshold_Width = config.threshold;
      WeChat_Consec_Trigger = config.consecutive;
      
      Print("已应用预设参数 - 宽度阈值: ", DoubleToString(Threshold_Width, 5), 
            ", 连续次数: ", IntegerToString(WeChat_Consec_Trigger));
      Print("===================================");
   }
   else {
      Print("===== BB_Width_EA 配置匹配结果 =====");
      Print("未找到匹配的预设配置，使用输入参数");
      
      if(dotPos > 0) {
         Print("当前品种带有后缀: ", currentSymbol);
         Print("尝试匹配基本名称: ", baseSymbol, " 但未找到匹配配置");
      }
      
      Print("使用输入参数 - 宽度阈值: ", DoubleToString(Threshold_Width, 5), 
            ", 连续次数: ", IntegerToString(WeChat_Consec_Trigger));
      Print("===================================");
   }
   
   return configFound;
}

// 初始化指标句柄
bool InitializeIndicators()
{
   // 创建布林带指标句柄
   bbHandle = iBands(Symbol(), BB_TF, BB_Period, BB_Shift, BB_Deviation, APP_Price);
   if(bbHandle == INVALID_HANDLE) {
      Print("创建布林带指标失败");
      return false;
   }
   
   // 创建ATR指标句柄
   atrHandle = iATR(Symbol(), BB_TF, 14);
   if(atrHandle == INVALID_HANDLE) {
      Print("创建ATR指标失败");
      // 不返回失败，因为ATR不是关键指标
   }
   
   return true;
}

// 发送启动通知
bool SendStartupNotification(bool configFound)
{
   string message = StringFormat("EA Initialized Successfully\nName: BB_Width_EA\nSymbol: %s\nTimeframe: %s\nTime: %s",
                               Symbol(), EnumToString(_Period),
                               TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES|TIME_SECONDS));
   
   // 如果使用了预设配置，添加到通知中
   if(configFound) {
      message += StringFormat("\n\n已应用预设配置:\n宽度阈值: %.5f\n连续次数: %d", 
                             Threshold_Width, WeChat_Consec_Trigger);
   }
   
   if(!wechatManager.SendMessage(message))
   {
      Print("Warning: WeChat startup notification failed");
      return false;
   }
   
   return true;
}

#endif
