//+------------------------------------------------------------------+
//|                                                   HedgeMaster.mq5 |
//|                        Copyright 2023, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

input double LotSize = 0.1;        // 交易手数
input ulong  MagicNumber = 88888;  // 魔术码
input int    TimeoutBars = 10;     // 时间止损周期

datetime lastBarTime;
CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    trade.SetExpertMagicNumber(MagicNumber);
    lastBarTime = 0;
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!isNewBar()) return;
    
    MqlRates rates[];
    if(CopyRates(_Symbol, _Period, 0, 3, rates) < 3) return;
    
    DeletePendingOrders();
    CheckEntries(rates);
    CheckExits(rates);
}

//+------------------------------------------------------------------+
//| 检查新K线                                                        |
//+------------------------------------------------------------------+
bool isNewBar()
{
    datetime currentTime = iTime(_Symbol, _Period, 0);
    if(currentTime != lastBarTime) {
        lastBarTime = currentTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 删除所有挂单                                                     |
//+------------------------------------------------------------------+
void DeletePendingOrders()
{
    for(int i = OrdersTotal()-1; i >= 0; i--) {
        ulong ticket = OrderGetTicket(i);
        if(ticket == 0) continue;
        
        if(OrderGetString(ORDER_SYMBOL) == _Symbol && 
           OrderGetInteger(ORDER_MAGIC) == MagicNumber) {
            trade.OrderDelete(ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| 检查入场条件                                                     |
//+------------------------------------------------------------------+
void CheckEntries(MqlRates &rates[])
{
    // 做多条件
    if(rates[1].close < rates[1].open &&    // 阴线
       rates[1].close < rates[2].low) {     // 收盘低于前K最低
        double price = rates[1].high;
        trade.BuyStop(LotSize, price, _Symbol);
    }
    
    // 做空条件
    if(rates[1].close > rates[1].open &&    // 阳线
       rates[1].close > rates[2].high) {    // 收盘高于前K最高
        double price = rates[1].low;
        trade.SellStop(LotSize, price, _Symbol);
    }
}

//+------------------------------------------------------------------+
//| 检查出场条件                                                     |
//+------------------------------------------------------------------+
void CheckExits(MqlRates &rates[])
{
    for(int i = PositionsTotal()-1; i >= 0; i--) {
        ulong ticket = PositionGetTicket(i);
        if(ticket == 0) continue;
        
        if(PositionGetString(POSITION_SYMBOL) != _Symbol || 
           PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;
           
        double entryPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        datetime entryTime = (datetime)PositionGetInteger(POSITION_TIME);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        
        bool shouldClose = false;
        
        // 价格出场条件
        if((type == POSITION_TYPE_BUY && rates[1].close > entryPrice) ||
           (type == POSITION_TYPE_SELL && rates[1].close < entryPrice)) {
            shouldClose = true;
        }
        
        // 时间出场条件
        int barsHeld = Bars(_Symbol, _Period, entryTime, TimeCurrent());
        if(barsHeld >= TimeoutBars) {
            shouldClose = true;
        }
        
        if(shouldClose) {
            trade.PositionClose(ticket);
        }
    }
}
//+------------------------------------------------------------------+