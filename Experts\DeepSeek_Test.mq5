//+------------------------------------------------------------------+
//|                                  BTCUSD_EMA_RSI_Strategy.mq5     |
//|                                      Copyright 2023, Your Name   |
//|                                             https://www.yoursite.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, Your Name"
#property link      "https://www.yoursite.com"
#property version   "1.00"

input int      FastEMA = 9;           // 快速EMA周期
input int      SlowEMA = 21;          // 慢速EMA周期
input int      RSI_Period = 14;       // RSI周期
input double   LotSize = 0.1;         // 交易手数
input int      TradeStartHour = 8;    // 交易开始时间（服务器时间）
input int      TradeEndHour = 20;     // 交易结束时间（服务器时间）

int handleFastEMA, handleSlowEMA, handleRSI;
double fastEMA[], slowEMA[], rsi[];
datetime lastTradeTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化指标句柄（确保正确的时序顺序）
   handleFastEMA = iMA(_Symbol, PERIOD_H1, FastEMA, 0, MODE_EMA, PRICE_CLOSE);
   handleSlowEMA = iMA(_Symbol, PERIOD_H1, SlowEMA, 0, MODE_EMA, PRICE_CLOSE);
   handleRSI = iRSI(_Symbol, PERIOD_H1, RSI_Period, PRICE_CLOSE);
   
   // 设置数组为时间序列（最新数据在数组末尾）
   ArraySetAsSeries(fastEMA, true);
   ArraySetAsSeries(slowEMA, true);
   ArraySetAsSeries(rsi, true);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!IsMarketOpen(_Symbol))return;

   // 检查最小报价单位
   if(TickSizeCheck() < Point() * 10) // BTCUSD通常有较大波动
   {
      Print("Tick size too small, skipping execution");
      return;
   }

   // 获取指标数据（正确处理时序）
   if(CopyBuffer(handleFastEMA, 0, 0, 3, fastEMA) < 3 ||
      CopyBuffer(handleSlowEMA, 0, 0, 3, slowEMA) < 3 ||
      CopyBuffer(handleRSI, 0, 0, 3, rsi) < 3)
   {
      Print("Failed to copy indicator buffers");
      return;
   }

   // 检查交叉信号（注意时序顺序）
   bool buySignal = fastEMA[1] > slowEMA[1] && fastEMA[2] <= slowEMA[2] && rsi[1] < 70;
   bool sellSignal = fastEMA[1] < slowEMA[1] && fastEMA[2] >= slowEMA[2] && rsi[1] > 30;

   // 获取当前持仓
   bool longPosition = PositionSelect(_Symbol);
   bool shortPosition = false;
   if(!longPosition) shortPosition = PositionSelect(_Symbol);

   // 交易逻辑
   if(buySignal && !longPosition && IsNewBar())
   {
      ExecuteOrder(ORDER_TYPE_BUY);
   }
   else if(sellSignal && !shortPosition && IsNewBar())
   {
      ExecuteOrder(ORDER_TYPE_SELL);
   }
}

//+------------------------------------------------------------------+
//| 执行订单函数（处理市场填充）                                     |
//+------------------------------------------------------------------+
bool ExecuteOrder(ENUM_ORDER_TYPE orderType)
{
   MqlTradeRequest request = {};
   MqlTradeCheckResult checkResult = {};  // 修改为正确类型
   MqlTradeResult tradeResult = {};       // 保留原结果变量

   request.type_filling = GetFilling(_Symbol);
   request.action = TRADE_ACTION_DEAL;    // 补充必要字段
   request.symbol = _Symbol;
   request.volume = LotSize;
   request.type = orderType;
   request.price = SymbolInfoDouble(_Symbol, orderType == ORDER_TYPE_BUY ? SYMBOL_ASK : SYMBOL_BID);
   request.deviation = 10;

   // 使用 checkResult 进行订单检查
   if(!OrderCheck(request, checkResult))  // 修正参数类型
   {
      Print("Order check error: ", GetLastError());
      return false;
   }

   // 使用 tradeResult 进行订单发送
   if(!OrderSend(request, tradeResult))   // 保持原有逻辑
   {
      Print("Order send error: ", GetLastError());
      return false;
   }

   Print("Trade executed: ", tradeResult.price);
   return true;
}

//+------------------------------------------------------------------+
//| 检查是否是新K线                                                  |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   static datetime lastBarTime;
   datetime currentBarTime = iTime(_Symbol, PERIOD_H1, 0);
   
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查最小报价单位                                                 |
//+------------------------------------------------------------------+
double TickSizeCheck()
{
   MqlTick lastTick;
   if(SymbolInfoTick(_Symbol, lastTick))
   {
      return lastTick.ask - lastTick.bid;
   }
   return 0;
}

//+------------------------------------------------------------------+
//|  获取市场填充模式
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFilling(const string Symb, const ENUM_TRADE_REQUEST_ACTIONS action = TRADE_ACTION_DEAL, const ENUM_ORDER_TYPE_FILLING Type = ORDER_FILLING_FOK){
    const ENUM_SYMBOL_TRADE_EXECUTION ExeMode = (ENUM_SYMBOL_TRADE_EXECUTION)::SymbolInfoInteger(Symb, SYMBOL_TRADE_EXEMODE);
    const int FillingMode = (int)::SymbolInfoInteger(Symb, SYMBOL_FILLING_MODE);

    if(FillingMode == 0 || (Type >= ORDER_FILLING_RETURN) || ((FillingMode & (Type + 1)) != Type +1)){
        if((ExeMode == SYMBOL_TRADE_EXECUTION_EXCHANGE) ||(ExeMode == SYMBOL_TRADE_EXECUTION_INSTANT)){
            return ORDER_FILLING_RETURN;
        } else{
            if(FillingMode == SYMBOL_FILLING_IOC){
                return ORDER_FILLING_IOC;
            }else{
                return ORDER_FILLING_FOK;
            }
        }
    }else{
        return Type;
    }
}


//+------------------------------------------------------------------+
//|  获取市场开放时间
//+------------------------------------------------------------------+
bool IsMarketOpen(const string symbol, const bool debug = false){
    datetime from = NULL;
    datetime to = NULL;
    datetime serverTime = TimeTradeServer();

    MqlDateTime dt;
    TimeToStruct(serverTime, dt);
    const ENUM_DAY_OF_WEEK day_of_week = (ENUM_DAY_OF_WEEK) dt.day_of_week;
    const int time = (int) MathMod(serverTime, PeriodSeconds(PERIOD_D1));
    if( debug )PrintFormat("%s(%s): Checking %s", __FUNCTION__, symbol, EnumToString(day_of_week));
    int session = 2;
    while(session > -1){
        if(SymbolInfoSessionTrade(symbol, day_of_week, session, from, to)){
            if( debug ) PrintFormat("    %s(%s): Checking %d>=%d && %d<=%d", __FUNCTION__, symbol, time, from, time, to);

            const int sessionFrom = (int) from;
            const int sessionTo = (int) to;
            if(sessionFrom < sessionTo){
                if(time >=sessionFrom && time <= sessionTo){
                    if( debug ) PrintFormat("%s Market is open", __FUNCTION__);
                    return true;
                }

            }else{
                if(time >= sessionFrom || time <= sessionTo){
                    if( debug ) PrintFormat("%s Market is open", __FUNCTION__);
                    return true;
                }

            }
        }
        session--;
    }
    if ( debug ) PrintFormat("%s Market not open", __FUNCTION__);
    return false;
}
