//+------------------------------------------------------------------+
//|                                                  Common.mqh |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#ifndef __COMMON_MQH__
#define __COMMON_MQH__

#include <WeChatRobotManager.mqh>
#include "BBData.mqh"

// 定义常量
#define ERR_DIRECTORY_ALREADY_EXISTS 4301   // 目录已存在错误代码

//--- 全局变量
extern int bbHandle;
extern BBData currentBB;    // 当前K线数据
extern BBData prevBB;       // 前一K线数据
extern BBData prev2BB;      // 前二K线数据
extern string panelName;

//--- 连续状态跟踪
extern int lastConsecutive;     // 最近一次连续次数
extern int currentConsecutive;  // 当前连续次数
extern int maxConsecutive;      // 最长连续次数

extern double averageConsecutive; // 平均连续次数
extern int totalCycles;         // 总周期数
extern int totalOccurrences;    // 总触发次数
extern datetime lastBarTime;    // K线时间戳

//--- 通知管理器
extern CWeChatRobotManager wechatManager; // 微信机器人管理器实例
extern int atrHandle;  // ATR指标句柄

//--- 配置管理器前向声明
struct BBWidthConfig;
class CConfigManager;

#endif
