//+------------------------------------------------------------------+
//|                                                  BB_Width_EA.mq5 |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "ChatGPT/DeepSeek"
#property version   "1.00"
#property description "Bollinger Bands Width Monitor with Info Panel"

//--- 输入参数
input int      BB_Period = 20;           // 布林带周期
input double   BB_Deviation = 2.0;       // 标准差倍数
input int      BB_Shift = 0;             // 指标平移
input ENUM_APPLIED_PRICE APP_Price = PRICE_CLOSE; // 应用价格
input ENUM_TIMEFRAMES BB_TF = PERIOD_CURRENT; // 时间框架

//--- 面板参数
input int      Panel_Corner = 0;         // 面板位置（0-3）
input color    Panel_Text_Color = clrWhite; // 文本颜色
input color    Panel_Back_Color = clrSteelBlue; // 背景颜色
input string   Panel_Font = "Consolas";  // 面板字体

//--- 信号参数
input double   Threshold_Width_Input = 8.0;    // 宽度阈值
input color    Arrow_Color = clrGoldenrod; // 箭头颜色

//--- 通知参数
input bool     WeChat_Enabled = true;   // 启用微信提醒
input int      WeChat_Consec_Trigger_Input = 5; // 微信通知连续次数阈值
input string   WeChat_Webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 微信机器人Webhook地址

//--- 资源监控参数
input bool     ResourceMonitor_Enabled_Input = false;   // 启用资源监控

// 包含模块化组件
#include <BB_Width_EA\Common.mqh>
#include <BB_Width_EA\DataProcessor.mqh>
#include <BB_Width_EA\SignalDetector.mqh>
#include <BB_Width_EA\PanelManager.mqh>
#include <BB_Width_EA\NotificationManager.mqh>
#include <BB_Width_EA\UIComponents.mqh>
#include <BB_Width_EA\ConfigManager.mqh>
#include <BB_Width_EA\ResourceMonitor.mqh>
#include <BB_Width_EA\InitManager.mqh>
#include <BB_Width_EA\SignalProcessor.mqh>

// 初始化全局变量
int bbHandle = INVALID_HANDLE;
BBData currentBB;    // 当前K线数据
BBData prevBB;       // 前一K线数据
BBData prev2BB;      // 前二K线数据
string panelName = "BB_Width_Panel";

// 连续状态跟踪
int lastConsecutive = 0;     // 最近一次连续次数
int currentConsecutive = 0;  // 当前连续次数
int maxConsecutive = 0;      // 最长连续次数

double averageConsecutive = 0; // 平均连续次数
int totalCycles = 0;         // 总周期数
int totalOccurrences = 0;    // 总触发次数
datetime lastBarTime = 0;    // K线时间戳

// 通知管理器
CWeChatRobotManager wechatManager; // 微信机器人管理器实例
int atrHandle = INVALID_HANDLE;  // ATR指标句柄

// 配置管理器实例定义
CConfigManager g_configManager;

// 可修改的参数变量
double Threshold_Width;       // 实际使用的宽度阈值
int WeChat_Consec_Trigger;    // 实际使用的连续次数阈值

// 资源监控变量
datetime lastResourceLogTime = 0;  // 上次资源日志时间
bool ResourceMonitor_Enabled;      // 资源监控启用开关


//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化连续状态
   lastConsecutive = 0;
   currentConsecutive = 0;
   maxConsecutive = 0;
   lastBarTime = iTime(Symbol(), BB_TF, 0);

   // 初始化EA参数
   bool configFound = InitializeEAParameters();

   // 初始化指标句柄
   if(!InitializeIndicators()) {
      return(INIT_FAILED);
   }

   // 初始化微信机器人
   if(!wechatManager.Initialize(WeChat_Webhook)) {
      Print("警告: 微信机器人初始化失败，请检查Webhook地址");
   }

   // 创建信息面板
   CreateInfoPanel();

   // 设置定时器（1秒更新，减少资源消耗）
   EventSetTimer(1);

   // 发送启动通知
   SendStartupNotification(configFound);

   // 初始化资源监控
   ResourceMonitor_Enabled = ResourceMonitor_Enabled_Input;

   // 记录初始资源使用情况
   // 强制记录，不考虑时间间隔
   lastResourceLogTime = 0;
   LogResourceUsage();

   if(ResourceMonitor_Enabled) {
      Print("资源监控已启用，将每小时记录一次资源使用情况");
   } else {
      Print("资源监控已禁用");
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 定时器事件处理函数                                               |
//+------------------------------------------------------------------+
void OnTimer()
{
   static datetime lastSignalCheckTime = 0;
   datetime currentTime = TimeCurrent();

   // 优先更新数据（每次都执行）
   ProcessMarketData();

   // 更新信息面板（每次都执行）
   UpdateInfoPanel();

   // 记录资源使用情况（每小时一次）
   LogResourceUsage();

   // 处理新K线信号
   ProcessNewBarSignal();

   // 检查并创建信号箭头
   CheckAndCreateSignalArrow(currentTime, lastSignalCheckTime);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除所有图形对象
   ObjectsDeleteAll(0, panelName);
   Comment("");

   // 释放指标句柄
   if(bbHandle != INVALID_HANDLE) {
      IndicatorRelease(bbHandle);
      bbHandle = INVALID_HANDLE;
   }

   if(atrHandle != INVALID_HANDLE) {
      IndicatorRelease(atrHandle);
      atrHandle = INVALID_HANDLE;
   }

   // 停止定时器
   EventKillTimer();

   // 记录最终资源使用情况
   LogFinalResourceUsage();

   Print("EA已卸载，所有资源已释放");
}
