MQL5 箭头符号代码参考表
============================

常用的箭头符号代码（用于PLOT_ARROW属性）：

圆点和圆形符号：
- 159: 实心圆点 ●
- 167: 空心圆圈 ○
- 233: 实心菱形 ♦
- 234: 空心菱形 ♢

箭头符号：
- 241: 向上箭头 ↑
- 242: 向下箭头 ↓
- 217: 向上三角 ▲
- 218: 向下三角 ▼

星形符号：
- 73: 星号 *
- 42: 星号 *
- 164: 实心星 ★
- 169: 空心星 ☆

方形符号：
- 110: 实心方块 ■
- 111: 空心方块 □
- 168: 小方块 ▪

特殊符号：
- 82: 字母R
- 88: 字母X
- 79: 字母O
- 43: 加号 +
- 45: 减号 -

使用建议：
- 斜率零点标记：推荐使用 159（实心圆点）或 167（空心圆圈）
- 局部最大值：推荐使用 217（向上三角）
- 局部最小值：推荐使用 218（向下三角）
- 重要转折点：推荐使用 233（实心菱形）

注意：不同的字体可能显示不同的符号，建议在实际图表中测试效果。
