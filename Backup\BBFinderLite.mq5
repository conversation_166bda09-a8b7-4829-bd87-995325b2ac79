//+------------------------------------------------------------------+
//|                                              BBFinderLite.mq5   |
//|                                  Copyright 2023, FXDreema Team  |
//|                                        https://www.fxdreema.com/ |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, FXDreema Team"
#property link      "https://www.fxdreema.com"
#property version   "2.40"
#property description "布林通道箱体形态可视化指示器（支持多矩形）"
#property script_show_inputs  // 修正属性声明
#include <Arrays\ArrayObj.mqh>

//--- 输入参数
input int      BBPeriod = 20;            // 布林带周期
input double   Deviation = 2.0;          // 标准差倍数
input double   WidthThreshold = 0.004;   // 箱体宽度阈值（价格百分比）
input color    BoxColor = clrLightGoldenrod;  // 箱体区域颜色
input int      Transparency = 50;        // 透明度 (0-100)
input int      MaxBoxes = 100;           // 最大保留箱体数
input int      MinBars = 5;              // 箱体最小K线数量

//--- 全局变量
int bbHandle;
double upper[], middle[], lower[];
datetime lastBarTime;

//+------------------------------------------------------------------+
//| BoxObject 类定义                                                |
//+------------------------------------------------------------------+
class BoxObject : public CObject
{
public:
   datetime startTime;
   datetime endTime;
   double   upperBand;
   double   lowerBand;
   double   highPrice;  // 新增最高价属性
   double   lowPrice;   // 新增最低价属性
   string   objName;
   bool     isActive;
   int      barsCount;

   BoxObject(datetime st, double up, double low) : startTime(st), endTime(st),
                                                   upperBand(up), lowerBand(low),
                                                   isActive(true), barsCount(1)
   {
      objName = "BoxArea_" + TimeToString(st) + "_" + IntegerToString(ChartID());
      highPrice = up;  // 初始化最高价
      lowPrice = low;  // 初始化最低价
   }
   
   void Update(datetime currentTime, double newUp, double newLow)
   {
      endTime = currentTime;
      upperBand = MathMax(upperBand, newUp);
      lowerBand = MathMin(lowerBand, newLow);
      highPrice = MathMax(highPrice, newUp);  // 更新最高价
      lowPrice = MathMin(lowPrice, newLow);    // 更新最低价
      barsCount++;
      RedrawObject();
   }
   
   void CreateVisual()
   {
      if(ObjectFind(0, objName) < 0)
      {
         ObjectCreate(0, objName, OBJ_RECTANGLE, 0, startTime, upperBand, endTime, lowerBand);
         ObjectSetInteger(0, objName, OBJPROP_COLOR, ColorToARGB(BoxColor,(uchar)(255*(100-Transparency)/100)));
         ObjectSetInteger(0, objName, OBJPROP_BACK, true);
         ObjectSetInteger(0, objName, OBJPROP_FILL, false);
      }
      
      // 创建文本标注
      string textName = objName + "_Text";
      double boxHeight = upperBand - lowerBand;
      if(ObjectFind(0, textName) < 0) {
         ObjectCreate(0, textName, OBJ_TEXT, 0, endTime, upperBand);
         ObjectSetInteger(0, textName, OBJPROP_ANCHOR, ANCHOR_RIGHT_UPPER);
         ObjectSetInteger(0, textName, OBJPROP_COLOR, clrWhite);
         ObjectSetInteger(0, textName, OBJPROP_FONTSIZE, 8);
         ObjectSetInteger(0, textName, OBJPROP_BACK, false);
         ObjectSetString(0, textName, OBJPROP_TEXT, StringFormat("H:%.2f L:%.2f\nΔ:%.2f", highPrice, lowPrice, highPrice-lowPrice));
         ObjectSetInteger(0, textName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, textName, OBJPROP_HIDDEN, true);
      }
   }

private:
   void RedrawObject()
   {
      if(ObjectFind(0, objName) >= 0)
      {
         ObjectSetInteger(0, objName, OBJPROP_TIME, 0, startTime);
         ObjectSetInteger(0, objName, OBJPROP_TIME, 1, endTime);
         ObjectSetDouble(0, objName, OBJPROP_PRICE, 0, upperBand);
         ObjectSetDouble(0, objName, OBJPROP_PRICE, 1, lowerBand);
      }
   }
   
   private:
   static int outputCounter;
   
   public:
   void PrintBoxProperties()
   {
      string timeFormat = "yyyy.MM.dd HH:mm:ss";
      double boxHeight = upperBand - lowerBand;  // 使用布林带上下轨计算箱体高度
      double boxDuration = (endTime - startTime) / 60.0; // 转换为分钟
      double priceRange = highPrice - lowPrice;  // 使用最高最低价计算价格波动范围
      
      // 每5行输出一次标题
      if(outputCounter % 5 == 0)
      {
         Print("开始时间,结束时间,持续时间(分钟),最高价,最低价,价格差值,箱体高度,K线数量");
      }
      
      Print(StringFormat("%s,%s,%d,%.3f,%.3f,%.3f,%.3f,%d",
         TimeToString(startTime, timeFormat),
         TimeToString(endTime, timeFormat),
         (int)boxDuration,
         highPrice,
         lowPrice,
         priceRange,
         boxHeight,
         barsCount
      ));
      
      outputCounter++;
      if(outputCounter >= 1000) outputCounter = 0; // 防止计数器溢出
   }
};

// 初始化BoxObject的静态计数器
int BoxObject::outputCounter = 0;

CArrayObj BoxList;

//+------------------------------------------------------------------+
//| 修正后的箱体检测逻辑                                            |
//+------------------------------------------------------------------+
void CheckBoxFormation()
{
   if(CopyBuffer(bbHandle,1,1,3,upper) <3 || 
      CopyBuffer(bbHandle,0,1,3,middle)<3 || 
      CopyBuffer(bbHandle,2,1,3,lower)<3) return;

   datetime currentTime = iTime(_Symbol,_Period,1);
   double currentClose = iClose(_Symbol,_Period,1);

   bool isBoxNow = (upper[0]-lower[0])/middle[0] < WidthThreshold &&
                  currentClose < upper[0] &&
                  currentClose > lower[0];

   bool activeBoxFound = false;
   for(int i=BoxList.Total()-1; i>=0; i--)
   {
      BoxObject* box = BoxList.At(i);
      if(box == NULL) continue;

      if(box.isActive)
      {
         if(isBoxNow)
         {
            box.Update(currentTime, upper[0], lower[0]);
            activeBoxFound = true;
            
            if(box.barsCount >= MinBars && ObjectFind(0, box.objName) < 0)
            {
               box.CreateVisual();
            }
         }
         else
         {
            box.isActive = false;
            if(box.barsCount < MinBars)
            {
               ObjectDelete(0, box.objName);
ObjectDelete(0, box.objName + "_Text");
               BoxList.Delete(i);
               delete box;
            }
            else
            {
               box.PrintBoxProperties(); // 在箱体结束时输出属性信息
            }
         }
      }
   }

   if(isBoxNow && !activeBoxFound)
   {
      BoxObject* newBox = new BoxObject(currentTime, upper[0], lower[0]);
      if(newBox != NULL) BoxList.Add(newBox);

      // 修正数组清理逻辑
      int validCount = 0;
      int keepIndex = -1;
      for(int i=BoxList.Total()-1; i>=0; i--)
      {
         BoxObject* box = BoxList.At(i);
         if(box == NULL) continue;
         
         if(box.barsCount >= MinBars) validCount++;
         if(validCount >= MaxBoxes)
         {
            keepIndex = i;
            break;
         }
      }
      
      if(keepIndex > 0)
      {
         for(int j=0; j<=keepIndex; j++)
         {
            BoxObject* old = BoxList.At(j);
            if(old != NULL)
            {
               ObjectDelete(0, old.objName);
               delete old;
            }
         }
         for(int j=0; j<=keepIndex; j++) {
            BoxList.Delete(j);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 初始化函数                                                       |
//+------------------------------------------------------------------+
int OnInit()
{
   bbHandle = iBands(_Symbol,_Period,BBPeriod,0,Deviation,PRICE_CLOSE);
   if(bbHandle == INVALID_HANDLE) return(INIT_FAILED);
   
   ArraySetAsSeries(upper,true);
   ArraySetAsSeries(middle,true);
   ArraySetAsSeries(lower,true);
   
   ObjectsDeleteAll(0,"BoxArea_");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 主循环函数                                                       |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime currentBarTime[1];
   if(CopyTime(_Symbol, _Period, 0, 1, currentBarTime) != 1) return;
   
   // 如果K线时间未变化（未生成新K线），直接返回
   if(currentBarTime[0] == lastBarTime) return;
   
   // 更新最后检测时间并执行箱体检测
   lastBarTime = currentBarTime[0];
   CheckBoxFormation();
}

//+------------------------------------------------------------------+
//| 清理函数                                                         |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   for(int i=BoxList.Total()-1; i>=0; i--)
   {
      BoxObject* box = BoxList.At(i);
      if(box != NULL)
      {
         ObjectDelete(0, box.objName);
ObjectDelete(0, box.objName + "_Text");
         delete box;
      }
   }
   BoxList.Clear();
   Comment("");
}