//+------------------------------------------------------------------+
//|                                                  WeChatRobotManager.mqh |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#property strict
#define SW_HIDE 0

class CWeChatRobotManager
{
private:
   string m_webhookUrl;
public:
   bool Initialize(string webhookUrl)
   {
      if(webhookUrl == "") return false;
      m_webhookUrl = webhookUrl;
      return true;
   }

   bool SendMessage(string message)
   {
      string headers = "Content-Type: application/json";
      string escapedMsg = "";
      for(int i=0; i<StringLen(message); i++) {
          ushort c = StringGetCharacter(message, i);
          if(c > 127) {
              escapedMsg += StringFormat("\\u%04x", c);
          } else {
              escapedMsg += CharToString((uchar)c);
          }
      }
      string postData = StringFormat("{\"msgtype\":\"text\",\"text\":{\"content\":\"%s\"}}", escapedMsg);

      return WebRequestWrapper(postData, "text");
   }

   //+------------------------------------------------------------------+
   //| 发送图片消息                                                      |
   //+------------------------------------------------------------------+
   bool SendImage(string imagePath)
   {
      // 读取图片文件为Base64编码
      string base64Image = ReadFileToBase64(imagePath);
      if(base64Image == "")
      {
         Print("无法读取图片文件");
         return false;
      }

      // 构建JSON请求体
      string json = StringFormat(
         "{\"msgtype\":\"image\",\"image\":{\"base64\":\"%s\",\"md5\":\"%s\"}}",
         base64Image,
         GetFileMD5(imagePath)
      );

      // 发送HTTP请求
      return WebRequestWrapper(json, "image");
   }

private:
   bool WebRequestWrapper(string postData, string msgType)
   {
      string headers = "Content-Type: application/json";

      uchar responseData[];
      string headersReturn;
      uchar postArray[];
      StringToCharArray(postData, postArray, 0, StringLen(postData));
      int result = WebRequest("POST", m_webhookUrl, headers, 5000, postArray, responseData, headersReturn);

      // 显式释放不再需要的数组
      ArrayFree(postArray);

      if(result == -1)
      {
         int errorCode = GetLastError();
         Print("发送微信消息失败[详细]：", "错误码:", errorCode, " 响应头:", headersReturn, " 原始数据:", CharArrayToString(responseData));
         ArrayFree(responseData); // 释放响应数据数组
         return false;
      }

      ArrayFree(responseData); // 释放响应数据数组
      return true;
   }

   //+------------------------------------------------------------------+
   //| 读取文件并转换为Base64编码                                        |
   //+------------------------------------------------------------------+
   string ReadFileToBase64(string filePath)
   {
      int handle = FileOpen(filePath, FILE_READ|FILE_BIN);
      if(handle == INVALID_HANDLE)
      {
         Print("无法打开文件: ", filePath, " 错误码: ", GetLastError());
         return "";
      }

      ulong fileSize = FileSize(handle);
      uchar buffer[];
      ArrayResize(buffer, (int)fileSize);

      uint bytesRead = FileReadArray(handle, buffer, 0, (int)fileSize);
      FileClose(handle);

      if(bytesRead != fileSize)
      {
         Print("读取文件不完全: ", filePath);
         ArrayFree(buffer); // 释放缓冲区
         return "";
      }

      // Base64编码
      uchar key[], base64Data[];
      if(CryptEncode(CRYPT_BASE64, buffer, key, base64Data) <= 0)
      {
         Print("Base64编码失败");
         ArrayFree(buffer); // 释放缓冲区
         return "";
      }

      // 获取结果并释放数组
      string result = CharArrayToString(base64Data);
      ArrayFree(buffer);
      ArrayFree(base64Data);

      return result;
   }

   //+------------------------------------------------------------------+
   //| 计算文件的MD5哈希值                                              |
   //+------------------------------------------------------------------+
   string GetFileMD5(string filePath)
   {
      int handle = FileOpen(filePath, FILE_READ|FILE_BIN);
      if(handle == INVALID_HANDLE)
      {
         Print("无法打开文件: ", filePath, " 错误码: ", GetLastError());
         return "";
      }

      ulong fileSize = FileSize(handle);
      uchar buffer[];
      ArrayResize(buffer, (int)fileSize);

      uint bytesRead = FileReadArray(handle, buffer, 0, (int)fileSize);
      FileClose(handle);

      if(bytesRead != fileSize)
      {
         Print("读取文件不完全: ", filePath);
         ArrayFree(buffer); // 释放缓冲区
         return "";
      }

      // MD5计算
      uchar key[], hash[16];
      if(CryptEncode(CRYPT_HASH_MD5, buffer, key, hash) != 16)
      {
         Print("MD5计算失败");
         ArrayFree(buffer); // 释放缓冲区
         return "";
      }

      string md5 = "";
      for(int i = 0; i < 16; i++)
      {
         md5 += StringFormat("%02x", hash[i]);
      }

      // 释放缓冲区
      ArrayFree(buffer);

      return md5;
   }
};