# BB_Width_EA Technical Reference

## Code Architecture

BB_Width_EA is designed with a modular architecture, separating functionality into multiple `.mqh` files for better organization and maintainability. This document provides a technical overview of the EA's structure and components.

## File Structure

### Main EA File
- `Experts\BB_Width_EA.mq5`: Main EA file that includes all modules and defines global variables

### Module Files (in `Include\BB_Width_EA\` directory)
- `BBData.mqh`: Defines the BBData structure for storing Bollinger Bands data
- `Common.mqh`: Contains common definitions, external variables, and includes
- `ConfigManager.mqh`: Manages preset configurations for different symbols and timeframes
- `DataProcessor.mqh`: Handles market data processing and Bollinger Bands calculations
- `InitManager.mqh`: Manages EA initialization and startup procedures
- `NotificationManager.mqh`: Handles notifications and screenshot functionality
- `PanelManager.mqh`: Manages the information panel creation and updates
- `ResourceMonitor.mqh`: Monitors and logs resource usage
- `SignalDetector.mqh`: Detects signals based on Bollinger Bands width
- `SignalProcessor.mqh`: Processes signals and triggers notifications
- `UIComponents.mqh`: Contains UI component creation and management functions

### External Dependencies
- `Include\WeChatRobotManager.mqh`: Handles WeChat robot communication

## Key Components

### BBData Structure
```cpp
struct BBData {
   double upper;    // Upper band
   double middle;   // Middle band
   double lower;    // Lower band
   double width;    // Band width
   double open;     // Open price
   double high;     // High price
   double low;      // Low price
   double close;    // Close price
   double atr;      // ATR value
   long volume;     // Volume
   bool isWritten;  // Data written flag
   datetime timestamp; // Timestamp
   
   void Calculate(double u, double m, double l, double o, double h, double l_price, double c, double a, long v);
};
```

### Configuration System
```cpp
struct BBWidthConfig {
   string symbol;           // Trading symbol
   ENUM_TIMEFRAMES timeframe; // Timeframe
   double threshold;        // Width threshold
   int consecutive;         // Consecutive count
   
   BBWidthConfig(string s="", ENUM_TIMEFRAMES tf=PERIOD_CURRENT, double th=0.0, int cons=0);
   bool Matches(string s, ENUM_TIMEFRAMES tf);
   string GetBaseSymbol(string s);
};

class CConfigManager {
private:
   BBWidthConfig m_configs[MAX_CONFIGS];
   int m_count;
   
   bool LoadFromFile();
   void SaveToFile();
   
public:
   CConfigManager();
   void Initialize();
   void AddDefaultConfigs();
   bool AddConfig(string symbol, ENUM_TIMEFRAMES timeframe, double threshold, int consecutive);
   BBWidthConfig GetConfig(int index);
   int GetCount();
   bool FindConfig(string symbol, ENUM_TIMEFRAMES timeframe, BBWidthConfig &config);
};
```

### WeChat Robot Manager
```cpp
class CWeChatRobotManager {
private:
   string m_webhookUrl;
   bool WebRequestWrapper(string postData, string msgType);
   string ReadFileToBase64(string filePath);
   string GetFileMD5(string filePath);
   
public:
   bool Initialize(string webhookUrl);
   bool SendMessage(string message);
   bool SendImage(string imagePath);
};
```

## Core Functionality

### Signal Detection Logic
The EA detects signals when the Bollinger Bands width falls below the set threshold:
```cpp
bool ValidateConditions() {
   bool result = prevBB.width > 0 && prevBB.width < Threshold_Width;
   return result;
}
```

### Consecutive Signal Tracking
```cpp
void ProcessNewBarSignal() {
   datetime currentBarTime = iTime(Symbol(), BB_TF, 0);
   
   if(currentBarTime != lastBarTime) {
      bool previousConditionMet = (prevBB.width > 0 && prevBB.width < Threshold_Width);
      
      if(previousConditionMet) {
         currentConsecutive++;
         maxConsecutive = MathMax(currentConsecutive, maxConsecutive);
         
         if(currentConsecutive == WeChat_Consec_Trigger) {
            SendBBWidthAlert(false); // Signal start notification
         }
      } else {
         if(currentConsecutive >= WeChat_Consec_Trigger) {
            SendBBWidthAlert(true); // Signal end notification
         }
         
         if(currentConsecutive > 0) {
            totalOccurrences++;
            lastConsecutive = currentConsecutive;
            totalCycles++;
            averageConsecutive = (averageConsecutive*(totalCycles-1) + currentConsecutive)/totalCycles;
         }
         currentConsecutive = 0;
      }
      lastBarTime = currentBarTime;
   }
}
```

### Data Processing
```cpp
void ProcessMarketData() {
   double upper[3], middle[3], lower[3];
   double open[3], high[3], low[3], close[3];
   double atr[3];
   long volume[3];

   if(lastBarTime != iTime(Symbol(), BB_TF, 0)) {
      prevBB.isWritten = false;
   }

   if(!GetBollingerBandsData(upper, middle, lower)) {
      Print("Failed to get Bollinger Bands data!");
      return;
   }

   // Get ATR indicator data
   if(atrHandle != INVALID_HANDLE) {
      CopyBuffer(atrHandle, 0, 0, 3, atr);
   } else {
      ArrayInitialize(atr, 0);
   }

   CopyTickVolume(Symbol(), BB_TF, 0, 3, volume);
   CopyOHLC(open, high, low, close);

   UpdateBBStructure(currentBB, upper[2], middle[2], lower[2], open[2], high[2], low[2], close[2], atr[2], volume[2]);
   UpdateBBStructure(prevBB, upper[1], middle[1], lower[1], open[1], high[1], low[1], close[1], atr[1], volume[1]);
   UpdateBBStructure(prev2BB, upper[0], middle[0], lower[0], open[0], high[0], low[0], close[0], atr[0], volume[0]);
}
```

### Notification System
```cpp
void SendBBWidthAlert(bool isEndSignal = false) {
   datetime triggerTime = TimeCurrent();
   
   // Prepare WeChat message body
   ChartSetInteger(0, CHART_SHOW, CHART_SHOW_DATE_SCALE, true);
   ChartSetInteger(0, CHART_SHOW, CHART_SHOW_PRICE_SCALE, true);
   ChartNavigate(0, CHART_END, -30);
   string body = "";
   
   // Add alert information
   if(isEndSignal) {
      body += "\n🔴🔴🔴 Bollinger Bands Width Signal End 🔴🔴🔴\n\n";
   } else {
      body += "\n🟢🟢🟢 Bollinger Bands Width Signal Start 🟢🟢🟢\n\n";
   }
   
   // Add symbol, timeframe, time information
   // Add parameter settings
   // Add consecutive status
   // Add current data
   
   // Send notification
   if(WeChat_Enabled) {
      // Send text message
      wechatManager.SendMessage(body);
      
      // Ensure screenshot directory exists
      // Generate filename
      // Take screenshot
      // Send image message
   }
}
```

### CSV Data Recording
```cpp
void WriteBBDataToCSV(datetime time, double price, BBData &data) {
   // Generate filename based on current week
   // Check if file exists, create with headers if not
   // Open file and append data
   // Mark data as written
}
```

## Event Handlers

### OnInit
- Initializes indicator handles
- Loads configuration
- Creates information panel
- Sets up timer
- Sends startup notification
- Initializes resource monitoring

### OnTimer
- Updates market data
- Updates information panel
- Logs resource usage
- Processes new bar signals
- Checks and creates signal arrows

### OnDeinit
- Deletes all graphic objects
- Releases indicator handles
- Stops timer
- Logs final resource usage

## Customization Points

### Adding New Configurations
To add new symbol/timeframe configurations:
1. Edit the `AddDefaultConfigs()` method in `ConfigManager.mqh`
2. Or create/edit the `BB_Width_Configs.csv` file in the common files directory

### Modifying UI
To modify the information panel:
1. Edit the `CreateInfoPanel()` method in `PanelManager.mqh`
2. Edit the `CreateBBLabels()` method in `UIComponents.mqh`

### Changing Signal Conditions
To modify signal detection logic:
1. Edit the `ValidateConditions()` method in `SignalDetector.mqh`

### Customizing Notifications
To modify notification content:
1. Edit the `SendBBWidthAlert()` method in `NotificationManager.mqh`

## Performance Considerations

- The EA uses a 1-second timer for updates, which provides responsive UI but may increase resource usage
- Arrow objects are limited to 500 to prevent excessive memory usage
- Resource monitoring is optional and can be disabled to reduce overhead
- Screenshot functionality may temporarily increase memory usage

## Error Handling

The EA includes error handling for:
- Indicator initialization failures
- File operations (CSV writing, screenshot saving)
- WeChat communication errors
- Directory creation

## Dependencies

- MetaTrader 5 API
- Built-in indicators (Bollinger Bands, ATR)
- WeChat enterprise robot API (for notifications)

## Extending the EA

To add new features to the EA:
1. Create a new `.mqh` file in the `Include\BB_Width_EA\` directory
2. Implement the new functionality
3. Include the new file in `BB_Width_EA.mq5`
4. Add necessary global variables and function calls
