//+------------------------------------------------------------------+
//|                                          LondonBreakout_EA.mq5 |
//|                                                                |
//|                                                                |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property description "伦敦钟突破策略EA"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- 全局交易对象
CTrade g_trade;
CPositionInfo g_positionInfo;
COrderInfo g_orderInfo;

//--- 枚举定义
enum ENUM_BREAKOUT_MODE {
    MODE_SINGLE_TRADE = 0,    // 每日仅执行一单
    MODE_REVERSE_TRADE = 1    // 反向突破平仓并开新单
};

enum ENUM_TRADING_DAYS {
    MONDAY = 1,       // 星期一
    TUESDAY = 2,      // 星期二
    WEDNESDAY = 4,    // 星期三
    THURSDAY = 8,     // 星期四
    FRIDAY = 16,      // 星期五
    WEEKDAYS = 31     // 所有工作日
};

//--- 输入参数
input group "时间设置"
input datetime StartTime = D'2023.01.09 09:00:00';  // 开始时间
input datetime EndTime = D'2023.01.10 06:00:00';    // 结束时间
input datetime CloseTime = D'2023.01.10 17:00:00';  // 平仓时间
input bool AutoUpdateTime = true;                   // 自动更新时间

input group "交易设置"
input double LotSize = 0.03;                        // 交易手数
input ENUM_BREAKOUT_MODE BreakoutMode = MODE_SINGLE_TRADE; // 突破模式
input int TradingDays = WEEKDAYS;                  // 交易日

input group "风险管理"
input double StopLossMultiplier = 1.0;              // 止损倍数
input double TakeProfitMultiplier = 4.0;            // 止盈倍数

input group "显示设置"
input color RectangleColor = clrLightGreen;         // 矩形颜色
input color HighLineColor = clrRed;                // 高点线颜色
input color LowLineColor = clrBlue;                // 低点线颜色
input color CloseTimeLineColor = clrBlack;         // 平仓时间线颜色

//--- 全局变量
datetime g_currentStartTime;
datetime g_currentEndTime;
datetime g_currentCloseTime;
bool g_isRangeCalculationPeriod = false;
bool g_isBreakoutWaitingPeriod = false;
bool g_isPositionOpened = false;
double g_highestPrice = 0;
double g_lowestPrice = 0;
datetime g_highestPriceTime = 0;
datetime g_lowestPriceTime = 0;
string g_rectangleName = "LB_Rectangle";
string g_highLineName = "LB_HighLine";
string g_lowLineName = "LB_LowLine";
string g_closeTimeLine = "LB_CloseTimeLine";
int g_magicNumber = 20240601;
int g_lastTradeType = -1; // -1: 无交易, 0: 买入, 1: 卖出

//+------------------------------------------------------------------+
//| 将服务器时间转换为北京时间                                        |
//+------------------------------------------------------------------+
datetime ServerToBeiJingTime(datetime serverTime)
{
    // 北京时间比GMT快8小时
    return serverTime + 8 * 3600;
}

//+------------------------------------------------------------------+
//| 将北京时间转换为服务器时间                                        |
//+------------------------------------------------------------------+
datetime BeiJingToServerTime(datetime beijingTime)
{
    // 服务器时间比北京时间慢8小时
    return beijingTime - 8 * 3600;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化交易对象
    g_trade.SetExpertMagicNumber(g_magicNumber);
    
    // 初始化时间参数
    InitializeTimeParameters();
    
    // 删除所有图形对象
    DeleteAllObjects();
    
    Print("伦敦钟突破策略EA初始化成功");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 注意：根据需求，不删除图形对象
    Print("伦敦钟突破策略EA已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 更新状态信息
    Comment(GetStatusInfo());
    
    // 检查是否需要更新时间参数
    CheckTimeUpdate();
    
    // 获取当前时间
    datetime currentTime = TimeCurrent();
    
    // 检查是否在交易日
    if(!IsAllowedTradingDay())
    {
        return;
    }
    
    // 1. 统计区间：计算最高价和最低价
    if(currentTime >= g_currentStartTime && currentTime < g_currentEndTime)
    {
        g_isRangeCalculationPeriod = true;
        g_isBreakoutWaitingPeriod = false;
        CalculateHighLow();
        return;
    }
    
    // 2. 突破区间：等待价格突破
    if(currentTime >= g_currentEndTime && currentTime < g_currentCloseTime)
    {
        // 如果刚从统计区间进入突破区间，初始化突破等待
        if(g_isRangeCalculationPeriod && !g_isBreakoutWaitingPeriod)
        {
            g_isRangeCalculationPeriod = false;
            g_isBreakoutWaitingPeriod = true;
            
            // 绘制高低价格线
            DrawPriceLines();
        }
        
        // 检查突破
        CheckBreakout();
        return;
    }
    
    // 3. 平仓区间：强制平仓
    if(currentTime >= g_currentCloseTime)
    {
        // 如果刚从突破区间进入平仓区间，绘制平仓线
        if(g_isBreakoutWaitingPeriod)
        {
            g_isBreakoutWaitingPeriod = false;
            DrawCloseTimeLine();
        }
        
        // 平掉所有持仓
        CloseAllPositions();
        
        // 重置交易状态
        g_isPositionOpened = false;
        g_lastTradeType = -1;
        
        // 检查是否需要更新时间参数（平仓时间后1小时）
        if(AutoUpdateTime && currentTime >= (g_currentCloseTime + 3600))
        {
            UpdateTimeParameters();
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化时间参数                                                    |
//+------------------------------------------------------------------+
void InitializeTimeParameters()
{
    g_currentStartTime = StartTime;
    g_currentEndTime = EndTime;
    g_currentCloseTime = CloseTime;
    
    // 如果启用自动更新，则立即更新时间参数
    if(AutoUpdateTime)
    {
        UpdateTimeParameters();
    }
}

//+------------------------------------------------------------------+
//| 更新时间参数                                                      |
//+------------------------------------------------------------------+
void UpdateTimeParameters()
{
    // 获取当前时间
    MqlDateTime now;
    TimeToStruct(TimeCurrent(), now);
    
    // 设置亚洲盘开盘时间（北京时间08:00，对应服务器时间00:00）
    MqlDateTime asiaOpen;
    TimeToStruct(TimeCurrent(), asiaOpen);
    asiaOpen.hour = 0;
    asiaOpen.min = 0;
    asiaOpen.sec = 0;
    g_currentStartTime = StructToTime(asiaOpen);
    
    // 设置伦敦盘开始（北京时间16:00，对应服务器时间08:00）
    MqlDateTime londonPreOpen;
    TimeToStruct(TimeCurrent(), londonPreOpen);
    londonPreOpen.hour = 8;
    londonPreOpen.min = 0;
    londonPreOpen.sec = 0;
    g_currentEndTime = StructToTime(londonPreOpen);
    
    // 设置平仓时间（北京时间01:00，对应服务器时间17:00）
    MqlDateTime closeTime;
    TimeToStruct(TimeCurrent(), closeTime);
    closeTime.hour = 17;
    closeTime.min = 0;
    closeTime.sec = 0;
    g_currentCloseTime = StructToTime(closeTime);
    
    // 重置状态
    g_isRangeCalculationPeriod = false;
    g_isBreakoutWaitingPeriod = false;
    g_isPositionOpened = false;
    g_highestPrice = 0;
    g_lowestPrice = 0;
    g_lastTradeType = -1;
    
    Print("时间参数已更新 - 开始时间: ", TimeToString(ServerToBeiJingTime(g_currentStartTime)), " (北京时间), 结束时间: ", 
          TimeToString(ServerToBeiJingTime(g_currentEndTime)), " (北京时间), 平仓时间: ", TimeToString(ServerToBeiJingTime(g_currentCloseTime)), " (北京时间)");
}

//+------------------------------------------------------------------+
//| 检查是否需要更新时间参数                                          |
//+------------------------------------------------------------------+
void CheckTimeUpdate()
{
    if(!AutoUpdateTime) return;
    
    datetime currentTime = TimeCurrent();
    
    // 如果当前时间已经超过平仓时间1小时，且不在任何活动期间，则更新时间参数
    if(currentTime >= (g_currentCloseTime + 3600) && 
       !g_isRangeCalculationPeriod && !g_isBreakoutWaitingPeriod)
    {
        UpdateTimeParameters();
    }
}

//+------------------------------------------------------------------+
//| 计算最高价和最低价                                                |
//+------------------------------------------------------------------+
void CalculateHighLow()
{
    // 获取当前价格
    double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double currentLow = iLow(_Symbol, PERIOD_CURRENT, 0);
    
    // 初始化最高价和最低价
    if(g_highestPrice == 0 || g_lowestPrice == 0)
    {
        g_highestPrice = currentHigh;
        g_lowestPrice = currentLow;
        g_highestPriceTime = iTime(_Symbol, PERIOD_CURRENT, 0);
        g_lowestPriceTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    }
    
    // 更新最高价
    if(currentHigh > g_highestPrice)
    {
        g_highestPrice = currentHigh;
        g_highestPriceTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    }
    
    // 更新最低价
    if(currentLow < g_lowestPrice)
    {
        g_lowestPrice = currentLow;
        g_lowestPriceTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    }
    
    // 绘制矩形
    DrawRectangle();
}

//+------------------------------------------------------------------+
//| 检查价格突破                                                      |
//+------------------------------------------------------------------+
void CheckBreakout()
{
    // 如果最高价或最低价未设置，则返回
    if(g_highestPrice == 0 || g_lowestPrice == 0) return;
    
    // 获取当前价格
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_LAST);
    double currentBid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double currentAsk = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    
    // 计算止损止盈距离
    double range = g_highestPrice - g_lowestPrice;
    double stopLossDistance = range * StopLossMultiplier;
    double takeProfitDistance = range * TakeProfitMultiplier;
    
    // 检查突破最高价（做多）
    if(currentPrice > g_highestPrice)
    {
        // 如果已经有多头持仓，则不再开仓
        if(g_lastTradeType == 0 && BreakoutMode == MODE_SINGLE_TRADE) return;
        
        // 如果有空头持仓且模式为反向交易，则平仓
        if(g_lastTradeType == 1 && BreakoutMode == MODE_REVERSE_TRADE)
        {
            CloseAllPositions();
        }
        
        // 开多单
        double stopLoss = currentAsk - stopLossDistance;
        double takeProfit = currentAsk + takeProfitDistance;
        
        if(g_trade.Buy(LotSize, _Symbol, currentAsk, stopLoss, takeProfit, "LondonBreakout"))
        {
            g_isPositionOpened = true;
            g_lastTradeType = 0; // 标记为多头
            Print("突破最高价，开多单 - 价格: ", currentAsk, ", 止损: ", stopLoss, ", 止盈: ", takeProfit);
        }
    }
    // 检查突破最低价（做空）
    else if(currentPrice < g_lowestPrice)
    {
        // 如果已经有空头持仓，则不再开仓
        if(g_lastTradeType == 1 && BreakoutMode == MODE_SINGLE_TRADE) return;
        
        // 如果有多头持仓且模式为反向交易，则平仓
        if(g_lastTradeType == 0 && BreakoutMode == MODE_REVERSE_TRADE)
        {
            CloseAllPositions();
        }
        
        // 开空单
        double stopLoss = currentBid + stopLossDistance;
        double takeProfit = currentBid - takeProfitDistance;
        
        if(g_trade.Sell(LotSize, _Symbol, currentBid, stopLoss, takeProfit, "LondonBreakout"))
        {
            g_isPositionOpened = true;
            g_lastTradeType = 1; // 标记为空头
            Print("突破最低价，开空单 - 价格: ", currentBid, ", 止损: ", stopLoss, ", 止盈: ", takeProfit);
        }
    }
}

//+------------------------------------------------------------------+
//| 平掉所有持仓                                                      |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(g_positionInfo.SelectByIndex(i))
        {
            if(g_positionInfo.Symbol() == _Symbol && g_positionInfo.Magic() == g_magicNumber)
            {
                g_trade.PositionClose(g_positionInfo.Ticket());
                Print("平仓 - 票号: ", g_positionInfo.Ticket());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 绘制矩形                                                          |
//+------------------------------------------------------------------+
void DrawRectangle()
{
    // 删除旧矩形
    ObjectDelete(0, g_rectangleName);
    
    // 创建新矩形
    if(!ObjectCreate(0, g_rectangleName, OBJ_RECTANGLE, 0, g_currentStartTime, g_lowestPrice, 
                    TimeCurrent(), g_highestPrice))
    {
        Print("创建矩形失败: ", GetLastError());
        return;
    }
    
    // 设置矩形属性
    ObjectSetInteger(0, g_rectangleName, OBJPROP_COLOR, RectangleColor);
    ObjectSetInteger(0, g_rectangleName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, g_rectangleName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, g_rectangleName, OBJPROP_FILL, true);
    ObjectSetInteger(0, g_rectangleName, OBJPROP_BACK, true);
    ObjectSetString(0, g_rectangleName, OBJPROP_TOOLTIP, "伦敦钟突破区间");
    
    // 刷新图表
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 绘制价格线                                                        |
//+------------------------------------------------------------------+
void DrawPriceLines()
{
    // 删除旧线
    ObjectDelete(0, g_highLineName);
    ObjectDelete(0, g_lowLineName);
    
    // 创建高价线
    if(!ObjectCreate(0, g_highLineName, OBJ_HLINE, 0, 0, g_highestPrice))
    {
        Print("创建高价线失败: ", GetLastError());
    }
    else
    {
        ObjectSetInteger(0, g_highLineName, OBJPROP_COLOR, HighLineColor);
        ObjectSetInteger(0, g_highLineName, OBJPROP_STYLE, STYLE_DASH);
        ObjectSetInteger(0, g_highLineName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, g_highLineName, OBJPROP_TOOLTIP, "最高价: " + DoubleToString(g_highestPrice, _Digits));
    }
    
    // 创建低价线
    if(!ObjectCreate(0, g_lowLineName, OBJ_HLINE, 0, 0, g_lowestPrice))
    {
        Print("创建低价线失败: ", GetLastError());
    }
    else
    {
        ObjectSetInteger(0, g_lowLineName, OBJPROP_COLOR, LowLineColor);
        ObjectSetInteger(0, g_lowLineName, OBJPROP_STYLE, STYLE_DASH);
        ObjectSetInteger(0, g_lowLineName, OBJPROP_WIDTH, 1);
        ObjectSetString(0, g_lowLineName, OBJPROP_TOOLTIP, "最低价: " + DoubleToString(g_lowestPrice, _Digits));
    }
    
    // 刷新图表
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 绘制平仓时间线                                                    |
//+------------------------------------------------------------------+
void DrawCloseTimeLine()
{
    // 删除旧线
    ObjectDelete(0, g_closeTimeLine);
    
    // 创建平仓时间线
    if(!ObjectCreate(0, g_closeTimeLine, OBJ_VLINE, 0, g_currentCloseTime, 0))
    {
        Print("创建平仓时间线失败: ", GetLastError());
        return;
    }
    
    // 设置线属性
    ObjectSetInteger(0, g_closeTimeLine, OBJPROP_COLOR, CloseTimeLineColor);
    ObjectSetInteger(0, g_closeTimeLine, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, g_closeTimeLine, OBJPROP_WIDTH, 1);
    ObjectSetString(0, g_closeTimeLine, OBJPROP_TOOLTIP, "平仓时间: " + TimeToString(ServerToBeiJingTime(g_currentCloseTime)));
    
    // 刷新图表
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 删除所有图形对象                                                  |
//+------------------------------------------------------------------+
void DeleteAllObjects()
{
    ObjectDelete(0, g_rectangleName);
    ObjectDelete(0, g_highLineName);
    ObjectDelete(0, g_lowLineName);
    ObjectDelete(0, g_closeTimeLine);
}

//+------------------------------------------------------------------+
//| 获取状态信息                                                      |
//+------------------------------------------------------------------+
string GetStatusInfo()
{
    string info = "伦敦钟突破策略EA\n";
    info += "当前时间: " + TimeToString(ServerToBeiJingTime(TimeCurrent())) + " (北京时间)\n";
    info += "开始时间: " + TimeToString(ServerToBeiJingTime(g_currentStartTime)) + " (北京时间)\n";
    info += "结束时间: " + TimeToString(ServerToBeiJingTime(g_currentEndTime)) + " (北京时间)\n";
    info += "平仓时间: " + TimeToString(ServerToBeiJingTime(g_currentCloseTime)) + " (北京时间)\n\n";
    
    if(g_isRangeCalculationPeriod)
    {
        info += "当前状态: 统计区间高低点\n";
        info += "最高价: " + DoubleToString(g_highestPrice, _Digits) + "\n";
        info += "最低价: " + DoubleToString(g_lowestPrice, _Digits) + "\n";
    }
    else if(g_isBreakoutWaitingPeriod)
    {
        info += "当前状态: 等待突破\n";
        info += "突破上轨: " + DoubleToString(g_highestPrice, _Digits) + "\n";
        info += "突破下轨: " + DoubleToString(g_lowestPrice, _Digits) + "\n";
    }
    else
    {
        info += "当前状态: 等待下一交易周期\n";
    }
    
    return info;
}

//+------------------------------------------------------------------+
//| 检查是否为允许交易的日期                                          |
//+------------------------------------------------------------------+
bool IsAllowedTradingDay()
{
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);
    
    int dayOfWeek = time.day_of_week; // 0-周日, 1-周一, ..., 6-周六
    
    // 检查当前星期几是否在允许交易的日期中
    if(dayOfWeek == 0 || dayOfWeek == 6) // 周末不交易
    {
        return false;
    }
    
    // 检查是否在选定的交易日中
    int dayFlag = 1 << (dayOfWeek - 1); // 转换为位标志
    return (TradingDays & dayFlag) != 0;
}