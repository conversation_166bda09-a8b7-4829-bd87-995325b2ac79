//+------------------------------------------------------------------+
//|                 企业微信机器人图片发送脚本                        |
//+------------------------------------------------------------------+
#property script_show_inputs

// 输入参数
// 输入参数
input string WebhookURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 企业微信机器人Webhook地址
// 注意：现在截图保存在以CSV文件名命名的目录中，例如：BB_Width_Data_EURUSD_PERIOD_H1_Week2345_2023.01.01_2023.01.07
input string ImagePath  = "C:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\Common\\Files\\BB_Width_Data_EURUSD_PERIOD_H1_Week2345_2023.01.01_2023.01.07\\BB_Alert_ETHUSDm_PERIOD_M1_20250523_050400.png"; // 要发送的图片路径

//+------------------------------------------------------------------+
//| 脚本程序起始函数                                                  |
//+------------------------------------------------------------------+
void OnStart()
{
    // 检查文件是否存在
    if(!FileIsExist(ImagePath))
    {
        MessageBox("图片文件不存在: " + ImagePath, "错误", MB_ICONERROR);
        return;
    }

    // 读取图片文件为Base64编码
    string base64Image = ReadFileToBase64(ImagePath);
    if(base64Image == "")
    {
        MessageBox("无法读取图片文件", "错误", MB_ICONERROR);
        return;
    }

    // 构建JSON请求体
    string json = StringFormat(
        "{\"msgtype\":\"image\",\"image\":{\"base64\":\"%s\",\"md5\":\"%s\"}}",
        base64Image,
        GetFileMD5(ImagePath)
    );

    // 发送HTTP请求
    char data[], result[];
    string headers = "Content-Type: application/json";

    StringToCharArray(json, data, 0, StringLen(json));

    int res = WebRequest(
        "POST",
        WebhookURL,
        headers,
        5000,
        data,
        result,
        headers
    );

    // 处理结果
    if(res == -1)
    {
        MessageBox("WebRequest失败，错误: " + IntegerToString(GetLastError()), "错误", MB_ICONERROR);
    }
    else
    {
        string response = CharArrayToString(result);
        Print("企业微信响应: ", response);
        MessageBox("图片发送成功!", "成功", MB_ICONINFORMATION);
    }
}

//+------------------------------------------------------------------+
//| 读取文件并转换为Base64编码                                        |
//+------------------------------------------------------------------+
string ReadFileToBase64(string filePath)
{
    int handle = FileOpen(filePath, FILE_READ|FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法打开文件: ", filePath, " 错误: ", GetLastError());
        return "";
    }

    ulong fileSize = FileSize(handle);
    uchar buffer[];
    ArrayResize(buffer, (int)fileSize);

    uint bytesRead = FileReadArray(handle, buffer, 0, (int)fileSize);
    FileClose(handle);

    if(bytesRead != fileSize)
    {
        Print("读取文件不完全: ", filePath);
        return "";
    }

    // 修正的CryptEncode调用方式
    uchar base64Data[];
    if(CryptEncode(CRYPT_BASE64, buffer, base64Data) <= 0)
    {
        Print("Base64编码失败");
        return "";
    }

    return CharArrayToString(base64Data);
}

//+------------------------------------------------------------------+
//| 计算文件的MD5哈希值                                              |
//+------------------------------------------------------------------+
string GetFileMD5(string filePath)
{
    int handle = FileOpen(filePath, FILE_READ|FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法打开文件: ", filePath, " 错误: ", GetLastError());
        return "";
    }

    ulong fileSize = FileSize(handle);
    uchar buffer[];
    ArrayResize(buffer, (int)fileSize);

    uint bytesRead = FileReadArray(handle, buffer, 0, (int)fileSize);
    FileClose(handle);

    if(bytesRead != fileSize)
    {
        Print("读取文件不完全: ", filePath);
        return "";
    }

    // 修正的MD5计算方式
    uchar hash[16];
    if(CryptEncode(CRYPT_HASH_MD5, buffer, hash) != 16)
    {
        Print("MD5计算失败");
        return "";
    }

    string md5 = "";
    for(int i = 0; i < 16; i++)
    {
        md5 += StringFormat("%02x", hash[i]);
    }

    return md5;
}