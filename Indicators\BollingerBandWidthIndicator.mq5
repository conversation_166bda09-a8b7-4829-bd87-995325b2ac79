//+------------------------------------------------------------------+
//|                                     BollingerBandWidthIndicator.mq5       |
//|                        Copyright 2023, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.20"
#property description "布林带宽度指标 - 可视化上下轨差值"
#property indicator_separate_window
#property indicator_buffers 8
#property indicator_plots   5
#property indicator_label1  "原始带宽"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrDodgerBlue
#property indicator_width1  2
#property indicator_label2  "平滑带宽"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrGold
#property indicator_width2  2
#property indicator_label3  "历史高位"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrRed
#property indicator_width3  1
#property indicator_label4  "历史低位"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrLime
#property indicator_width4  1
#property indicator_label5  "斜率零点"
#property indicator_type5   DRAW_ARROW
#property indicator_color5  clrMagenta
#property indicator_width5  3

// 输入参数
input int      BBPeriod = 20;            // 布林带周期，BTC-H4-30；
input double   Deviations = 2.0;         // 标准差倍数
input int      SmoothPeriod = 5;         // 平滑周期(0=禁用)
input int      HistoryBars = 1000;       // 历史分析柱数
input bool     ShowPercentile = true;    // 显示百分位水平
input bool     ShowExtremes = true;      // 显示历史高低位
input bool     ShowAlerts = false;        // 显示警报
input bool     ShowDebugInfo = false;     // 显示调试信息
input bool     ShowZeroSlope = false;      // 显示斜率零点
input int      SlopePeriod = 3;           // 斜率计算周期
input double   SlopeThreshold = 0.01;     // 斜率阈值（接近零的范围）
input int      ArrowCode = 159;           // 箭头符号代码 (159=圆点, 233=菱形, 167=圆圈)
input ENUM_APPLIED_PRICE ApplyTo = PRICE_CLOSE; // 应用价格

// 指标缓冲区
double BandWidthBuffer[];
double SmoothedBuffer[];
double HighLevelBuffer[];
double LowLevelBuffer[];
double ZeroSlopeBuffer[];
double Percentile90[];
double Percentile10[];
double MedianBuffer[];

// 全局变量
int handleBB;
double highestBand = 0, lowestBand = DBL_MAX;
datetime lastAlertTime;
// 参数变化检测
int lastBBPeriod = 0;
double lastDeviations = 0;
int lastHistoryBars = 0;

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                              |
//+------------------------------------------------------------------+
int OnInit()
{
   // 设置指标缓冲区
   SetIndexBuffer(0, BandWidthBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SmoothedBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, HighLevelBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, LowLevelBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, ZeroSlopeBuffer, INDICATOR_DATA);
   SetIndexBuffer(5, Percentile90, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, Percentile10, INDICATOR_CALCULATIONS);
   SetIndexBuffer(7, MedianBuffer, INDICATOR_CALCULATIONS);

   // 设置斜率零点的箭头符号
   PlotIndexSetInteger(4, PLOT_ARROW, ArrowCode); // 使用用户选择的符号
   PlotIndexSetString(4, PLOT_LABEL, "斜率零点");
   PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 4); // 设置符号大小

   // 设置指标属性
   IndicatorSetString(INDICATOR_SHORTNAME, "BB Width(" + string(BBPeriod) + ")");
   IndicatorSetInteger(INDICATOR_DIGITS, 5);
   IndicatorSetInteger(INDICATOR_LEVELS, 3);
   IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, 0.0);

   // 设置水平线值
   if(ShowPercentile)
   {
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, 0.0);
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 2, 0.0);
      IndicatorSetInteger(INDICATOR_LEVELCOLOR, 1, clrMagenta);
      IndicatorSetInteger(INDICATOR_LEVELCOLOR, 2, clrCyan);
      IndicatorSetInteger(INDICATOR_LEVELSTYLE, 1, STYLE_DOT);
      IndicatorSetInteger(INDICATOR_LEVELSTYLE, 2, STYLE_DOT);
   }

   // 获取布林带句柄
   handleBB = iBands(_Symbol, _Period, BBPeriod, 0, Deviations, ApplyTo);
   if(handleBB == INVALID_HANDLE)
   {
      Print("布林带指标初始化失败");
      return INIT_FAILED;
   }

   // 不设置数组为时间序列，使用正向索引
   // 这样可以避免索引混乱问题

   // 初始化极值
   highestBand = 0;
   lowestBand = DBL_MAX;
   lastAlertTime = 0;

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 自定义指标迭代函数                                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // 检查可用数据
   if(rates_total < BBPeriod + 10)
      return 0;

   // 检测参数变化
   bool parametersChanged = (lastBBPeriod != BBPeriod ||
                            lastDeviations != Deviations ||
                            lastHistoryBars != HistoryBars);

   if(parametersChanged)
   {
      // 参数变化时重新创建布林带句柄
      if(handleBB != INVALID_HANDLE)
         IndicatorRelease(handleBB);

      handleBB = iBands(_Symbol, _Period, BBPeriod, 0, Deviations, ApplyTo);
      if(handleBB == INVALID_HANDLE)
      {
         Print("布林带指标重新初始化失败");
         return 0;
      }

      // 更新参数记录
      lastBBPeriod = BBPeriod;
      lastDeviations = Deviations;
      lastHistoryBars = HistoryBars;

      // 重置统计数据
      highestBand = 0;
      lowestBand = DBL_MAX;

      if(ShowDebugInfo)
         Print("检测到参数变化，重新初始化指标 - BBPeriod:", BBPeriod, ", Deviations:", Deviations);
   }

   // 复制布林带数据
   double upper[], lower[], middle[];
   ArrayResize(upper, rates_total);
   ArrayResize(lower, rates_total);
   ArrayResize(middle, rates_total);

   if(CopyBuffer(handleBB, 1, 0, rates_total, upper) <= 0 ||
      CopyBuffer(handleBB, 2, 0, rates_total, lower) <= 0 ||
      CopyBuffer(handleBB, 0, 0, rates_total, middle) <= 0)
      return 0;

   // 计算起始位置 - 优化参数变化时的处理
   int start = 0;
   if(prev_calculated > BBPeriod)
   {
      start = prev_calculated - 1;
   }
   else
   {
      // 参数变化或首次计算时，从布林带周期开始
      start = BBPeriod;
      // 重置极值，避免参数变化时的异常显示
      highestBand = 0;
      lowestBand = DBL_MAX;
   }
   
   // 主循环
   for(int i = start; i < rates_total && !IsStopped(); i++)
   {
      // 1. 计算原始带宽 --------------------------------------------------
      double width = upper[i] - lower[i];
      double relativeWidth = 0;

      // 防止除零错误
      if(middle[i] > 0)
         relativeWidth = (width / middle[i]) * 100; // 百分比宽度
      else
         relativeWidth = 0;

      // 存储原始带宽
      BandWidthBuffer[i] = relativeWidth;

      // 调试信息：显示近期数值
      if(ShowDebugInfo && i >= rates_total - 5)
      {
         Print("Bar[", i, "] Width=", DoubleToString(width, 5),
               " Middle=", DoubleToString(middle[i], 5),
               " RelativeWidth=", DoubleToString(relativeWidth, 2), "%");
      }

      // 2. 平滑带宽 (可选) ----------------------------------------------
      if(SmoothPeriod > 1 && i >= SmoothPeriod - 1)
      {
         double sum = 0;
         int count = 0;
         for(int j = 0; j < SmoothPeriod && (i - j) >= 0; j++)
         {
            sum += BandWidthBuffer[i - j];
            count++;
         }
         SmoothedBuffer[i] = (count > 0) ? sum / count : relativeWidth;
      }
      else
      {
         SmoothedBuffer[i] = relativeWidth;
      }

      // 2.5 斜率零点检测 ------------------------------------------------
      ZeroSlopeBuffer[i] = EMPTY_VALUE; // 默认不显示

      if(ShowZeroSlope && i >= SlopePeriod && SlopePeriod >= 2)
      {
         // 使用线性回归方法计算局部斜率
         double slope = CalculateLocalSlope(SmoothedBuffer, i, SlopePeriod);

         // 检测斜率是否接近零
         if(MathAbs(slope) <= SlopeThreshold)
         {
            // 使用专门的函数验证是否为局部极值点
            bool isLocalExtreme = IsLocalExtreme(SmoothedBuffer, i, 2);

            // 如果是局部极值点，标记该点
            if(isLocalExtreme)
            {
               double currentValue = SmoothedBuffer[i];
               ZeroSlopeBuffer[i] = currentValue;

               if(ShowDebugInfo)
               {
                  Print("检测到斜率零点 - Bar[", i, "] 值:", DoubleToString(currentValue, 2),
                        "% 斜率:", DoubleToString(slope, 4));
               }
            }
         }
      }

      // 3. 计算统计值 (优化历史数据收集逻辑) ---------------------------------
      // 确保有足够的有效数据进行统计计算
      int minDataRequired = MathMax(BBPeriod + 10, 50); // 至少需要50个有效数据点
      if(i >= minDataRequired && HistoryBars > 0)
      {
         // 3.1 收集历史数据 - 只收集有效的计算结果
         double history[];
         int maxHistorySize = MathMin(HistoryBars, i - BBPeriod + 1);
         ArrayResize(history, maxHistorySize);
         int count = 0;

         // 从第一个有效计算位置开始收集数据
         int startIdx = MathMax(BBPeriod, i - maxHistorySize + 1);
         for(int j = startIdx; j <= i && count < maxHistorySize; j++)
         {
            // 只收集有效的带宽数据（非零值）
            if(BandWidthBuffer[j] > 0)
            {
               history[count++] = BandWidthBuffer[j];
            }
         }

         if(count >= 10) // 至少需要10个数据点才进行统计计算
         {
            // 3.2 调整数组大小并排序
            ArrayResize(history, count);  // 调整到实际数据大小
            ArraySort(history);           // 排序整个数组

            // 3.3 计算百分位（使用更稳健的计算方法）
            int idx90 = (int)MathMax(0, MathMin(count - 1, MathRound((count - 1) * 0.90)));
            int idx10 = (int)MathMax(0, MathMin(count - 1, MathRound((count - 1) * 0.10)));
            int idx50 = (int)MathMax(0, MathMin(count - 1, MathRound((count - 1) * 0.50)));

            Percentile90[i] = history[idx90];
            Percentile10[i] = history[idx10];
            MedianBuffer[i] = history[idx50];
         }
         else
         {
            // 数据不足时，使用当前值或前一个有效值
            if(i > BBPeriod && Percentile90[i-1] > 0)
            {
               Percentile90[i] = Percentile90[i-1];
               Percentile10[i] = Percentile10[i-1];
               MedianBuffer[i] = MedianBuffer[i-1];
            }
            else
            {
               Percentile90[i] = relativeWidth;
               Percentile10[i] = relativeWidth;
               MedianBuffer[i] = relativeWidth;
            }
         }

         // 3.4 更新历史高低位
         if(ShowExtremes)
         {
            // 初始化极值缓冲区
            HighLevelBuffer[i] = EMPTY_VALUE;
            LowLevelBuffer[i] = EMPTY_VALUE;

            // 更新全局极值
            if(relativeWidth > highestBand)
            {
               highestBand = relativeWidth;
               HighLevelBuffer[i] = relativeWidth;
            }

            if(relativeWidth < lowestBand)
            {
               lowestBand = relativeWidth;
               LowLevelBuffer[i] = relativeWidth;
            }
         }
      }
      else
      {
         // 数据不足时使用当前值
         Percentile90[i] = relativeWidth;
         Percentile10[i] = relativeWidth;
         MedianBuffer[i] = relativeWidth;
         HighLevelBuffer[i] = EMPTY_VALUE;
         LowLevelBuffer[i] = EMPTY_VALUE;
      }
         
      // 4. 警报系统 -------------------------------------------------
      if(ShowAlerts && i == rates_total - 1 && time[i] != lastAlertTime)
      {
         // 极高值警报
         if(relativeWidth > Percentile90[i] && Percentile90[i] > 0)
         {
            Alert("布林带宽度极高: ", _Symbol, " ", EnumToString(_Period),
                  "\n当前: ", DoubleToString(relativeWidth, 2), "%",
                  "\n90%分位: ", DoubleToString(Percentile90[i], 2), "%");
            lastAlertTime = time[i];
         }
         // 极低值警报
         else if(relativeWidth < Percentile10[i] && Percentile10[i] > 0)
         {
            Alert("布林带宽度极低: ", _Symbol, " ", EnumToString(_Period),
                  "\n当前: ", DoubleToString(relativeWidth, 2), "%",
                  "\n10%分位: ", DoubleToString(Percentile10[i], 2), "%");
            lastAlertTime = time[i];
         }
      }
   }

   // 更新水平线值
   if(ShowPercentile && rates_total > HistoryBars)
   {
      int lastIdx = rates_total - 1;
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, Percentile90[lastIdx]);
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 2, Percentile10[lastIdx]);
      IndicatorSetString(INDICATOR_LEVELTEXT, 1, "90%分位: " + DoubleToString(Percentile90[lastIdx], 2));
      IndicatorSetString(INDICATOR_LEVELTEXT, 2, "10%分位: " + DoubleToString(Percentile10[lastIdx], 2));
   }

   return rates_total;
}

//+------------------------------------------------------------------+
//| 指标去初始化函数                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清除图表对象
   ObjectsDeleteAll(0, "BBWidth_");
}

//+------------------------------------------------------------------+
//| 计算局部斜率（使用线性回归方法）                                 |
//+------------------------------------------------------------------+
double CalculateLocalSlope(const double &buffer[], int currentIndex, int period)
{
   if(currentIndex < period - 1 || period < 2)
      return 0.0;

   // 线性回归计算斜率
   double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
   int n = period;

   for(int i = 0; i < period; i++)
   {
      int bufferIndex = currentIndex - period + 1 + i;
      if(bufferIndex < 0) continue;

      double x = i;  // 时间序列 (0, 1, 2, ...)
      double y = buffer[bufferIndex];  // 对应的数值

      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumX2 += x * x;
   }

   // 计算斜率: slope = (n*ΣXY - ΣX*ΣY) / (n*ΣX² - (ΣX)²)
   double denominator = n * sumX2 - sumX * sumX;
   if(MathAbs(denominator) < 0.0001)
      return 0.0;

   double slope = (n * sumXY - sumX * sumY) / denominator;
   return slope;
}

//+------------------------------------------------------------------+
//| 检测局部极值点                                                   |
//+------------------------------------------------------------------+
bool IsLocalExtreme(const double &buffer[], int currentIndex, int lookback = 2)
{
   if(currentIndex < lookback || currentIndex >= ArraySize(buffer) - lookback)
      return false;

   double currentValue = buffer[currentIndex];
   bool isMaximum = true;
   bool isMinimum = true;

   // 检查左右两边的值
   for(int i = 1; i <= lookback; i++)
   {
      if(buffer[currentIndex - i] >= currentValue)
         isMaximum = false;
      if(buffer[currentIndex + i] >= currentValue)
         isMaximum = false;

      if(buffer[currentIndex - i] <= currentValue)
         isMinimum = false;
      if(buffer[currentIndex + i] <= currentValue)
         isMinimum = false;
   }

   return (isMaximum || isMinimum);
}