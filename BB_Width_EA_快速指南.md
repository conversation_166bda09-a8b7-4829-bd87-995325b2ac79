# BB_Width_EA 快速指南

## 功能概述

BB_Width_EA是一个专为MetaTrader 5平台开发的布林带宽度监控工具，能够识别布林带宽度低于设定阈值的情况，并通过微信机器人发送通知。这种情况通常表示市场波动性降低，可能即将出现重要的价格突破。

## 主要特点

- ✅ **实时监控**：监控布林带宽度，当宽度低于阈值时标记信号
- ✅ **信号追踪**：记录连续信号次数，包括当前、最近和历史最高连续次数
- ✅ **可视化界面**：信息面板显示布林带数据、价格信息和K线倒计时
- ✅ **微信通知**：通过企业微信机器人发送文本和截图通知
- ✅ **自动配置**：根据交易品种和时间周期自动应用最佳参数
- ✅ **数据记录**：将信号数据保存到CSV文件，按周自动分割

## 安装方法

1. 将`Experts\BB_Width_EA.mq5`复制到MT5的`Experts`目录
2. 将`Include\BB_Width_EA`文件夹复制到MT5的`Include`目录
3. 将`Include\WeChatRobotManager.mqh`复制到MT5的`Include`目录
4. 在MT5中编译并附加到图表

**快速安装**：使用PowerShell运行`install.ps1`脚本

## 参数设置

### 关键参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| BB_Period | 20 | 布林带周期 |
| BB_Deviation | 2.0 | 标准差倍数 |
| Threshold_Width_Input | 8.0 | 宽度阈值 |
| WeChat_Enabled | true | 启用微信提醒 |
| WeChat_Consec_Trigger_Input | 5 | 微信通知连续次数阈值 |
| WeChat_Webhook | (URL) | 微信机器人Webhook地址 |

## 信息面板

信息面板显示以下内容：
- 前二K线、前一K线和当前K线的布林带数据
- OHLC价格、ATR和成交量
- 当前阈值和连续状态（当前连续、最近连续、历史最高）
- K线收盘倒计时

## 信号条件和通知

1. **信号条件**：布林带宽度低于设定阈值
2. **信号标记**：在图表下轨处显示箭头
3. **微信通知**：
   - 当连续信号次数达到阈值时发送"信号开始"通知
   - 当连续信号结束时发送"信号结束"通知
   - 通知包含交易品种、时间周期、参数设置、连续状态和实时数据
   - 同时发送图表截图

## 预设配置

EA内置以下默认配置：

| 品种 | 时间周期 | 宽度阈值 | 连续次数 |
|------|---------|---------|---------|
| XAUUSD | M5 | 6.5 | 5 |
| EURUSD | M15 | 0.00050 | 5 |
| EURUSD | H1 | 0.00080 | 3 |
| GBPUSD | M15 | 0.00060 | 5 |
| USDJPY | H1 | 0.080 | 3 |

可通过编辑`BB_Width_Configs.csv`文件添加自定义配置。

## 数据记录

EA将信号数据保存到CSV文件中，文件位于MT5的公共文件目录：
```
%APPDATA%\MetaQuotes\Terminal\Common\Files\BB_Width_Data_[品种]_[时间周期]_Week[周数]_[日期范围].csv
```

截图保存在与CSV文件同名的目录中。

## 常见问题

1. **微信通知不工作**
   - 检查WeChat_Webhook地址是否正确
   - 确认网络连接正常

2. **找不到信号**
   - 调整Threshold_Width_Input参数
   - 使用预设配置系统

3. **截图保存失败**
   - 确保MT5有足够权限创建文件
   - 检查磁盘空间

## 使用技巧

1. **针对不同品种优化参数**：使用预设配置系统为不同品种设置最佳参数
2. **结合其他指标**：将BB_Width_EA的信号与其他指标结合使用，提高准确性
3. **定期检查CSV数据**：分析历史信号数据，优化交易策略
4. **调整通知阈值**：根据交易品种的特性调整WeChat_Consec_Trigger_Input参数
