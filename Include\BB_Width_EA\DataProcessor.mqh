//+------------------------------------------------------------------+
//|                                              DataProcessor.mqh    |
//|                                    Generated by ChatGPT/DeepSeek  |
//|                                         https://www.deepseek.com  |
//+------------------------------------------------------------------+
#ifndef __DATA_PROCESSOR_MQH__
#define __DATA_PROCESSOR_MQH__

#include "Common.mqh"

// 处理市场数据
void ProcessMarketData()
{
   double upper[3], middle[3], lower[3];
   double open[3], high[3], low[3], close[3];
   double atr[3];
   long volume[3];

   // 每个新K线周期开始时重置写入状态
   if(lastBarTime != iTime(Symbol(), BB_TF, 0))
   {
      prevBB.isWritten = false;
   }

   if(!GetBollingerBandsData(upper, middle, lower)) {
      Print("获取布林带数据失败!");
      return;
   }

   // 获取ATR指标数据（使用已存在的句柄）
   if(atrHandle != INVALID_HANDLE) {
      CopyBuffer(atrHandle, 0, 0, 3, atr);
   } else {
      Print("ATR指标句柄无效，无法获取数据");
      ArrayInitialize(atr, 0);
   }

   // 获取成交量数据
   CopyTickVolume(Symbol(), BB_TF, 0, 3, volume);

   CopyOHLC(open, high, low, close);

   UpdateBBStructure(currentBB, upper[2], middle[2], lower[2], open[2], high[2], low[2], close[2], atr[2], volume[2]);
   UpdateBBStructure(prevBB, upper[1], middle[1], lower[1], open[1], high[1], low[1], close[1], atr[1], volume[1]);
   UpdateBBStructure(prev2BB, upper[0], middle[0], lower[0], open[0], high[0], low[0], close[0], atr[0], volume[0]);
}

// 获取布林带数据
bool GetBollingerBandsData(double &upper[], double &middle[], double &lower[])
{
   if(CopyBuffer(bbHandle, 0, 0, 3, middle) <= 0) return false;
   if(CopyBuffer(bbHandle, 1, 0, 3, upper) <= 0) return false;
   if(CopyBuffer(bbHandle, 2, 0, 3, lower) <= 0) return false;
   return true;
}

// 复制OHLC数据
void CopyOHLC(double &open[], double &high[], double &low[], double &close[]) {
   CopyOpen(Symbol(), BB_TF, 0, 3, open);
   CopyHigh(Symbol(), BB_TF, 0, 3, high);
   CopyLow(Symbol(), BB_TF, 0, 3, low);
   CopyClose(Symbol(), BB_TF, 0, 3, close);
}

// 更新布林带结构
void UpdateBBStructure(BBData &bb, double u, double m, double l,
                      double o, double h, double l_price, double c, double a, long v) {
   if(u != EMPTY_VALUE && l != EMPTY_VALUE)
      bb.Calculate(u, m, l, o, h, l_price, c, a, v);
}

#endif