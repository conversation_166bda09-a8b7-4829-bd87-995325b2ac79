//+------------------------------------------------------------------+
//|                                 VolatilityExpansionPro.mq5       |
//|                                  Copyright 2024, Trae AI Expert |
//|                                            https://www.example.com|
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Trae AI Expert"
#property version   "2.00"
#property strict
#property indicator_chart_window

// 输入参数
input int    VolatilityPeriod = 20;    // 波动率计算周期（分钟）
input double ExpansionMultiplier = 1.2; // 扩张倍数阈值
input int    HistoryMinutes = 60;      // 历史参考周期（分钟）
input color  SignalColor = clrRoyalBlue;// 信号标记颜色

// 全局变量
double volatilityBuffer[];            // 波动率历史数据
double expansionRates[];              // 扩张率历史数据
datetime lastBarTime;
int objCounter = 0;

// 调试面板对象
long chartID;
string debugInfo[6];
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化数组
   ArraySetAsSeries(volatilityBuffer, true);
   ArrayResize(volatilityBuffer, HistoryMinutes);
   ArraySetAsSeries(expansionRates, true);
   ArrayResize(expansionRates, HistoryMinutes);
   
   // 创建调试面板
   chartID = ChartID();
   CreateDebugPanel();
   
   // 初始化历史数据
   if(!LoadHistoricalData())
   {
      Alert("历史数据加载失败!");
      return(INIT_FAILED);
   }
   
   // 设置定时器（每10秒更新面板）
   EventSetTimer(10);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 创建调试信息面板                                                 |
//+------------------------------------------------------------------+
void CreateDebugPanel()
{
   // 面板背景
   ObjectCreate(chartID, "DebugBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_YDISTANCE, 50);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_XSIZE, 250);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_YSIZE, 150);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_BGCOLOR, clrWhiteSmoke);
   ObjectSetInteger(chartID, "DebugBG", OBJPROP_BORDER_TYPE, BORDER_FLAT);

   // 信息标签
   for(int i=0; i<6; i++)
   {
      string objName = "DebugLabel"+IntegerToString(i);
      ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 15);
      ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 55+i*25);
      ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrBlack);
      debugInfo[i] = "";
   }
}

//+------------------------------------------------------------------+
//| 更新调试面板信息                                                 |
//+------------------------------------------------------------------+
void UpdateDebugPanel(double currentVol, double currentExpansion, double threshold)
{
   debugInfo[0] = "当前波动率: " + DoubleToString(currentVol, 2);
   debugInfo[1] = "当前扩张率: " + DoubleToString(currentExpansion*100, 1)+"%";
   debugInfo[2] = "触发阈值: " + DoubleToString(threshold*100, 1)+"%";
   debugInfo[3] = "历史最高: " + DoubleToString(ArrayMaximum(expansionRates)*100, 1)+"%";
   debugInfo[4] = "历史平均: " + DoubleToString(ArrayAverage(expansionRates)*100, 1)+"%";
   debugInfo[5] = "最后信号: " + TimeToString(lastBarTime, TIME_MINUTES);

   for(int i=0; i<6; i++)
   {
      string objName = "DebugLabel"+IntegerToString(i);
      ObjectSetString(chartID, objName, OBJPROP_TEXT, debugInfo[i]);
   }
}

//+------------------------------------------------------------------+
//| 加载历史数据                                                     |
//+------------------------------------------------------------------+
bool LoadHistoricalData()
{
   int totalBars = MathMin(Bars(_Symbol, PERIOD_M1), HistoryMinutes*2);
   
   for(int i=0; i<HistoryMinutes; i++)
   {
      int shift = i + VolatilityPeriod;
      if(shift >= totalBars) return false;
      
      volatilityBuffer[i] = CalculateVolatility(shift);
      if(i > 0)
      {
         expansionRates[i] = (volatilityBuffer[i] - volatilityBuffer[i-1]) / volatilityBuffer[i-1];
      }
   }
   return true;
}

//+------------------------------------------------------------------+
//| 计算波动率（基于价格振幅）                                       |
//+------------------------------------------------------------------+
double CalculateVolatility(int shift)
{
   double sum = 0;
   for(int i=0; i<VolatilityPeriod; i++)
   {
      sum += (iHigh(_Symbol, PERIOD_M1, shift+i) - iLow(_Symbol, PERIOD_M1, shift+i));
   }
   return sum / _Point; // 转换为点数
}

//+------------------------------------------------------------------+
//| 主逻辑处理                                                       |
//+------------------------------------------------------------------+
void ProcessTick()
{
   datetime currentTime = iTime(_Symbol, PERIOD_M1, 0);
   
   if(currentTime != lastBarTime)
   {
      // 计算当前波动率
      double currentVol = CalculateVolatility(0);
      
      // 计算瞬时扩张率
      double prevVol = volatilityBuffer[0];
      double currentExpansion = (currentVol - prevVol) / prevVol;
      
      // 更新历史数据
      UpdateBuffers(currentVol, currentExpansion);
      
      // 计算动态阈值
      double threshold = ArrayAverage(expansionRates) * ExpansionMultiplier;
      
      // 触发条件判断
      if(currentExpansion >= threshold && threshold != 0)
      {
         TriggerSignal(currentVol, currentExpansion);
         LogSignal(currentVol, currentExpansion, threshold);
      }
      
      // 更新调试面板
      UpdateDebugPanel(currentVol, currentExpansion, threshold);
      
      lastBarTime = currentTime;
   }
}

//+------------------------------------------------------------------+
//| 更新数据缓冲区                                                   |
//+------------------------------------------------------------------+
void UpdateBuffers(double newVolatility, double newExpansion)
{
   // 后移波动率数据
   for(int i=ArraySize(volatilityBuffer)-1; i>0; i--)
   {
      volatilityBuffer[i] = volatilityBuffer[i-1];
   }
   volatilityBuffer[0] = newVolatility;
   
   // 后移扩张率数据
   for(int i=ArraySize(expansionRates)-1; i>0; i--)
   {
      expansionRates[i] = expansionRates[i-1];
   }
   expansionRates[0] = newExpansion;
}

//+------------------------------------------------------------------+
//| 触发交易信号                                                     |
//+------------------------------------------------------------------+
void TriggerSignal(double vol, double expansion)
{
   objCounter++;
   
   // 创建箭头标记
   string arrowName = "Signal_"+IntegerToString(objCounter);
   ObjectCreate(chartID, arrowName, OBJ_ARROW_UP, 0, lastBarTime, iLow(_Symbol, PERIOD_M1, 1));
   ObjectSetInteger(chartID, arrowName, OBJPROP_COLOR, SignalColor);
   ObjectSetInteger(chartID, arrowName, OBJPROP_WIDTH, 3);
   
   // 创建信息标签
   string labelName = "Label_"+IntegerToString(objCounter);
   ObjectCreate(chartID, labelName, OBJ_TEXT, 0, lastBarTime, iHigh(_Symbol, PERIOD_M1, 1)+50*_Point);
   ObjectSetString(chartID, labelName, OBJPROP_TEXT, StringFormat("▲ 扩张 %.1f%%", expansion*100));
   ObjectSetInteger(chartID, labelName, OBJPROP_COLOR, SignalColor);
}

//+------------------------------------------------------------------+
//| 记录信号日志                                                     |
//+------------------------------------------------------------------+
void LogSignal(double vol, double expansion, double threshold)
{
   string logEntry = StringFormat("%s 信号触发! 波动率: %.1f 扩张率: %.2f%% 阈值: %.2f%%",
                   TimeToString(TimeCurrent(), TIME_SECONDS), vol, expansion*100, threshold*100);
   Print(logEntry);
   Comment(logEntry); // 在图表显示最新信号
}

//+------------------------------------------------------------------+
//| 通用工具函数                                                     |
//+------------------------------------------------------------------+
double ArrayAverage(const double &arr[])
{
   double sum = 0;
   int count = 0;
   for(int i=0; i<ArraySize(arr); i++)
   {
      if(arr[i] != 0)
      {
         sum += arr[i];
         count++;
      }
   }
   return count>0 ? sum/count : 0;
}

//+------------------------------------------------------------------+
//| Timer事件处理函数                                                |
//+------------------------------------------------------------------+
void OnTimer()
{
   ProcessTick();
}

//+------------------------------------------------------------------+
//| 清理函数                                                         |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   EventKillTimer();
   ObjectsDeleteAll(chartID, "Debug");
   ObjectsDeleteAll(chartID, "Signal_");
   ObjectsDeleteAll(chartID, "Label_");
}