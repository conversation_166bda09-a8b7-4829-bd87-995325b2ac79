//+------------------------------------------------------------------+
//|                                        NotificationManager.mqh    |
//|                                    Generated by ChatGPT/DeepSeek  |
//|                                         https://www.deepseek.com  |
//+------------------------------------------------------------------+
#ifndef __NOTIFICATION_MANAGER_MQH__
#define __NOTIFICATION_MANAGER_MQH__

#include "Common.mqh"

// 获取当前CSV文件名（不带后缀）
string GetCurrentCSVFileName()
{
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int daysToMonday = (timeStruct.day_of_week == 0) ? 6 : (timeStruct.day_of_week - 1);
   datetime mondayTime = TimeCurrent() - daysToMonday * 24 * 60 * 60;
   datetime sundayTime = mondayTime + 6 * 24 * 60 * 60;
   string weekRange = TimeToString(mondayTime, TIME_DATE) + "_" + TimeToString(sundayTime, TIME_DATE);
   int currentWeekNumber = (int)(mondayTime / (7 * 24 * 60 * 60));
   string weekNumber = IntegerToString(currentWeekNumber);
   string fileName = "BB_Width_Data_" + Symbol() + "_" + EnumToString(_Period) + "_Week" + weekNumber + "_"+ weekRange;

   return fileName;
}

// 确保截图目录存在
bool EnsureScreenshotDirExists()
{
   // 使用当前CSV文件名作为目录名（不带后缀）
   string dirName = GetCurrentCSVFileName();

   // 检查目录是否存在
   if(!FileIsExist(dirName, FILE_COMMON))
   {
      // 创建目录
      int error = GetLastError(); // 先获取错误代码

      // 如果错误是"目录已存在"，则忽略错误
      if(error == ERR_DIRECTORY_ALREADY_EXISTS) {
         Print("截图目录已存在: ", dirName);
         return true;
      }

      // 尝试创建目录
      if(!FolderCreate(dirName, FILE_COMMON))
      {
         Print("创建截图目录失败: ", GetLastError());
         return false;
      }
      Print("已创建截图目录: ", dirName);
   }

   return true;
}

//+------------------------------------------------------------------+
//| 发送布林带宽度提醒                                               |
//+------------------------------------------------------------------+
void SendBBWidthAlert(bool isEndSignal = false)
{
   datetime triggerTime = TimeCurrent();

   // 准备微信消息正文
   // 调整图表显示范围
   ChartSetInteger(0, CHART_SHOW, CHART_SHOW_DATE_SCALE, true);
   ChartSetInteger(0, CHART_SHOW, CHART_SHOW_PRICE_SCALE, true);
   ChartNavigate(0, CHART_END, -30);
   string body = "";

   // 添加提醒信息
   // 移动端优化格式
   if(isEndSignal) {
      body += "\n🔴🔴🔴 布林带宽度信号结束 🔴🔴🔴\n\n";
   } else {
      body += "\n🟢🟢🟢 布林带宽度信号开始 🟢🟢🟢\n\n";
   }
   body += StringFormat("品种: %s\n周期: %s\n时间: %s\n\n", Symbol(), EnumToString(_Period), TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES | TIME_SECONDS));

   // 参数区块
   body += "〖参数设置〗\n";
   body += StringFormat("周期数: %d\n偏移量: %.1f\n阈值宽度: %.5f\n\n", BB_Period, BB_Deviation, Threshold_Width);

   // 连续状态
   body += "〖连续状态〗\n";

   if(isEndSignal) {
      // 信号结束时显示持续了多少次
      body += StringFormat("信号持续: %d次\n历史最高: %d次\n", lastConsecutive, maxConsecutive);

      // 计算信号持续的时间（根据时间周期）
      int periodMinutes = 0;
      switch(_Period) {
         case PERIOD_M1: periodMinutes = 1; break;
         case PERIOD_M5: periodMinutes = 5; break;
         case PERIOD_M15: periodMinutes = 15; break;
         case PERIOD_M30: periodMinutes = 30; break;
         case PERIOD_H1: periodMinutes = 60; break;
         case PERIOD_H4: periodMinutes = 240; break;
         case PERIOD_D1: periodMinutes = 1440; break;
         case PERIOD_W1: periodMinutes = 10080; break;
         case PERIOD_MN1: periodMinutes = 43200; break;
         default: periodMinutes = 0;
      }

      int totalMinutes = lastConsecutive * periodMinutes;
      int hours = totalMinutes / 60;
      int minutes = totalMinutes % 60;

      if(hours > 0) {
         body += StringFormat("持续时间: %d小时%d分钟\n", hours, minutes);
      } else {
         body += StringFormat("持续时间: %d分钟\n", minutes);
      }
   } else {
      // 信号开始时显示当前连续次数
      body += StringFormat("当前连续: %d次\n历史最高: %d次\n", currentConsecutive, maxConsecutive);
   }

   body += StringFormat("最近连续: %d次\n平均连续: %.1f次\n\n", lastConsecutive, averageConsecutive);

   // 当前数据（键值对齐）
   body += "〖实时数据〗\n";
   body += StringFormat("上轨: %.5f\n中轨: %.5f\n", currentBB.upper, currentBB.middle);
   body += StringFormat("下轨: %.5f\n宽度: %.5f", currentBB.lower, currentBB.width);

   // 发送通知
   if(WeChat_Enabled) {
      // 发送文本消息
      wechatManager.SendMessage(body);

      // 确保截图目录存在
      if(!EnsureScreenshotDirExists())
      {
         Print("无法确保截图目录存在，将使用默认目录");
      }

      // 获取当前CSV文件名作为目录名（不带后缀）
      string dirName = GetCurrentCSVFileName();

      // 生成文件名
      MqlDateTime timeStruct;
      TimeToStruct(triggerTime, timeStruct);
      string signalType = isEndSignal ? "End" : "Start";
      string fileName = StringFormat("BB_Alert_%s_%s_%s_%04d%02d%02d_%02d%02d%02d.png",
         Symbol(),
         EnumToString(_Period),
         signalType,
         timeStruct.year, timeStruct.mon, timeStruct.day,
         timeStruct.hour, timeStruct.min, timeStruct.sec);

      // 完整路径（包含目录）
      string filePath = dirName + "\\" + fileName;
      Print("截图路径: ", filePath);


      // 截图并记录路径
      ChartSetInteger(0, CHART_SHOW_DATE_SCALE, true);
      ChartSetInteger(0, CHART_SHOW_PRICE_SCALE, true);

      if(!ChartScreenShot(0, filePath, 1920, 1080, ALIGN_RIGHT)) {
         int error = GetLastError();
         Print(StringFormat("截图失败! 错误码:%d ", error));
      }
      else {
         Print(StringFormat("截图已生成: %s", filePath));

         // 发送图片消息
         if(!wechatManager.SendImage(filePath)) {
            Print("发送图片到微信失败");
         }
         else {
            Print("图片已成功发送到微信");
         }
      }
   }
}

#endif