# BB_Width_EA Documentation

## 1. Introduction

BB_Width_EA (Bollinger Bands Width Monitor EA) is an Expert Advisor developed for the MetaTrader 5 platform, designed to monitor Bollinger Bands width and generate signals when specific conditions are met. The EA identifies situations where the Bollinger Bands width falls below a set threshold, which typically indicates reduced market volatility and potential upcoming price breakouts.

## 2. Key Features

- **Bollinger Bands Width Monitoring**: Real-time monitoring of Bollinger Bands width, generating signals when width falls below the set threshold
- **Consecutive Signal Tracking**: Records and displays the count of consecutive signals, including current consecutive, recent consecutive, and historical maximum consecutive counts
- **Signal Visualization**: Marks signal positions on the chart with arrows
- **Real-time Information Panel**: Displays Bollinger Bands data, OHLC prices, ATR, and volume for current, previous, and second previous candles
- **Candle Countdown Timer**: Shows countdown to current candle closing
- **WeChat Notifications**: Sends text and chart screenshot notifications via WeChat enterprise robot
- **Data Recording**: Saves signal data to CSV files, automatically segmented by week
- **Preset Configuration System**: Automatically applies optimal parameters based on trading instrument and timeframe
- **Resource Monitoring**: Periodically records EA resource usage

## 3. Installation

1. Copy `Experts\BB_Width_EA.mq5` file to MT5's `Experts` directory
2. Copy `Include\BB_Width_EA` folder and all its files to MT5's `Include` directory
3. Copy `Include\WeChatRobotManager.mqh` file to MT5's `Include` directory
4. Compile BB_Width_EA.mq5 file in MetaTrader 5
5. Attach the compiled EA to the desired chart

**Quick Installation (using installation script)**:
1. Open PowerShell
2. Execute the following command to allow script execution:
   ```powershell
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   ```
3. Run the installation script:
   ```powershell
   .\install.ps1
   ```

## 4. Parameter Configuration

### Bollinger Bands Parameters
- **BB_Period** (default: 20): Bollinger Bands period
- **BB_Deviation** (default: 2.0): Standard deviation multiplier
- **BB_Shift** (default: 0): Indicator shift
- **APP_Price** (default: PRICE_CLOSE): Applied price
- **BB_TF** (default: PERIOD_CURRENT): Timeframe

### Panel Parameters
- **Panel_Corner** (default: 0): Panel position (0-3)
- **Panel_Text_Color** (default: clrWhite): Text color
- **Panel_Back_Color** (default: clrSteelBlue): Background color
- **Panel_Font** (default: "Consolas"): Panel font

### Signal Parameters
- **Threshold_Width_Input** (default: 8.0): Width threshold
- **Arrow_Color** (default: clrGoldenrod): Arrow color

### Notification Parameters
- **WeChat_Enabled** (default: true): Enable WeChat notifications
- **WeChat_Consec_Trigger_Input** (default: 5): WeChat notification consecutive count threshold
- **WeChat_Webhook**: WeChat robot Webhook URL

### Resource Monitoring Parameters
- **ResourceMonitor_Enabled_Input** (default: false): Enable resource monitoring

## 5. User Interface

BB_Width_EA provides an information-rich panel displayed on the chart. The panel includes the following information:

### Bollinger Bands Data Area
- **Second Previous Candle**: Displays Bollinger Bands upper, middle, lower bands and width, as well as OHLC prices, ATR, and volume
- **Previous Candle**: Displays Bollinger Bands data and price information
- **Current Candle**: Displays Bollinger Bands data, price information, and candle closing countdown

### Consecutive Status Area
- **Current Threshold**: Displays the current width threshold (green indicates current width is below threshold, red indicates above threshold)
- **Current Consecutive**: Current consecutive signal count
- **Recent Consecutive**: Most recent consecutive signal count
- **Historical Maximum**: Historical maximum consecutive signal count
- **Average Consecutive**: Average consecutive signal count
- **Total Cycles**: Total signal cycle count

### Candle Countdown
- Displays countdown to current candle closing, format automatically adjusts based on remaining time:
  - Greater than 1 hour: HH:MM:SS
  - Less than 1 hour: MM:SS
  - Turns red when remaining time is less than 20% of total time

## 6. Signal Generation and Notifications

### Signal Conditions
When the Bollinger Bands width (difference between upper and lower bands) falls below the set threshold, the EA generates a signal.

### Signal Visualization
- Signals are marked with arrows on the chart (positioned at the lower band)
- Keeps up to 500 most recent arrows, automatically deleting the oldest ones when exceeded

### WeChat Notifications
When the consecutive signal count reaches the set threshold (default is 5), the EA sends WeChat notifications:
1. **Signal Start Notification**: Sent when consecutive signal count first reaches the threshold
2. **Signal End Notification**: Sent when consecutive signals end (width no longer below threshold)

Notification content includes:
- Signal type (start/end)
- Trading instrument and timeframe
- Current time
- Parameter settings (period, deviation, threshold width)
- Consecutive status (current consecutive, historical maximum, recent consecutive, average consecutive)
- Real-time data (upper band, middle band, lower band, width)
- Chart screenshot

## 7. Configuration System

BB_Width_EA includes a preset configuration system that automatically applies optimal parameters based on trading instrument and timeframe.

### Default Configurations
- XAUUSD, M5: Width threshold = 6.5, Consecutive count = 5
- EURUSD, M15: Width threshold = 0.00050, Consecutive count = 5
- EURUSD, H1: Width threshold = 0.00080, Consecutive count = 3
- GBPUSD, M15: Width threshold = 0.00060, Consecutive count = 5
- USDJPY, H1: Width threshold = 0.080, Consecutive count = 3

### Custom Configurations
Users can add custom configurations by creating or editing the `BB_Width_Configs.csv` file. This file is located in MT5's common files directory (`%APPDATA%\MetaQuotes\Terminal\Common\Files\`).

CSV file format:
```
Symbol,Timeframe,Threshold,Consecutive
XAUUSD,M5,6.5,5
EURUSD,M15,0.00050,5
```

### Symbol Matching
The configuration system supports matching symbols with suffixes, e.g., "EURUSD.a" will match the configuration for "EURUSD".

## 8. Data Recording

### CSV Files
The EA saves signal data to CSV files located in MT5's common files directory (`%APPDATA%\MetaQuotes\Terminal\Common\Files\`).

File naming format:
```
BB_Width_Data_[Symbol]_[Timeframe]_Week[WeekNumber]_[WeekStartDate]_[WeekEndDate].csv
```

CSV files contain the following columns:
- Time: Signal time
- Price: Signal price
- Upper: Bollinger Bands upper band
- Middle: Bollinger Bands middle band
- Lower: Bollinger Bands lower band
- Width: Bollinger Bands width
- Open: Open price
- High: High price
- Low: Low price
- Close: Close price
- ATR: Average True Range
- Volume: Volume
- Timestamp: Timestamp

### Screenshot Saving
The EA saves chart screenshots when sending WeChat notifications. Screenshots are saved in a directory with the same name as the CSV file (without extension).

## 9. Resource Monitoring

When resource monitoring is enabled, the EA records resource usage every hour, including:
- Memory usage (total, used, percentage)
- Object count (total, arrow count)
- Indicator handle status
- Terminal information (DLL allowed, maximum bars, code page)
- EA running time

Resource usage is recorded in MT5's log.

## 10. Troubleshooting and FAQ

### WeChat Notifications Not Working
- Check if WeChat_Enabled parameter is set to true
- Confirm WeChat_Webhook URL is correct
- Check if network connection is normal
- Look for related error messages in MT5 log

### No Signals Found
- Check if Threshold_Width_Input parameter is set appropriately
- Confirm Bollinger Bands parameters (BB_Period, BB_Deviation) are suitable for the current market
- Try using the preset configuration system for specific instruments and timeframes

### Screenshot Saving Failed
- Ensure MT5 has sufficient permissions to create directories and files
- Check if disk space is sufficient
- Look for error messages in MT5 log

## 11. Version History

### Version 1.00
- Initial version
- Basic Bollinger Bands width monitoring functionality
- Information panel display
- WeChat notification functionality
- CSV data recording

## 12. Contact and Support

For any questions or suggestions, please contact the developer:
- Email: [<EMAIL>](mailto:<EMAIL>)
- Website: [https://www.example.com](https://www.example.com)
