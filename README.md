# DayTrader 交易专家顾问系统

## 项目概述

本项目是一套基于MetaTrader 5平台的交易专家顾问(EA)系统，包含多个针对不同交易策略的EA。这些EA旨在帮助交易者自动执行交易策略，提高交易效率和准确性。

## 主要功能

- **BreakoutDetector2_M1**: M1时间框架的突破检测器，结合ADX、ATR和RSI指标进行三重过滤
- **MA_CrossTrend_M15**: M15时间框架的均线交叉趋势跟踪系统
- **EMA_Cross_M5**: M5时间框架的EMA交叉信号生成器
- **VolatilitySignalPro_M1**: M1时间框架的波动率扩张信号检测器

## 安装步骤

1. 将Experts文件夹中的所有.mq5文件复制到MetaTrader 5的Experts目录下
2. 在MetaTrader 5中编译所有.mq5文件
3. 将编译后的.ex5文件附加到相应的交易品种图表上

## 使用说明

每个EA都包含详细的输入参数，用户可以根据自己的交易策略进行调整。建议在使用前进行充分的回测和优化。

**部署脚本使用**

- 打开PowerShell
- 临时允许脚本执行：

     ```powershell
     Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass 
     ```

     ```powershell
     .\install.ps1
     ```

- 或者永久修改执行策略（需要管理员权限）：

     ```powershell
     Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
     .\deploy_to_mt5.ps1
     ```

## 专家顾问简介

### BreakoutDetector2_M1

- 功能：检测价格突破并生成交易信号
- 主要指标：ADX、ATR、RSI
- 时间框架：M1
- 交易策略：当ADX显示趋势强度，ATR显示波动率扩大，RSI显示超买超卖时产生信号
- 参数设置建议：ADX周期14，ATR周期14，RSI周期14
- 适用场景：适合日内交易和趋势跟踪策略

### VolumeSpike

- 功能：检测成交量突增并生成交易信号
- 主要指标：成交量、价格变动
- 时间框架：M1
- 交易策略：当成交量突破设定的阈值时产生交易信号
- 参数设置建议：WindowSize=100，Threshold=300.0，MinBaseVolume=10，ConsecutiveTicks=3，PriceChangeFilter=1.0
- 适用场景：适合日内交易和突破策略

### MA_CrossTrend_M15

- 功能：跟踪均线交叉趋势
- 主要指标：快速MA、慢速MA
- 时间框架：M15
- 交易策略：当快速MA上穿慢速MA时做多，下穿时做空
- 参数设置建议：快速MA周期5，慢速MA周期20
- 适用场景：适合趋势跟踪和波段交易策略

### EMA_Cross_M5

- 功能：生成EMA交叉信号
- 主要指标：快速EMA、慢速EMA
- 时间框架：M5
- 交易策略：当快速EMA上穿慢速EMA时做多，下穿时做空
- 参数设置建议：快速EMA周期12，慢速EMA周期26
- 适用场景：适合日内交易和趋势跟踪策略

### VolumeSpike

- 功能：检测成交量突增并生成交易信号
- 主要指标：成交量、价格变动
- 时间框架：M1
- 交易策略：当成交量突破设定的阈值时产生交易信号
- 参数设置建议：WindowSize=100，Threshold=300.0，MinBaseVolume=10，ConsecutiveTicks=3，PriceChangeFilter=1.0
- 适用场景：适合日内交易和突破策略

### VolatilitySignalPro_M1

- 功能：检测波动率扩张信号
- 主要指标：ATR
- 时间框架：M1
- 交易策略：当ATR突破前高时产生交易信号
- 参数设置建议：ATR周期14
- 适用场景：适合突破交易策略和波动率交易

### RangeDetector_M15

- 功能：检测M15时间框架下的震荡行情
- 主要指标：ADX、布林带、RSI
- 时间框架：M15
- 交易策略：当ADX显示趋势强度较弱，布林带收窄，RSI处于中性区域时，判断为震荡行情
- 参数设置建议：ADX周期14，布林带周期20，RSI周期14
- 适用场景：适合区间交易和震荡策略

- 功能：检测波动率扩张信号
- 主要指标：ATR
- 时间框架：M1
- 交易策略：当ATR突破前高时产生交易信号
- 参数设置建议：ATR周期14
- 适用场景：适合突破交易策略和波动率交易
