//+------------------------------------------------------------------+
//|                                                  BBData.mqh     |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#ifndef __BB_DATA_MQH__
#define __BB_DATA_MQH__

// 布林带数据结构体
struct BBData {
   double upper;    // 上轨
   double middle;   // 中轨
   double lower;    // 下軌
   double width;    // 带宽
   double open;     // 开盘价
   double high;     // 最高价
   double low;      // 最低价
   double close;    // 收盘价
   double atr;      // ATR值
   long volume;     // 成交量
   bool isWritten;  // 数据是否已写入标志
   datetime timestamp; // 时间戳
   
   void Calculate(double u, double m, double l, double o, double h, double l_price, double c, double a, long v) {
      upper = u;
      middle = m;
      lower = l;
      width = upper - lower;
      open = o;
      high = h;
      low = l_price;
      close = c;
      atr = a;
      volume = v;
   }
};

#endif