#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

// 外部参数
input int FastMAPeriod = 50;     // 快速均线周期
input int SlowMAPeriod = 200;    // 慢速均线周期
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M15; // 图表周期

// 文件操作参数
input string CrossLogFolder = "MQL5\\Files\\MACross\\";  // 交叉点记录文件夹（MT4/MT5通用）
input bool EnableCrossLog = true;   // 启用交叉点记录

// 全局变量
int fastMAHandle, slowMAHandle;
double fastMA[2], slowMA[2]; // 仅保留2个元素用于实时判断

input int AlertInterval = 60;    // 警报间隔时间（秒）
input int PanelX = 20;          // 面板X坐标
input int PanelY = 20;          // 面板Y坐标
input color BullColor = clrLime;  // 多头颜色
input color BearColor = clrRed;   // 空头颜色

// 全局变量
datetime lastAlertTime;
string trendStatus = "N/A";      // 趋势状态
long chartID;                    // 图表ID
datetime lastBarTime;           // 记录上一次检测的K线时间
string logFilePath;             // 日志文件路径
bool prevBullTrend = false;     // 上一次的趋势状态

int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();
   
   // 初始化移动平均线指标
   fastMAHandle = iMA(_Symbol, TimeFrame, FastMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, TimeFrame, SlowMAPeriod, 0, MODE_SMA, PRICE_CLOSE);
   
   // 增强指标句柄检查
   if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE){
      string errorMsg = StringFormat("指标初始化失败 FastMA(%d) SlowMA(%d)",
                                   fastMAHandle, slowMAHandle);
      Alert(errorMsg);
      Print(errorMsg);
      return(INIT_FAILED);
   }

   // 初始化最后K线时间
   datetime timeArray[1];
   CopyTime(_Symbol, TimeFrame, 0, 1, timeArray); // 获取最新K线时间
   lastBarTime = timeArray[0];
   
   // 初始化日志文件路径
   if(EnableCrossLog)
   {
      // 确保文件夹存在
      string commonPath = TerminalInfoString(TERMINAL_COMMONDATA_PATH);
      string fullFolderPath = commonPath + "\\" + CrossLogFolder;
      
      // 创建文件夹（如果不存在）
      if(!FolderCreate(fullFolderPath))
      {
         int error = GetLastError();
         if(error != ERR_DIRECTORY_ALREADY_EXISTS)
         {
            Print("无法创建日志文件夹: ", fullFolderPath, ", 错误码: ", error);
         }
      }
      
      // 设置日志文件名（使用符号名和时间帧）
      string fileName = StringFormat("%s_%s_MACross.csv", _Symbol, EnumToString(TimeFrame));
      logFilePath = fullFolderPath + fileName;
      
      // 如果文件不存在，创建并写入标题行
      if(!FileIsExist(logFilePath))
      {
         int fileHandle = FileOpen(logFilePath, FILE_WRITE|FILE_CSV|FILE_ANSI);
         if(fileHandle != INVALID_HANDLE)
         {
            FileWrite(fileHandle, "日期时间", "符号", "时间帧", "交叉类型", "快速MA", "慢速MA", "收盘价");
            FileClose(fileHandle);
            Print("创建交叉点记录文件: ", logFilePath);
         }
         else
         {
            Print("无法创建交叉点记录文件: ", logFilePath, ", 错误码: ", GetLastError());
         }
      }
   }

   // 初始化趋势状态
   CalculateInitialTrend();
   prevBullTrend = (trendStatus == "多头趋势");
   UpdateInfoPanel();

   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   DeleteInfoPanel();
   IndicatorRelease(fastMAHandle);
   IndicatorRelease(slowMAHandle);
   
   // 输出日志信息
   if(EnableCrossLog)
   {
      Print("均线交叉记录已保存到文件: ", logFilePath);
   }
}

void OnTick()
{
   UpdateInfoPanel();
   // 获取当前图表周期
   if(Period() != TimeFrame){
      Alert("请在", EnumToString(TimeFrame), "时间框架使用本EA");
      return;
   }

   // 检查是否有新K线形成
   datetime currentBarTime[1];
   if(CopyTime(_Symbol, TimeFrame, 0, 1, currentBarTime) < 1)
   {
      Print("时间数据获取失败！");
      return;
   }

   // 如果K线时间未变化（未生成新K线），直接返回
   if(currentBarTime[0] == lastBarTime) return;

   // 更新最后检测时间
   lastBarTime = currentBarTime[0];

   // 复制指标数据 - 使用已闭合的K线数据（索引从1开始）
   if(CopyBuffer(fastMAHandle, 0, 1, 2, fastMA) < 2 || 
      CopyBuffer(slowMAHandle, 0, 1, 2, slowMA) < 2)
   {
      Print("MA数据复制失败！");
      return;
   }

   // 简化的趋势判断逻辑 - 直接比较已闭合K线的MA值
   bool newBullTrend = (fastMA[0] > slowMA[0]);
   bool currentIsBull = (trendStatus == "多头趋势");
   
   // 检测交叉点并记录
   if(newBullTrend != prevBullTrend)
   {
      string crossType = newBullTrend ? "金叉(多头)" : "死叉(空头)";
      Print("检测到均线交叉: ", crossType, ", ", _Symbol, " ", EnumToString(TimeFrame));
      
      // 记录交叉点
      if(EnableCrossLog)
      {
         // 获取当前K线收盘价
         double closePrice = iClose(_Symbol, TimeFrame, 1);
         // 记录交叉点数据
         LogCrossPoint(currentBarTime[0], crossType, fastMA[0], slowMA[0], closePrice);
      }
      
      // 更新上一次趋势状态
      prevBullTrend = newBullTrend;
   }
   
   // 只在趋势改变且满足警报间隔时发出警报
   if(newBullTrend != currentIsBull && (TimeCurrent() - lastAlertTime) >= AlertInterval)
   {
      lastAlertTime = TimeCurrent();
      
      if(newBullTrend)
      {
         Alert("趋势转多：", Symbol(), " ", EnumToString(TimeFrame));
         trendStatus = "多头趋势";
      }
      else
      {
         Alert("趋势转空：", Symbol(), " ", EnumToString(TimeFrame));
         trendStatus = "空头趋势";
      }
      
      UpdateInfoPanel();
   }
}

// 记录交叉点到CSV文件
void LogCrossPoint(datetime crossTime, string crossType, double fastMAValue, double slowMAValue, double closePrice)
{
   if(logFilePath == "" || !EnableCrossLog) return;
   
   int fileHandle = FileOpen(logFilePath, FILE_READ|FILE_WRITE|FILE_CSV|FILE_ANSI);
   if(fileHandle != INVALID_HANDLE)
   {
      // 移动到文件末尾
      FileSeek(fileHandle, 0, SEEK_END);
      
      // 写入交叉点数据
      FileWrite(fileHandle, 
                TimeToString(crossTime, TIME_DATE|TIME_MINUTES|TIME_SECONDS), 
                _Symbol, 
                EnumToString(TimeFrame), 
                crossType, 
                DoubleToString(fastMAValue, _Digits), 
                DoubleToString(slowMAValue, _Digits), 
                DoubleToString(closePrice, _Digits));
      
      FileClose(fileHandle);
      Print("交叉点已记录到文件: ", logFilePath);
   }
   else
   {
      Print("无法打开交叉点记录文件: ", logFilePath, ", 错误码: ", GetLastError());
   }
}

// 创建信息面板
void CreateInfoPanel()
{
   // 背景矩形
   ObjectCreate(chartID, "InfoPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_XSIZE, 200);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_YSIZE, 40);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   
   // 状态标签
   ObjectCreate(chartID, "TrendStatus", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "TrendStatus", OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, "TrendStatus", OBJPROP_YDISTANCE, PanelY+10);
   ObjectSetString(chartID, "TrendStatus", OBJPROP_TEXT, "趋势状态：" + trendStatus);
   ObjectSetInteger(chartID, "TrendStatus", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, "TrendStatus", OBJPROP_FONTSIZE, 8);
}

// 更新信息面板
void UpdateInfoPanel()
{
   color panelColor = (trendStatus == "多头趋势") ? BullColor : BearColor;
   
   // 更新背景颜色
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_COLOR, panelColor);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_WIDTH, 2);
   ObjectSetInteger(chartID, "InfoPanelBG", OBJPROP_BORDER_COLOR, panelColor);
   
   // 更新状态文本
   ObjectSetString(chartID, "TrendStatus", OBJPROP_TEXT, "趋势状态：" + trendStatus);
   ObjectSetInteger(chartID, "TrendStatus", OBJPROP_COLOR, panelColor);
}

// 初始趋势计算函数
void CalculateInitialTrend()
{
   // 多次尝试获取数据
   for(int attempt = 1; attempt <= 5; attempt++)
   {
      // 首先尝试使用索引1（已闭合的K线）
      if(CopyBuffer(fastMAHandle, 0, 1, 1, fastMA) >= 1 && 
         CopyBuffer(slowMAHandle, 0, 1, 1, slowMA) >= 1)
      {
         trendStatus = (fastMA[0] > slowMA[0]) ? "多头趋势" : "空头趋势";
         Print("初始趋势判断成功：", trendStatus, "，尝试次数：", attempt);
         return;
      }
      
      // 如果无法获取索引1的数据，尝试使用索引0（当前K线）
      if(attempt > 3)
      {
         if(CopyBuffer(fastMAHandle, 0, 0, 1, fastMA) >= 1 && 
            CopyBuffer(slowMAHandle, 0, 0, 1, slowMA) >= 1)
         {
            trendStatus = (fastMA[0] > slowMA[0]) ? "多头趋势" : "空头趋势";
            Print("初始趋势判断成功（使用当前K线）：", trendStatus, "，尝试次数：", attempt);
            return;
         }
      }
      
      // 等待并重试
      Print("尝试获取初始趋势数据，第", attempt, "次尝试失败，等待后重试...");
      Sleep(200 * attempt); // 逐渐增加等待时间
   }
   
   // 所有尝试都失败，使用默认值
   Print("无法获取初始趋势数据，使用默认值");
   trendStatus = "N/A";
   
   // 最后尝试直接使用价格比较
   double close = iClose(_Symbol, TimeFrame, 1);
   double open = iOpen(_Symbol, TimeFrame, 1);
   
   if(close != 0 && open != 0)
   {
      trendStatus = (close > open) ? "多头趋势" : "空头趋势";
      Print("使用价格比较判断趋势：", trendStatus, " (Close:", close, ", Open:", open, ")");
   }
}

// 删除信息面板
void DeleteInfoPanel()
{
   ObjectDelete(chartID, "InfoPanelBG");
   ObjectDelete(chartID, "TrendStatus");
}