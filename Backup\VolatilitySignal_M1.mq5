#property copyright "Copyright 2024, <PERSON>rae AI"
#property version   "1.00"
#property strict

// 外部参数
input double ATR_GrowthRate = 20.0;     // ATR小时增长率阈值(%)
input int ATR_Period = 14;            // ATR计算周期
input int SignalBars = 5;             // 信号持续柱数
input int PanelX = 20;                // 面板X坐标
input int PanelY = 20;                // 面板Y坐标
input color SignalColor = clrDodgerBlue; // 信号标记颜色

// 全局变量
long chartID;
double atrValues[3];
double growthRates[]; // 改为动态数组
int signalCount = 0;
int atrHandle;

int OnInit()
{
   chartID = ChartID();
   CreateInfoPanel();
   
   // 初始化ATR指标和增长率数组
   ArrayResize(growthRates,5);
   ArrayInitialize(growthRates,0);
   atrHandle = iATR(_Symbol, PERIOD_H1, ATR_Period);
   ArraySetAsSeries(atrValues, true);
   
   return(INIT_SUCCEEDED);
}

datetime lastHour;

void OnTick()
{
   // 每小时更新数据
   if(TimeMinute(TimeCurrent()) == 0 && TimeCurrent() != lastHour){
      lastHour = TimeCurrent();
      if(!UpdateATRData()) return;
   }
   
   // 每分钟校验数据有效性
   if(TimeSeconds(TimeCurrent()) % 60 == 0){
      if(!UpdateATRData()) return;
   }
   
   double currentGrowth = CalculateGrowthRate();
   UpdateGrowthArray(currentGrowth);
   
   DetectVolatilitySignal();
   UpdateInfoPanel();
}

bool UpdateATRData()
{
   // 获取最新3小时数据（包含当前未完成的小时数据）
   bool success = CopyBuffer(atrHandle, 0, 0, 3, atrValues) == 3;
   if(success){
      Print(StringFormat("ATR更新成功 时间:%s 值:[%.5f,%.5f,%.5f]",
           TimeToString(TimeCurrent()), atrValues[0], atrValues[1], atrValues[2]));
   }
   return success;
}

double CalculateGrowthRate()
{
   if(atrValues[1] == 0) return 0;
   return (atrValues[0] - atrValues[1])/atrValues[1]*100;
}

void UpdateGrowthArray(double newRate)
{
   ArrayCopy(growthRates, growthRates, 0, 1, 4);
   growthRates[4] = newRate;
   Print(StringFormat("增长率更新: %.2f%% 队列:[%.2f%%,%.2f%%,%.2f%%,%.2f%%,%.2f%%]",
        newRate, growthRates[0], growthRates[1], growthRates[2], growthRates[3], growthRates[4]));
}

void DetectVolatilitySignal()
{
   // 检测连续达标
   int count = 0;
   for(int i=0; i<5; i++){
      if(growthRates[i] > ATR_GrowthRate) count++;
   }
   
   if(count >= 3){
      signalCount = SignalBars;
      DrawSignalArrow(241, SignalColor);
      TriggerAlert();
   }
}

void DrawSignalArrow(int code, color clr)
{
   string objName = "VolSignal_"+TimeToString(TimeCurrent());
   ObjectCreate(chartID, objName, OBJ_ARROW, 0, TimeCurrent(), iClose(_Symbol,PERIOD_M1,0));
   ObjectSetInteger(chartID, objName, OBJPROP_ARROWCODE, code);
   ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clr);
   ObjectSetInteger(chartID, objName, OBJPROP_WIDTH, 2);
}

void TriggerAlert()
{
   string message = StringFormat("波动率扩张！当前增长率:%.1f%%", growthRates[4]);
   Alert(message);
   Print(message);
}

// 信息面板函数（复用现有架构）
void CreateInfoPanel()
{
   // 背景矩形
   ObjectCreate(chartID, "VolPanelBG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "VolPanelBG", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(chartID, "VolPanelBG", OBJPROP_XDISTANCE, PanelX);
   ObjectSetInteger(chartID, "VolPanelBG", OBJPROP_YDISTANCE, PanelY);
   ObjectSetInteger(chartID, "VolPanelBG", OBJPROP_XSIZE, 300);
   ObjectSetInteger(chartID, "VolPanelBG", OBJPROP_YSIZE, 100);

   // 创建信息标签
   CreatePanelLabel("ATRLabel", "ATR增长率：", 10);
   CreatePanelLabel("SignalLabel", "信号状态：", 40);
}

void CreatePanelLabel(string name, string text, int yOffset)
{
   ObjectCreate(chartID, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, name, OBJPROP_XDISTANCE, PanelX+10);
   ObjectSetInteger(chartID, name, OBJPROP_YDISTANCE, PanelY+yOffset);
   ObjectSetInteger(chartID, name, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, name, OBJPROP_FONTSIZE, 8);
   ObjectSetString(chartID, name, OBJPROP_TEXT, text);
}

void UpdateInfoPanel()
{
   string statusText = (signalCount > 0) ? "ACTIVE ("+IntegerToString(signalCount)+" bars)" : "Inactive";
   
   ObjectSetString(chartID, "ATRLabel", OBJPROP_TEXT, "当前ATR增长率: "+DoubleToString(growthRates[4],1)+"%");
   ObjectSetString(chartID, "SignalLabel", OBJPROP_TEXT, "信号状态: "+statusText);
   
   // 自动递减信号计数器
   if(signalCount > 0) signalCount--;
}

void OnDeinit(const int reason)
{
   ObjectDelete(chartID, "VolPanelBG");
   ObjectDelete(chartID, "ATRLabel");
   ObjectDelete(chartID, "SignalLabel");
}