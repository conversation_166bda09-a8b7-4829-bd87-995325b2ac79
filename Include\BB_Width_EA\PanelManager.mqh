//+------------------------------------------------------------------+
//|                                                  PanelManager.mqh |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#ifndef __PANEL_MANAGER_MQH__
#define __PANEL_MANAGER_MQH__

#include "Common.mqh"

// 创建信息面板
void CreateInfoPanel()
{
   // 面板背景
   ObjectCreate(0, panelName+"_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(0, panelName+"_BG", OB<PERSON>ROP_YDISTANCE, 40);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_XSIZE, 420);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_YSIZE, 740);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_BGCOLOR, Panel_Back_Color);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, panelName+"_BG", OBJPROP_CORNER, Panel_Corner);

   // 标题文本
   CreateLabel("Title", "Bollinger Bands Monitor", 30, 30, Panel_Text_Color);
   ObjectSetString(0, panelName+"_Title", OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(0, panelName+"_Title", OBJPROP_BGCOLOR, clrGray);

   // 各时间段布林带数据
   CreateBBLabels("Prev2", "前二K线:", 80, prev2BB);
   CreateBBLabels("Prev", "前一K线:", 80+150, prevBB);
   CreateBBLabels("", "当前K线:", 80+300, currentBB);

   // 连续次数显示区域
   int startY = 500;
   CreateLabel("ThresholdLabel", "当前阈值: ", 30, startY+30, Panel_Text_Color);
   CreateLabel("ThresholdValue", DoubleToString(Threshold_Width,_Digits), 140, startY+30, Panel_Text_Color);

   CreateLabel("CurrentConsecLabel", "当前连续: ", 30, startY+60, Panel_Text_Color);
   CreateLabel("CurrentConsecValue", "0", 140, startY+60, Panel_Text_Color);

   CreateLabel("LastConsecLabel", "最近连续: ", 30, startY+90, Panel_Text_Color);
   CreateLabel("LastConsecValue", "0", 140, startY+90, Panel_Text_Color);

   CreateLabel("MaxConsecLabel", "最长连续: ", 30, startY+120, Panel_Text_Color);
   CreateLabel("MaxConsecValue", "0", 140, startY+120, Panel_Text_Color);

   CreateLabel("AvgConsecLabel", "平均连续: ", 30, startY+150, Panel_Text_Color);
   CreateLabel("AvgConsecValue", "0", 140, startY+150, Panel_Text_Color);

   CreateLabel("TotalOccurLabel", "总触发次数: ", 30, startY+180, Panel_Text_Color);
   CreateLabel("TotalOccurValue", "0", 180, startY+180, Panel_Text_Color);

   // 配置信息区域
   CreateLabel("ConfigTitle", "预设配置信息", 30, startY+220, clrGold);

   // 获取当前品种和基本名称
   string currentSymbol = Symbol();
   string baseSymbol = currentSymbol;
   int dotPos = StringFind(currentSymbol, ".");
   if(dotPos > 0) {
      baseSymbol = StringSubstr(currentSymbol, 0, dotPos);
   }

   // 检查是否有匹配的预设配置
   BBWidthConfig config;
   bool configFound = false;

   // 遍历所有配置
   for(int i=0; i<g_configManager.GetCount(); i++) {
      BBWidthConfig cfg = g_configManager.GetConfig(i);
      // 使用配置结构体的Matches方法检查是否匹配
      if(cfg.Matches(currentSymbol, _Period)) {
         config = cfg;
         configFound = true;
         break;
      }
   }

   if(configFound) {
      CreateLabel("ConfigStatus", "已应用预设配置", 30, startY+240, clrLime);
      CreateLabel("ConfigDetails", config.ToString(), 30, startY+260, Panel_Text_Color);

      if(dotPos > 0) {
         CreateLabel("SymbolInfo", StringFormat("品种: %s (基本名称: %s)", currentSymbol, baseSymbol), 30, startY+280, clrSkyBlue);
      }
   } else {
      if(dotPos > 0) {
         CreateLabel("ConfigStatus", StringFormat("未找到匹配配置，使用输入参数 (尝试匹配: %s)", baseSymbol), 30, startY+240, clrOrange);
         CreateLabel("SymbolInfo", StringFormat("品种: %s (基本名称: %s)", currentSymbol, baseSymbol), 30, startY+260, clrSkyBlue);
      } else {
         CreateLabel("ConfigStatus", "未找到匹配配置，使用输入参数", 30, startY+240, clrOrange);
      }
   }
}

// 创建文本标签
void CreateLabel(const string name, const string text, int x, int y, color clr)
{
   ObjectCreate(0, panelName+"_"+name, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, panelName+"_"+name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_CORNER, Panel_Corner);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, panelName+"_"+name, OBJPROP_FONTSIZE, 8);
   ObjectSetString(0, panelName+"_"+name, OBJPROP_FONT, Panel_Font);
}

// 更新信息面板
void UpdateInfoPanel()
{
   // 更新布林带数据
   UpdateBBValues("", currentBB);
   UpdateBBValues("Prev", prevBB);
   UpdateBBValues("Prev2", prev2BB);

   // 更新阈值状态颜色
   color statusColor = (prevBB.width < Threshold_Width) ? clrLime : clrRed;
   ObjectSetInteger(0, panelName+"_ThresholdValue", OBJPROP_COLOR, statusColor);

   // 更新连续次数显示
   ObjectSetString(0, panelName+"_CurrentConsecValue", OBJPROP_TEXT, IntegerToString(currentConsecutive));
   ObjectSetString(0, panelName+"_LastConsecValue", OBJPROP_TEXT, IntegerToString(lastConsecutive));
   ObjectSetString(0, panelName+"_MaxConsecValue", OBJPROP_TEXT, IntegerToString(maxConsecutive));
   ObjectSetString(0, panelName+"_AvgConsecValue", OBJPROP_TEXT, DoubleToString(averageConsecutive,1));
   ObjectSetString(0, panelName+"_TotalOccurValue", OBJPROP_TEXT, IntegerToString(totalOccurrences));

   // 设置颜色
   ObjectSetInteger(0, panelName+"_CurrentConsecValue", OBJPROP_COLOR, (currentConsecutive>0)?clrLime:clrSilver);
   ObjectSetInteger(0, panelName+"_LastConsecValue", OBJPROP_COLOR, (lastConsecutive>0)?clrGold:clrSilver);
   ObjectSetInteger(0, panelName+"_MaxConsecValue", OBJPROP_COLOR, clrRed);

   // 更新收线倒计时
   UpdateBarCountdown();
}

// 更新布林带数值
void UpdateBBValues(string prefix, BBData &data)
{
   ObjectSetString(0, panelName+"_"+prefix+"UpperValue", OBJPROP_TEXT, DoubleToString(data.upper, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"MiddleValue", OBJPROP_TEXT, DoubleToString(data.middle, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"LowerValue", OBJPROP_TEXT, DoubleToString(data.lower, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"WidthValue", OBJPROP_TEXT, DoubleToString(data.width, _Digits));

   // 更新OHLC
   ObjectSetString(0, panelName+"_"+prefix+"OpenValue", OBJPROP_TEXT, "O:"+DoubleToString(data.open, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"HighValue", OBJPROP_TEXT, "H:"+DoubleToString(data.high, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"LowValue", OBJPROP_TEXT, "L:"+DoubleToString(data.low, _Digits));
   ObjectSetString(0, panelName+"_"+prefix+"CloseValue", OBJPROP_TEXT, "C:"+DoubleToString(data.close, _Digits));

   // 更新ATR和Volume
   ObjectSetString(0, panelName+"_"+prefix+"ATRValue", OBJPROP_TEXT, DoubleToString(data.atr,2));
   ObjectSetString(0, panelName+"_"+prefix+"VolValue", OBJPROP_TEXT, IntegerToString(data.volume));
}

// 更新收线倒计时
void UpdateBarCountdown()
{
   // 获取当前服务器时间
   datetime serverTime = TimeCurrent();

   // 获取当前K线的开始时间
   datetime barOpenTime = iTime(Symbol(), _Period, 0);

   // 计算下一根K线的开始时间
   datetime nextBarTime = 0;

   // 根据不同的时间周期计算下一根K线的开始时间
   switch(_Period) {
      case PERIOD_M1:  nextBarTime = barOpenTime + 60; break;
      case PERIOD_M5:  nextBarTime = barOpenTime + 300; break;
      case PERIOD_M15: nextBarTime = barOpenTime + 900; break;
      case PERIOD_M30: nextBarTime = barOpenTime + 1800; break;
      case PERIOD_H1:  nextBarTime = barOpenTime + 3600; break;
      case PERIOD_H4:  nextBarTime = barOpenTime + 14400; break;
      case PERIOD_D1:  nextBarTime = barOpenTime + 86400; break;
      case PERIOD_W1:  nextBarTime = barOpenTime + 604800; break;
      case PERIOD_MN1:
         // 月线需要特殊处理，这里简化处理为30天
         nextBarTime = barOpenTime + 2592000;
         break;
      default: nextBarTime = barOpenTime + PeriodSeconds(_Period);
   }

   // 计算剩余时间（秒）
   int remainingSeconds = (int)(nextBarTime - serverTime);

   // 如果剩余时间小于0，可能是由于服务器时间不同步，设为0
   if(remainingSeconds < 0) remainingSeconds = 0;

   // 格式化剩余时间
   string countdownText = "";

   // 提取时、分、秒
   int days = remainingSeconds / 86400;
   int hours = (remainingSeconds % 86400) / 3600;
   int minutes = (remainingSeconds % 3600) / 60;
   int seconds = remainingSeconds % 60;

   // 对于日线以上的周期，显示天数
   if(_Period >= PERIOD_D1) {
      countdownText = StringFormat("%d天 %02d:%02d:%02d", days, hours, minutes, seconds);
   }
   // 对于剩余时间大于1小时的情况，使用HH:MM:SS格式
   else if(remainingSeconds >= 3600) {
      // 如果是小时周期，显示总小时数
      if(_Period >= PERIOD_H1) {
         int totalHours = remainingSeconds / 3600;
         countdownText = StringFormat("%02d:%02d:%02d", totalHours, minutes, seconds);
      }
      // 否则使用标准HH:MM:SS格式
      else {
         countdownText = StringFormat("%02d:%02d:%02d", hours, minutes, seconds);
      }
   }
   // 对于剩余时间小于1小时的情况，使用MM:SS格式
   else {
      countdownText = StringFormat("%02d:%02d", minutes, seconds);
   }

   // 设置倒计时文本
   ObjectSetString(0, panelName+"_CountdownValue", OBJPROP_TEXT, countdownText);

   // 根据剩余时间设置颜色
   color countdownColor = clrYellow;

   // 如果剩余时间少于总时间的20%，显示为红色
   int periodSeconds = PeriodSeconds(_Period);
   if(remainingSeconds < periodSeconds * 0.2) {
      countdownColor = clrRed;
   }
   // 如果剩余时间少于总时间的50%，显示为橙色
   else if(remainingSeconds < periodSeconds * 0.5) {
      countdownColor = clrOrange;
   }

   ObjectSetInteger(0, panelName+"_CountdownValue", OBJPROP_COLOR, countdownColor);
}

#endif