//+------------------------------------------------------------------+
//|                                                  SignalDetector.mqh |
//|                                    Generated by ChatGPT/DeepSeek |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#ifndef __SIGNAL_DETECTOR_MQH__
#define __SIGNAL_DETECTOR_MQH__

#include "Common.mqh"

// 清理旧的箭头对象
void CleanupOldArrows(int maxArrows = 500)
{
   // 收集所有箭头信息
   string arrowNames[];
   datetime arrowTimes[];
   int count = 0;

   // 计算箭头数量并收集信息
   for(int i=ObjectsTotal(0, 0, OBJ_ARROW_CHECK)-1; i>=0; i--)
   {
      string name = ObjectName(0, i, 0, OBJ_ARROW_CHECK);
      if(StringFind(name, "BB_Width_Arrow_") == 0)
      {
         // 扩展数组
         ArrayResize(arrowNames, count + 1);
         ArrayResize(arrowTimes, count + 1);

         // 存储箭头信息
         arrowNames[count] = name;
         arrowTimes[count] = (datetime)ObjectGetInteger(0, name, OBJPROP_TIME);
         count++;
      }
   }

   // 如果箭头数量超过最大值，需要删除一些
   if(count > maxArrows)
   {
      // 按时间排序（冒泡排序 - 简单实现）
      for(int i=0; i<count-1; i++)
      {
         for(int j=0; j<count-i-1; j++)
         {
            if(arrowTimes[j] > arrowTimes[j+1])
            {
               // 交换时间
               datetime tempTime = arrowTimes[j];
               arrowTimes[j] = arrowTimes[j+1];
               arrowTimes[j+1] = tempTime;

               // 交换名称
               string tempName = arrowNames[j];
               arrowNames[j] = arrowNames[j+1];
               arrowNames[j+1] = tempName;
            }
         }
      }

      // 删除最旧的箭头（排序后在数组前面的）
      int deleteCount = count - maxArrows;
      for(int i=0; i<deleteCount; i++)
      {
         if(!ObjectDelete(0, arrowNames[i]))
         {
            Print("无法删除箭头: ", arrowNames[i], ", 错误: ", GetLastError());
         }
      }

      // 释放数组
      ArrayFree(arrowNames);
      ArrayFree(arrowTimes);
   }
}

// 验证条件
bool ValidateConditions() {
   bool result = prevBB.width > 0 && prevBB.width < Threshold_Width;
   return result;
}

// 创建信号箭头
void CreateSignalArrow(datetime time, double price)
{
   if(prevBB.isWritten) return;

   string arrowName = "BB_Width_Arrow_" + TimeToString(time);
   ObjectCreate(0, arrowName, OBJ_ARROW_CHECK, 0, time, price);
   ObjectSetInteger(0, arrowName, OBJPROP_COLOR, Arrow_Color);
   ObjectSetInteger(0, arrowName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, arrowName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
   ObjectSetInteger(0, arrowName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, arrowName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, arrowName, OBJPROP_ZORDER, 0);

   WriteBBDataToCSV(time, price, prevBB);

   // 清理旧箭头，保留最近的500个
   CleanupOldArrows(500);
}

// 写入CSV数据
void WriteBBDataToCSV(datetime time, double price, BBData &data)
{
   static int lastWeekNumber = -1;
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int daysToMonday = (timeStruct.day_of_week == 0) ? 6 : (timeStruct.day_of_week - 1);
   datetime mondayTime = TimeCurrent() - daysToMonday * 24 * 60 * 60;
   datetime sundayTime = mondayTime + 6 * 24 * 60 * 60;
   string weekRange = TimeToString(mondayTime, TIME_DATE) + "_" + TimeToString(sundayTime, TIME_DATE);
   int currentWeekNumber = (int)(mondayTime / (7 * 24 * 60 * 60));
   string weekNumber = IntegerToString(currentWeekNumber);
   string fileName = "BB_Width_Data_" + Symbol() + "_" + EnumToString(_Period) + "_Week" + weekNumber + "_"+ weekRange + ".csv";

   if(lastWeekNumber != currentWeekNumber)
   {
      lastWeekNumber = currentWeekNumber;
      if(FileIsExist(fileName, FILE_COMMON)) FileDelete(fileName, FILE_COMMON);
      int headerHandle = FileOpen(fileName, FILE_WRITE|FILE_CSV|FILE_COMMON, ',');
      if(headerHandle != INVALID_HANDLE)
      {
         FileWrite(headerHandle, "Time", "Price", "Upper", "Middle", "Lower", "Width", "Open", "High", "Low", "Close", "ATR", "Volume", "Timestamp");
         FileClose(headerHandle);
      }
   }

   int fileHandle = FileOpen(fileName, FILE_READ|FILE_WRITE|FILE_CSV|FILE_COMMON, ',');
   if(fileHandle != INVALID_HANDLE)
   {
      bool shouldWrite = (data.timestamp == 0 || data.timestamp != time);

      if(shouldWrite)
      {
         FileSeek(fileHandle, 0, SEEK_END);
         FileWrite(fileHandle, TimeToString(time), DoubleToString(price, _Digits), DoubleToString(data.upper, _Digits), DoubleToString(data.middle, _Digits), DoubleToString(data.lower, _Digits), DoubleToString(data.width, _Digits), DoubleToString(data.open, _Digits), DoubleToString(data.high, _Digits), DoubleToString(data.low, _Digits), DoubleToString(data.close, _Digits), DoubleToString(data.atr, _Digits), IntegerToString(data.volume), TimeToString(time));
         data.isWritten = true;
         data.timestamp = time;
      }

      // 确保在所有情况下都关闭文件句柄
      FileClose(fileHandle);
   }
   else
   {
      Print("无法打开CSV文件: ", fileName, ", 错误码: ", GetLastError());
   }
}

#endif