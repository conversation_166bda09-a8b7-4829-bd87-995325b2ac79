//+------------------------------------------------------------------+
//|                                              Compile_Test.mq5 |
//|                                    编译测试脚本                   |
//|                                         https://www.augment.com |
//+------------------------------------------------------------------+
#property copyright "Augment Agent"
#property version   "1.00"
#property description "编译测试脚本"
#property script_show_inputs

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("开始编译测试...");
    
    // 测试包含文件
    Print("测试Common.mqh包含...");
    #include "../Include/ZigZag_2B_EA/Common.mqh"
    
    Print("测试基本数据结构...");
    
    // 测试SwingPoint结构
    SwingPoint testSwing;
    testSwing.time = TimeCurrent();
    testSwing.price = 1.0000;
    testSwing.barIndex = 0;
    testSwing.type = SWING_HIGH;
    testSwing.isValid = true;
    testSwing.strength = 1.0;
    
    Print("SwingPoint测试通过");
    
    // 测试Pattern2B结构
    Pattern2B testPattern;
    testPattern.type = PATTERN_2B_BULLISH;
    testPattern.status = PATTERN_CONFIRMED;
    testPattern.formationTime = TimeCurrent();
    testPattern.keyLevel = 1.0000;
    testPattern.fakeBreakLevel = 0.9995;
    testPattern.entryLevel = 1.0005;
    testPattern.strength = 0.75;
    testPattern.isActive = true;
    
    Print("Pattern2B测试通过");
    
    // 测试工具函数
    Print("测试工具函数...");
    
    string timeStr = FormatTime(TimeCurrent());
    Print("FormatTime测试: ", timeStr);
    
    string doubleStr = FormatDouble(1.23456, 2);
    Print("FormatDouble测试: ", doubleStr);
    
    double normalizedPrice = NormalizePrice(1.234567);
    Print("NormalizePrice测试: ", DoubleToString(normalizedPrice, _Digits));
    
    // 测试验证函数
    bool isValidSwing = IsValidSwingPoint(testSwing);
    Print("IsValidSwingPoint测试: ", isValidSwing ? "通过" : "失败");
    
    bool isValidPattern = IsValidPattern(testPattern);
    Print("IsValidPattern测试: ", isValidPattern ? "通过" : "失败");
    
    // 测试字符串函数
    string patternTypeStr = GetPatternTypeString(PATTERN_2B_BULLISH);
    Print("GetPatternTypeString测试: ", patternTypeStr);
    
    string patternStatusStr = GetPatternStatusString(PATTERN_CONFIRMED);
    Print("GetPatternStatusString测试: ", patternStatusStr);
    
    color patternColor = GetPatternColor(PATTERN_2B_BULLISH);
    Print("GetPatternColor测试: ", ColorToString(patternColor));
    
    // 测试统计信息
    Print("测试统计信息初始化...");
    InitializeStatistics();
    Print("统计信息初始化完成");
    
    Print("所有编译测试通过！");
    Print("ZigZag 2B EA的基础组件可以正常编译和运行。");
}
