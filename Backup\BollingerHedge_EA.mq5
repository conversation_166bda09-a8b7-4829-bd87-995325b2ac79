//+------------------------------------------------------------------+
//|                                            BollingerHedge_EA.mq5 |
//|                                  布林带对冲策略EA - 只认赚不认赔  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property description "基于布林带的对冲交易策略，只设止盈不设止损，永不加仓"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
input group "基本参数";
input int inputMagicNum = 999999;           // 魔术号
input double inputLotSize = 0.25;           // 交易手数（25%仓位）
input int inputMaxLeverage = 10;            // 最大杠杆倍数

input group "布林带参数";
input int inputBBPeriod = 20;               // 布林带周期
input double inputBBDeviation = 2.0;       // 布林带标准差
input ENUM_TIMEFRAMES inputTimeframe = PERIOD_H1; // 时间周期

input group "止盈参数";
input double inputTakeProfitPoints = 500;   // 止盈点数
input bool inputUseATRTakeProfit = true;    // 使用ATR动态止盈
input double inputATRMultiplier = 2.0;      // ATR倍数
input int inputATRPeriod = 14;              // ATR周期

input group "锁仓参数";
input double inputLockDistance = 5000;      // 锁仓距离（点数）
input bool inputAutoLock = true;            // 自动锁仓
input double inputMaxDrawdownPercent = 20;  // 最大回撤百分比触发锁仓

input group "风险控制";
input double inputMaxPositions = 4;         // 最大持仓数量
input bool inputEnableTimeFilter = true;   // 启用时间过滤
input int inputMinHoursBetweenTrades = 2;   // 最小开仓间隔（小时）

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
CTrade trade;
CPositionInfo positionInfo;

datetime lastTradeTime = 0;
double accountStartBalance = 0;
bool isLocked = false;
datetime lockTime = 0;

//+------------------------------------------------------------------+
//| EA初始化函数                                                      |
//+------------------------------------------------------------------+
int OnInit() {
    trade.SetExpertMagicNumber(inputMagicNum);
    accountStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);

    // 启动定时器，每30秒更新一次信息面板
    EventSetTimer(30);

    Print("布林带对冲策略EA启动");
    Print("策略核心：只认赚不认赔，永不加仓");
    Print("开仓条件：布林上轨开空，下轨开多");
    Print("风险控制：只设止盈不设止损，单边行情自动锁仓");
    Print("参数设置：");
    Print("  - 交易手数：", inputLotSize);
    Print("  - 布林带周期：", inputBBPeriod);
    Print("  - 时间周期：", EnumToString(inputTimeframe));
    Print("  - 止盈点数：", inputTakeProfitPoints);
    Print("  - 锁仓距离：", inputLockDistance);
    Print("  - 最大持仓：", inputMaxPositions);

    // 创建初始信息面板
    CreateInfoPanel();

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| EA主要逻辑函数                                                    |
//+------------------------------------------------------------------+
void OnTick() {
    // 检查是否为新K线
    if(!IsNewBar()) return;

    // 风险检查
    if(!RiskCheck()) return;

    // 检查锁仓状态
    CheckLockStatus();

    // 如果已锁仓，暂停新开仓
    if(isLocked) {
        CheckUnlockConditions();
        return;
    }

    // 检查开仓条件
    CheckTradingSignals();

    // 检查止盈
    CheckTakeProfit();
}

//+------------------------------------------------------------------+
//| 检查是否为新K线                                                   |
//+------------------------------------------------------------------+
bool IsNewBar() {
    static datetime prevBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, inputTimeframe, 0);

    if(prevBarTime != currentBarTime) {
        prevBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 风险检查                                                          |
//+------------------------------------------------------------------+
bool RiskCheck() {
    // 检查最大持仓数量
    if(GetPositionCount() >= inputMaxPositions) {
        return false;
    }

    // 检查时间间隔
    if(inputEnableTimeFilter) {
        if(TimeCurrent() - lastTradeTime < inputMinHoursBetweenTrades * 3600) {
            return false;
        }
    }

    // 检查账户余额
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double drawdown = (accountStartBalance - currentBalance) / accountStartBalance * 100;

    if(drawdown > inputMaxDrawdownPercent && inputAutoLock) {
        TriggerLock("最大回撤触发锁仓");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals() {
    // 获取布林带数据
    double upperBand[], lowerBand[], middleBand[];
    ArraySetAsSeries(upperBand, true);
    ArraySetAsSeries(lowerBand, true);
    ArraySetAsSeries(middleBand, true);

    int bbHandle = iBands(_Symbol, inputTimeframe, inputBBPeriod, 0, inputBBDeviation, PRICE_CLOSE);
    CopyBuffer(bbHandle, 1, 0, 3, upperBand);  // 上轨
    CopyBuffer(bbHandle, 2, 0, 3, lowerBand);  // 下轨
    CopyBuffer(bbHandle, 0, 0, 3, middleBand); // 中轨

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // 检查是否触及布林上轨（开空条件）
    if(currentPrice >= upperBand[1] && !HasPosition(POSITION_TYPE_SELL)) {
        double tp = CalculateTakeProfit(false, currentPrice);
        if(trade.Sell(inputLotSize, _Symbol, currentPrice, 0, tp, "布林上轨开空")) {
            lastTradeTime = TimeCurrent();
            Print("布林上轨开空成功，价格：", currentPrice, "，止盈：", tp);
        }
    }

    // 检查是否触及布林下轨（开多条件）
    if(ask <= lowerBand[1] && !HasPosition(POSITION_TYPE_BUY)) {
        double tp = CalculateTakeProfit(true, ask);
        if(trade.Buy(inputLotSize, _Symbol, ask, 0, tp, "布林下轨开多")) {
            lastTradeTime = TimeCurrent();
            Print("布林下轨开多成功，价格：", ask, "，止盈：", tp);
        }
    }
}

//+------------------------------------------------------------------+
//| 计算止盈价格                                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit(bool isBuy, double entryPrice) {
    double tpDistance = inputTakeProfitPoints * _Point;

    // 使用ATR动态止盈
    if(inputUseATRTakeProfit) {
        int atrHandle = iATR(_Symbol, inputTimeframe, inputATRPeriod);
        double atrBuffer[];
        ArraySetAsSeries(atrBuffer, true);
        CopyBuffer(atrHandle, 0, 1, 1, atrBuffer);
        tpDistance = atrBuffer[0] * inputATRMultiplier;
    }

    if(isBuy) {
        return entryPrice + tpDistance;
    } else {
        return entryPrice - tpDistance;
    }
}

//+------------------------------------------------------------------+
//| 检查是否有指定类型的持仓                                           |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_POSITION_TYPE posType) {
    for(int i = 0; i < PositionsTotal(); i++) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == _Symbol &&
               positionInfo.Magic() == inputMagicNum &&
               positionInfo.PositionType() == posType) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 获取持仓数量                                                      |
//+------------------------------------------------------------------+
int GetPositionCount() {
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == inputMagicNum) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 检查止盈                                                          |
//+------------------------------------------------------------------+
void CheckTakeProfit() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == inputMagicNum) {
                double currentPrice = (positionInfo.PositionType() == POSITION_TYPE_BUY) ?
                                    SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                    SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                double profit = positionInfo.Profit();

                // 只有盈利的单子才平仓
                if(profit > 0) {
                    if(trade.PositionClose(positionInfo.Ticket())) {
                        Print("止盈平仓成功，盈利：", profit);
                        // 平仓后立即寻找新的开仓机会
                        lastTradeTime = 0; // 重置时间限制
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查锁仓状态                                                      |
//+------------------------------------------------------------------+
void CheckLockStatus() {
    if(isLocked) return;

    // 检查是否需要触发锁仓
    double maxLoss = 0;
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    for(int i = 0; i < PositionsTotal(); i++) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == inputMagicNum) {
                double openPrice = positionInfo.PriceOpen();
                double loss = 0;

                if(positionInfo.PositionType() == POSITION_TYPE_BUY) {
                    loss = (openPrice - currentPrice) / _Point;
                } else {
                    loss = (currentPrice - openPrice) / _Point;
                }

                if(loss > maxLoss) {
                    maxLoss = loss;
                }
            }
        }
    }

    // 如果单笔亏损超过锁仓距离，触发锁仓
    if(maxLoss > inputLockDistance) {
        TriggerLock("单笔亏损超过锁仓距离");
    }
}

//+------------------------------------------------------------------+
//| 触发锁仓                                                          |
//+------------------------------------------------------------------+
void TriggerLock(string reason) {
    if(isLocked) return;

    isLocked = true;
    lockTime = TimeCurrent();

    Print("触发锁仓 - 原因：", reason);
    Print("当前时间：", TimeToString(lockTime));
    Print("锁仓期间暂停新开仓，等待市场稳定");

    // 实际的锁仓逻辑 - 计算净头寸并开立反向对冲单
    double netBuyVolume = 0.0;  // 净多头头寸
    double netSellVolume = 0.0; // 净空头头寸
    
    // 计算当前持仓的净头寸
    for(int i = 0; i < PositionsTotal(); i++) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == inputMagicNum) {
                if(positionInfo.PositionType() == POSITION_TYPE_BUY) {
                    netBuyVolume += positionInfo.Volume();
                } else {
                    netSellVolume += positionInfo.Volume();
                }
            }
        }
    }
    
    // 计算需要对冲的头寸
    double netVolume = netBuyVolume - netSellVolume;
    
    // 开立反向对冲单
    if(netVolume > 0) { // 净多头，需要开空单对冲
        if(trade.Sell(netVolume, _Symbol, 0, 0, 0, "锁仓对冲空单")) {
            Print("锁仓成功 - 开立对冲空单，数量：", netVolume);
        } else {
            Print("锁仓失败 - 无法开立对冲空单，错误：", GetLastError());
        }
    } else if(netVolume < 0) { // 净空头，需要开多单对冲
        if(trade.Buy(-netVolume, _Symbol, 0, 0, 0, "锁仓对冲多单")) {
            Print("锁仓成功 - 开立对冲多单，数量：", -netVolume);
        } else {
            Print("锁仓失败 - 无法开立对冲多单，错误：", GetLastError());
        }
    } else {
        Print("锁仓检查 - 当前无需对冲，多空头寸已平衡");
    }
}

//+------------------------------------------------------------------+
//| 检查解锁条件                                                      |
//+------------------------------------------------------------------+
void CheckUnlockConditions() {
    if(!isLocked) return;

    // 锁仓至少持续24小时
    if(TimeCurrent() - lockTime < 24 * 3600) return;

    // 检查市场是否进入波动期（可以通过ATR等指标判断）
    int atrHandle = iATR(_Symbol, inputTimeframe, inputATRPeriod);
    double atrBuffer[];
    ArraySetAsSeries(atrBuffer, true);
    CopyBuffer(atrHandle, 0, 1, 10, atrBuffer);

    // 计算ATR平均值
    double avgATR = 0;
    for(int i = 0; i < 10; i++) {
        avgATR += atrBuffer[i];
    }
    avgATR /= 10;

    // 如果当前ATR小于平均值，说明市场趋于稳定
    if(atrBuffer[0] < avgATR * 0.8) {
        isLocked = false;
        Print("解除锁仓 - 市场趋于稳定，恢复正常交易");
    }
}

//+------------------------------------------------------------------+
//| 获取账户信息                                                      |
//+------------------------------------------------------------------+
string GetAccountInfo() {
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

    double drawdown = 0;
    if(accountStartBalance > 0) {
        drawdown = (accountStartBalance - balance) / accountStartBalance * 100;
    }

    string info = StringFormat(
        "账户信息 | 余额:%.2f | 净值:%.2f | 已用保证金:%.2f | 可用保证金:%.2f | 回撤:%.2f%%",
        balance, equity, margin, freeMargin, drawdown
    );

    return info;
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                      |
//+------------------------------------------------------------------+
void CreateInfoPanel() {
    string panelName = "BollingerHedge_InfoPanel";

    if(ObjectFind(0, panelName) < 0) {
        ObjectCreate(0, panelName, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(0, panelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, panelName, OBJPROP_XDISTANCE, 10);
        ObjectSetInteger(0, panelName, OBJPROP_YDISTANCE, 30);
        ObjectSetInteger(0, panelName, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, panelName, OBJPROP_FONTSIZE, 10);
        ObjectSetString(0, panelName, OBJPROP_FONT, "Arial");
    }

    string info = "布林带对冲策略EA\n";
    info += StringFormat("魔术号: %d\n", inputMagicNum);
    info += StringFormat("手数: %.2f\n", inputLotSize);
    info += StringFormat("当前持仓: %d\n", GetPositionCount());
    info += StringFormat("锁仓状态: %s\n", isLocked ? "已锁仓" : "正常");
    info += GetAccountInfo();

    ObjectSetString(0, panelName, OBJPROP_TEXT, info);
}

//+------------------------------------------------------------------+
//| EA反初始化函数                                                    |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // 停止定时器
    EventKillTimer();

    // 删除信息面板
    ObjectDelete(0, "BollingerHedge_InfoPanel");

    Print("布林带对冲策略EA已停止");
    Print("停止原因：", reason);

    // 输出最终统计信息
    double finalBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double totalReturn = ((finalBalance - accountStartBalance) / accountStartBalance) * 100;
    Print("最终收益率：", DoubleToString(totalReturn, 2), "%");
}

//+------------------------------------------------------------------+
//| 定时器函数                                                        |
//+------------------------------------------------------------------+
void OnTimer() {
    CreateInfoPanel();
}
