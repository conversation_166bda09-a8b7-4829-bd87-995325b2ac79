#property copyright "Copyright 2024, Trae AI"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//|                                                  EMA_Cross_M5.mq5 |
//|                                 过滤未完成K线的双EMA金叉/死叉检测       |
//+------------------------------------------------------------------+

// 外部参数
input int FastMAPeriod = 13;     // 快速EMA周期
input int SlowMAPeriod = 34;    // 慢速EMA周期
input ENUM_TIMEFRAMES TimeFrame = PERIOD_M5; // 图表周期
input int DotSize = 5;         // 圆点尺寸（像素）
input int MaxDots = 200;        // 最大保留的圆点数量
input ENUM_APPLIED_PRICE PriceType = PRICE_CLOSE;  // 应用价格类型

// 图形对象参数
#define GOLD_COLOR clrGold
#define DEAD_COLOR clrRed
#define DOT_PREFIX "EMA_Cross_Dot_"
#define LABEL_PREFIX "LABEL_EMA_Cross_Dot_"

// 定义交叉标记结构体
struct SCrossMark
{
   string dotName;     // 圆点对象名称
   string labelName;   // 标签对象名称
   datetime time;      // 交叉时间
   double price;       // 交叉价格
   bool isGolden;      // 是否金叉
};

// 全局变量
int fastMAHandle, slowMAHandle;
double fastMA[3], slowMA[3];
datetime lastCrossTime;
datetime lastBarTime;   // 记录上一次检测的K线时间
int markCount = 0;      // 标记计数器
SCrossMark crossMarks[]; // 交叉标记数组

int OnInit()
{
   // 先释放可能已存在的指标句柄，防止内存泄漏
   if(fastMAHandle != INVALID_HANDLE)
      IndicatorRelease(fastMAHandle);
   if(slowMAHandle != INVALID_HANDLE)
      IndicatorRelease(slowMAHandle);
      
   // 初始化EMA指标
   fastMAHandle = iMA(_Symbol, TimeFrame, FastMAPeriod, 0, MODE_EMA, PriceType);
   slowMAHandle = iMA(_Symbol, TimeFrame, SlowMAPeriod, 0, MODE_EMA, PriceType);

   if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE){
      Alert("EMA指标初始化失败");
      return(INIT_FAILED);
   }
   
   // 初始化交叉标记数组
   ArrayResize(crossMarks, MaxDots);
   markCount = 0;
   
   // 初始化最后K线时间
   datetime timeArray[1];
   CopyTime(_Symbol, TimeFrame, 0, 1, timeArray); // 获取最新K线时间
   lastBarTime = timeArray[0];
   
   // 输出初始化日志
   Print("EMA交叉指标初始化成功 - 快速周期:", FastMAPeriod, " 慢速周期:", SlowMAPeriod, " 时间周期:", EnumToString((ENUM_TIMEFRAMES)TimeFrame));
   
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   // 删除所有圆点和标签标记
   DeleteAllDots();
   
   // 释放指标句柄
   if(fastMAHandle != INVALID_HANDLE)
      IndicatorRelease(fastMAHandle);
   if(slowMAHandle != INVALID_HANDLE)
      IndicatorRelease(slowMAHandle);
   
   // 输出终止日志
   Print("EMA交叉指标已终止运行，原因代码:", reason);
}

//+------------------------------------------------------------------+
//| OnTick函数                                                       |
//+------------------------------------------------------------------+
void OnTick()
{
   // 每次tick都检查是否新K线生成
   CheckEMACross();
}

//+------------------------------------------------------------------+
//| 主检测函数（仅在K线闭合时执行）                                  |
//+------------------------------------------------------------------+
void CheckEMACross()
{
   datetime currentBarTime[1];
   
   // 获取当前最新K线时间
   if(CopyTime(_Symbol, TimeFrame, 0, 1, currentBarTime) < 1)
   {
      Print("时间数据获取失败！");
      return;
   }

   //--- 如果K线时间未变化（未生成新K线），直接返回
   if(currentBarTime[0] == lastBarTime) return;

   //--- 更新最后检测时间
   lastBarTime = currentBarTime[0];

   //--- 复制EMA数据（此时使用前一根已闭合K线的数据）
   // 先尝试获取更多数据点以提高交叉点定位精度
   int fastCopied = CopyBuffer(fastMAHandle, 0, 1, 5, fastMA);
   int slowCopied = CopyBuffer(slowMAHandle, 0, 1, 5, slowMA);
   
   // 如果无法获取5个数据点，尝试获取3个
   if(fastCopied < 5 || slowCopied < 5)
   {
      fastCopied = CopyBuffer(fastMAHandle, 0, 1, 3, fastMA);
      slowCopied = CopyBuffer(slowMAHandle, 0, 1, 3, slowMA);
      
      // 如果仍然无法获取足够数据，则返回
      if(fastCopied < 2 || slowCopied < 2)
      {
         Print("EMA数据复制失败！复制数量: 快速=", fastCopied, " 慢速=", slowCopied);
         return;
      }
   }

   double previousFast = fastMA[0];  
   double currentFast  = fastMA[1]; 
   double previousSlow = slowMA[0];
   double currentSlow  = slowMA[1];

   // 检测交叉 - 使用已闭合K线数据
   bool goldenCross = (previousFast < previousSlow && currentFast > currentSlow);
   bool deadCross = (previousFast > previousSlow && currentFast < currentSlow);

   // 获取实际交叉发生的K线时间（索引1表示最近收线的K线）
   datetime crossTime = iTime(_Symbol, TimeFrame, 1);

   if((goldenCross || deadCross) && (lastCrossTime == 0 || crossTime > lastCrossTime))
   {
      // 计算交叉点价格
      double crossPrice = 0;
      // crossTime已在前面定义
      
      // 使用更精确的多项式插值法计算交叉点
      if(goldenCross || deadCross)
      {
         // 获取两条均线在前几个K线的数据点
         double fastPoints[5], slowPoints[5];
         datetime timePoints[5];
         int maxPoints = MathMin(fastCopied, slowCopied); // 使用实际获取到的数据点数量
         
         // 复制数据到临时数组
         for(int i = 0; i < maxPoints; i++)
         {
            if(i < fastCopied) fastPoints[i] = fastMA[i];
            if(i < slowCopied) slowPoints[i] = slowMA[i];
            timePoints[i] = iTime(_Symbol, TimeFrame, i+1); // i+1因为我们从索引1开始复制数据
         }
         
         // 对于金叉，我们需要找到快线从下穿过慢线的点
         if(goldenCross)
         {
            // 精确定位交叉点
            for(int i = 0; i < MathMin(maxPoints-1, 4); i++) // 根据实际数据点数量限制检查区间
            {
               // 如果在这个区间内存在交叉
               if((fastPoints[i] > slowPoints[i] && fastPoints[i+1] < slowPoints[i+1]) ||
                  (fastPoints[i] < slowPoints[i] && fastPoints[i+1] > slowPoints[i+1]))
               {
                  // 使用线性插值计算交叉点
                  double fastSlope = (fastPoints[i] - fastPoints[i+1]) / (timePoints[i] - timePoints[i+1]);
                  double slowSlope = (slowPoints[i] - slowPoints[i+1]) / (timePoints[i] - timePoints[i+1]);
                  double slopeDiff = fastSlope - slowSlope;
                  
                  if(MathAbs(slopeDiff) > 0.0000001) // 避免除以接近零的值
                  {
                     // 计算交叉时间偏移量
                     double timeDelta = (slowPoints[i+1] - fastPoints[i+1]) / slopeDiff;
                     
                     // 限制在区间内
                     double intervalDuration = timePoints[i] - timePoints[i+1];
                     timeDelta = MathMax(0, MathMin(timeDelta, intervalDuration));
                     
                     // 计算精确交叉时间和价格
                     crossTime = timePoints[i+1] + (datetime)timeDelta;
                     crossPrice = fastPoints[i+1] + fastSlope * timeDelta;
                     break;
                  }
                  else
                  {
                     // 斜率几乎相等，使用中点
                     crossTime = timePoints[i+1] + (datetime)((timePoints[i] - timePoints[i+1]) / 2);
                     crossPrice = (fastPoints[i+1] + slowPoints[i+1]) / 2;
                     break;
                  }
               }
            }
         }
         // 对于死叉，我们需要找到快线从上穿过慢线的点
         else if(deadCross)
         {
            // 精确定位交叉点
            for(int i = 0; i < 4; i++) // 只检查前4个区间
            {
               // 如果在这个区间内存在交叉
               if((fastPoints[i] < slowPoints[i] && fastPoints[i+1] > slowPoints[i+1]) ||
                  (fastPoints[i] > slowPoints[i] && fastPoints[i+1] < slowPoints[i+1]))
               {
                  // 使用线性插值计算交叉点
                  double fastSlope = (fastPoints[i] - fastPoints[i+1]) / (timePoints[i] - timePoints[i+1]);
                  double slowSlope = (slowPoints[i] - slowPoints[i+1]) / (timePoints[i] - timePoints[i+1]);
                  double slopeDiff = fastSlope - slowSlope;
                  
                  if(MathAbs(slopeDiff) > 0.0000001) // 避免除以接近零的值
                  {
                     // 计算交叉时间偏移量
                     double timeDelta = (slowPoints[i+1] - fastPoints[i+1]) / slopeDiff;
                     
                     // 限制在区间内
                     double intervalDuration = timePoints[i] - timePoints[i+1];
                     timeDelta = MathMax(0, MathMin(timeDelta, intervalDuration));
                     
                     // 计算精确交叉时间和价格
                     crossTime = timePoints[i+1] + (datetime)timeDelta;
                     crossPrice = fastPoints[i+1] + fastSlope * timeDelta;
                     break;
                  }
                  else
                  {
                     // 斜率几乎相等，使用中点
                     crossTime = timePoints[i+1] + (datetime)((timePoints[i] - timePoints[i+1]) / 2);
                     crossPrice = (fastPoints[i+1] + slowPoints[i+1]) / 2;
                     break;
                  }
               }
            }
         }
         
         // 如果上面的算法没有找到交叉点（这种情况很少见），使用简单的线性插值
         if(crossPrice == 0)
         {
            double fastSlope = (fastMA[0] - fastMA[1]) / PeriodSeconds(TimeFrame);
            double slowSlope = (slowMA[0] - slowMA[1]) / PeriodSeconds(TimeFrame);
            double slopeDiff = fastSlope - slowSlope;
            
            if(MathAbs(slopeDiff) < 0.0000001) // 斜率几乎相等
            {
               crossPrice = (fastMA[0] + slowMA[0]) / 2;
               // 保持crossTime不变
            }
            else
            {
               double timeDelta = (slowMA[1] - fastMA[1]) / slopeDiff;
               timeDelta = MathMax(0, MathMin(timeDelta, PeriodSeconds(TimeFrame)));
               crossPrice = fastMA[1] + fastSlope * timeDelta;
               crossTime = crossTime + (datetime)timeDelta;
            }
         }
      }

      // 绘制交叉标记
      DrawCrossMark(crossTime, crossPrice, goldenCross);
      
      // 更新最后交叉时间
      lastCrossTime = crossTime;
      
      // 输出交叉日志
      string crossType = goldenCross ? "金叉" : "死叉";
      string logMessage = StringFormat("%s发生在时间: %s, 价格: %.5f", 
                                       crossType, TimeToString(crossTime, TIME_DATE | TIME_SECONDS), 
                                       crossPrice);
      string logMessage4FastMA = StringFormat("快速EMA: %.5f, %.5f, %.5f", 
                                        fastMA[0], fastMA[1], fastMA[2]);
      string logMessage4SlowMA = StringFormat("慢速EMA: %.5f, %.5f, %.5f", 
                                        slowMA[0], slowMA[1], slowMA[2]);
      Print(logMessage);
      Print(logMessage4FastMA);
      Print(logMessage4SlowMA);
   }
}

// 添加交叉标记到数组并管理数量
void AddCrossMark(string dotName, string labelName, datetime crossTime, double crossPrice, bool isGolden)
{
   // 检查数组大小是否足够
   if(ArraySize(crossMarks) < MaxDots)
   {
      ArrayResize(crossMarks, MaxDots);
   }
   
   // 如果标记数量超过最大值，删除最旧的标记
   if(markCount >= MaxDots)
   {
      // 删除最旧的圆点和标签
      if(!ObjectDelete(0, crossMarks[0].dotName))
         Print("删除圆点标记失败，错误码:", GetLastError());
      if(!ObjectDelete(0, crossMarks[0].labelName))
         Print("删除标签标记失败，错误码:", GetLastError());
         
      // 移动数组元素
      for(int i = 0; i < MaxDots - 1; i++)
      {
         crossMarks[i] = crossMarks[i + 1];
      }
      
      // 添加新标记
      crossMarks[MaxDots - 1].dotName = dotName;
      crossMarks[MaxDots - 1].labelName = labelName;
      crossMarks[MaxDots - 1].time = crossTime;
      crossMarks[MaxDots - 1].price = crossPrice;
      crossMarks[MaxDots - 1].isGolden = isGolden;
   }
   else
   {
      // 添加新标记
      crossMarks[markCount].dotName = dotName;
      crossMarks[markCount].labelName = labelName;
      crossMarks[markCount].time = crossTime;
      crossMarks[markCount].price = crossPrice;
      crossMarks[markCount].isGolden = isGolden;
      markCount++;
   }
}

//+------------------------------------------------------------------+
//| 绘制交叉标记函数                                               |
//+------------------------------------------------------------------+
void DrawCrossMark(datetime crossTime, double crossPrice, bool isGoldenCross)
{
   // 创建新圆点标记，使用前缀常量和更精确的时间戳
   string objName = StringFormat("%s%d_%d", DOT_PREFIX, (int)crossTime, GetTickCount());
   if(!ObjectCreate(0, objName, OBJ_ELLIPSE, 0, crossTime, crossPrice + DotSize/2, crossTime, crossPrice - DotSize/2))
   {
      Print("创建圆点标记失败，错误码:", GetLastError());
      return;
   }
   
   // 设置圆点属性
   ObjectSetInteger(0, objName, OBJPROP_COLOR, isGoldenCross ? GOLD_COLOR : DEAD_COLOR);
   ObjectSetInteger(0, objName, OBJPROP_WIDTH, 2);
   ObjectSetInteger(0, objName, OBJPROP_FILL, true);
   ObjectSetInteger(0, objName, OBJPROP_BACK, false);
   ObjectSetInteger(0, objName, OBJPROP_ZORDER, 100);
   
   // 添加文字标签说明交叉类型，使用前缀常量和更精确的时间戳
   string labelName = StringFormat("%s%d_%d", LABEL_PREFIX, (int)crossTime, GetTickCount());
   if(!ObjectCreate(0, labelName, OBJ_TEXT, 0, crossTime, crossPrice - DotSize))
   {
      Print("创建标签标记失败，错误码:", GetLastError());
      // 如果标签创建失败，删除已创建的圆点
      ObjectDelete(0, objName);
      return;
   }
   
   ObjectSetString(0, labelName, OBJPROP_TEXT, isGoldenCross ? "金叉" : "死叉");
   ObjectSetInteger(0, labelName, OBJPROP_COLOR, isGoldenCross ? GOLD_COLOR : DEAD_COLOR);
   ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, labelName, OBJPROP_BACK, false);
   ObjectSetInteger(0, labelName, OBJPROP_ZORDER, 101);

   // 添加到数组并管理数量，同时管理圆点和标签
   AddCrossMark(objName, labelName, crossTime, crossPrice, isGoldenCross);
   
   // 绘制对象后刷新图表，确保对象可见
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 删除所有圆点和标签标记                                        |
//+------------------------------------------------------------------+
void DeleteAllDots()
{
   // 先删除所有已存储的标记对象
   for(int i = 0; i < markCount; i++)
   {
      if(!ObjectDelete(0, crossMarks[i].dotName))
         Print("删除圆点对象失败: ", crossMarks[i].dotName, " 错误码:", GetLastError());
      
      if(!ObjectDelete(0, crossMarks[i].labelName))
         Print("删除标签对象失败: ", crossMarks[i].labelName, " 错误码:", GetLastError());
   }
   
   // 为安全起见，再遍历图表上所有对象，确保删除所有相关对象
   long chartId = ChartID();
   int totalObjects = ObjectsTotal(chartId, 0);
   
   // 从后向前遍历，因为删除对象后索引会变化
   for(int i = totalObjects - 1; i >= 0; i--)
   {
      string objName = ObjectName(chartId, 0, i);
      
      // 删除圆点
      if(StringSubstr(objName, 0, StringLen(DOT_PREFIX)) == DOT_PREFIX)
      {
         if(!ObjectDelete(chartId, objName))
            Print("删除圆点对象失败: ", objName, " 错误码:", GetLastError());
      }
      
      // 删除标签
      if(StringSubstr(objName, 0, StringLen(LABEL_PREFIX)) == LABEL_PREFIX)
      {
         if(!ObjectDelete(chartId, objName))
            Print("删除标签对象失败: ", objName, " 错误码:", GetLastError());
      }
   }
   
   // 重置计数器和数组
   markCount = 0;
   ArrayResize(crossMarks, MaxDots);
}