//+------------------------------------------------------------------+
//|                                              Multi_Williams_R.mq5 |
//|                                                                   |
//|                                         https://www.example.com   |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      "https://www.example.com"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 10
#property indicator_plots   10
#property indicator_minimum -100
#property indicator_maximum 0

//--- 输入参数：最多支持10个不同周期的威廉指标
input group "=== 威廉指标1设置 ==="
input bool   Enable_WPR1 = true;         // 启用威廉指标1
input int    WPR_Period1 = 14;           // 威廉指标1周期
input color  WPR_Color1 = clrRed;        // 威廉指标1颜色
input int    WPR_Width1 = 1;             // 威廉指标1线宽
input ENUM_LINE_STYLE WPR_Style1 = STYLE_SOLID; // 威廉指标1线型

input group "=== 威廉指标2设置 ==="
input bool   Enable_WPR2 = true;         // 启用威廉指标2
input int    WPR_Period2 = 28;           // 威廉指标2周期
input color  WPR_Color2 = clrBlue;       // 威廉指标2颜色
input int    WPR_Width2 = 1;             // 威廉指标2线宽
input ENUM_LINE_STYLE WPR_Style2 = STYLE_SOLID; // 威廉指标2线型

input group "=== 威廉指标3设置 ==="
input bool   Enable_WPR3 = false;        // 启用威廉指标3
input int    WPR_Period3 = 56;           // 威廉指标3周期
input color  WPR_Color3 = clrGreen;      // 威廉指标3颜色
input int    WPR_Width3 = 1;             // 威廉指标3线宽
input ENUM_LINE_STYLE WPR_Style3 = STYLE_SOLID; // 威廉指标3线型

input group "=== 威廉指标4设置 ==="
input bool   Enable_WPR4 = false;        // 启用威廉指标4
input int    WPR_Period4 = 84;           // 威廉指标4周期
input color  WPR_Color4 = clrMagenta;    // 威廉指标4颜色
input int    WPR_Width4 = 1;             // 威廉指标4线宽
input ENUM_LINE_STYLE WPR_Style4 = STYLE_SOLID; // 威廉指标4线型

input group "=== 威廉指标5设置 ==="
input bool   Enable_WPR5 = false;        // 启用威廉指标5
input int    WPR_Period5 = 112;          // 威廉指标5周期
input color  WPR_Color5 = clrOrange;     // 威廉指标5颜色
input int    WPR_Width5 = 1;             // 威廉指标5线宽
input ENUM_LINE_STYLE WPR_Style5 = STYLE_SOLID; // 威廉指标5线型

input group "=== 威廉指标6设置 ==="
input bool   Enable_WPR6 = false;        // 启用威廉指标6
input int    WPR_Period6 = 140;          // 威廉指标6周期
input color  WPR_Color6 = clrCyan;       // 威廉指标6颜色
input int    WPR_Width6 = 1;             // 威廉指标6线宽
input ENUM_LINE_STYLE WPR_Style6 = STYLE_SOLID; // 威廉指标6线型

input group "=== 威廉指标7设置 ==="
input bool   Enable_WPR7 = false;        // 启用威廉指标7
input int    WPR_Period7 = 168;          // 威廉指标7周期
input color  WPR_Color7 = clrYellow;     // 威廉指标7颜色
input int    WPR_Width7 = 1;             // 威廉指标7线宽
input ENUM_LINE_STYLE WPR_Style7 = STYLE_SOLID; // 威廉指标7线型

input group "=== 威廉指标8设置 ==="
input bool   Enable_WPR8 = false;        // 启用威廉指标8
input int    WPR_Period8 = 196;          // 威廉指标8周期
input color  WPR_Color8 = clrPurple;     // 威廉指标8颜色
input int    WPR_Width8 = 1;             // 威廉指标8线宽
input ENUM_LINE_STYLE WPR_Style8 = STYLE_SOLID; // 威廉指标8线型

input group "=== 威廉指标9设置 ==="
input bool   Enable_WPR9 = false;        // 启用威廉指标9
input int    WPR_Period9 = 224;          // 威廉指标9周期
input color  WPR_Color9 = clrBrown;      // 威廉指标9颜色
input int    WPR_Width9 = 1;             // 威廉指标9线宽
input ENUM_LINE_STYLE WPR_Style9 = STYLE_SOLID; // 威廉指标9线型

input group "=== 威廉指标10设置 ==="
input bool   Enable_WPR10 = false;       // 启用威廉指标10
input int    WPR_Period10 = 252;         // 威廉指标10周期
input color  WPR_Color10 = clrGray;      // 威廉指标10颜色
input int    WPR_Width10 = 1;            // 威廉指标10线宽
input ENUM_LINE_STYLE WPR_Style10 = STYLE_SOLID; // 威廉指标10线型

input group "=== 水平线设置 ==="
input bool   Show_Levels = true;         // 显示超买超卖水平线
input int    Overbought_Level = -20;     // 超买水平线位置
input int    Oversold_Level = -80;       // 超卖水平线位置
input color  Levels_Color = clrDimGray;  // 水平线颜色
input ENUM_LINE_STYLE Levels_Style = STYLE_DOT; // 水平线线型

//--- 指标缓冲区
double WPR_Buffer1[];
double WPR_Buffer2[];
double WPR_Buffer3[];
double WPR_Buffer4[];
double WPR_Buffer5[];
double WPR_Buffer6[];
double WPR_Buffer7[];
double WPR_Buffer8[];
double WPR_Buffer9[];
double WPR_Buffer10[];

//--- 全局变量
int activeIndicators = 0;
string indicatorName = "Multi Williams %R";

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                             |
//+------------------------------------------------------------------+
int OnInit()
{
   // 设置指标名称
   IndicatorSetString(INDICATOR_SHORTNAME, indicatorName);
   
   // 初始化指标缓冲区
   InitBuffer(0, WPR_Buffer1, "Williams %R (" + IntegerToString(WPR_Period1) + ")", Enable_WPR1, WPR_Color1, WPR_Width1, WPR_Style1);
   InitBuffer(1, WPR_Buffer2, "Williams %R (" + IntegerToString(WPR_Period2) + ")", Enable_WPR2, WPR_Color2, WPR_Width2, WPR_Style2);
   InitBuffer(2, WPR_Buffer3, "Williams %R (" + IntegerToString(WPR_Period3) + ")", Enable_WPR3, WPR_Color3, WPR_Width3, WPR_Style3);
   InitBuffer(3, WPR_Buffer4, "Williams %R (" + IntegerToString(WPR_Period4) + ")", Enable_WPR4, WPR_Color4, WPR_Width4, WPR_Style4);
   InitBuffer(4, WPR_Buffer5, "Williams %R (" + IntegerToString(WPR_Period5) + ")", Enable_WPR5, WPR_Color5, WPR_Width5, WPR_Style5);
   InitBuffer(5, WPR_Buffer6, "Williams %R (" + IntegerToString(WPR_Period6) + ")", Enable_WPR6, WPR_Color6, WPR_Width6, WPR_Style6);
   InitBuffer(6, WPR_Buffer7, "Williams %R (" + IntegerToString(WPR_Period7) + ")", Enable_WPR7, WPR_Color7, WPR_Width7, WPR_Style7);
   InitBuffer(7, WPR_Buffer8, "Williams %R (" + IntegerToString(WPR_Period8) + ")", Enable_WPR8, WPR_Color8, WPR_Width8, WPR_Style8);
   InitBuffer(8, WPR_Buffer9, "Williams %R (" + IntegerToString(WPR_Period9) + ")", Enable_WPR9, WPR_Color9, WPR_Width9, WPR_Style9);
   InitBuffer(9, WPR_Buffer10, "Williams %R (" + IntegerToString(WPR_Period10) + ")", Enable_WPR10, WPR_Color10, WPR_Width10, WPR_Style10);
   
   // 设置水平线
   if(Show_Levels)
   {
      IndicatorSetInteger(INDICATOR_LEVELS, 2);
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, Overbought_Level);
      IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, Oversold_Level);
      IndicatorSetString(INDICATOR_LEVELTEXT, 0, "超买 (" + IntegerToString(Overbought_Level) + ")");
      IndicatorSetString(INDICATOR_LEVELTEXT, 1, "超卖 (" + IntegerToString(Oversold_Level) + ")");
      
      for(int i=0; i<2; i++)
      {
         IndicatorSetInteger(INDICATOR_LEVELCOLOR, i, Levels_Color);
         IndicatorSetInteger(INDICATOR_LEVELSTYLE, i, Levels_Style);
      }
   }
   
   // 检查是否至少有一个指标被启用
   if(activeIndicators == 0)
   {
      Print("错误: 至少需要启用一个威廉指标!");
      return(INIT_PARAMETERS_INCORRECT);
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 初始化指标缓冲区                                                 |
//+------------------------------------------------------------------+
void InitBuffer(int index, double &buffer[], string label, bool enabled, color clr, int width, ENUM_LINE_STYLE style)
{
   if(!enabled)
   {
      PlotIndexSetInteger(index, PLOT_DRAW_TYPE, DRAW_NONE);
      return;
   }
   
   activeIndicators++;
   SetIndexBuffer(index, buffer, INDICATOR_DATA);
   PlotIndexSetString(index, PLOT_LABEL, label);
   PlotIndexSetInteger(index, PLOT_DRAW_TYPE, DRAW_LINE);
   PlotIndexSetInteger(index, PLOT_LINE_COLOR, clr);
   PlotIndexSetInteger(index, PLOT_LINE_WIDTH, width);
   PlotIndexSetInteger(index, PLOT_LINE_STYLE, style);
}

//+------------------------------------------------------------------+
//| 自定义指标迭代函数                                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // 检查数据点是否足够
   if(rates_total < 1) return(0);
   
   // 计算起始位置
   int start;
   if(prev_calculated == 0)
      start = 0;
   else
      start = prev_calculated - 1;
   
   // 计算各个周期的威廉指标
   if(Enable_WPR1) CalculateWilliamsR(rates_total, start, WPR_Period1, WPR_Buffer1, high, low, close);
   if(Enable_WPR2) CalculateWilliamsR(rates_total, start, WPR_Period2, WPR_Buffer2, high, low, close);
   if(Enable_WPR3) CalculateWilliamsR(rates_total, start, WPR_Period3, WPR_Buffer3, high, low, close);
   if(Enable_WPR4) CalculateWilliamsR(rates_total, start, WPR_Period4, WPR_Buffer4, high, low, close);
   if(Enable_WPR5) CalculateWilliamsR(rates_total, start, WPR_Period5, WPR_Buffer5, high, low, close);
   if(Enable_WPR6) CalculateWilliamsR(rates_total, start, WPR_Period6, WPR_Buffer6, high, low, close);
   if(Enable_WPR7) CalculateWilliamsR(rates_total, start, WPR_Period7, WPR_Buffer7, high, low, close);
   if(Enable_WPR8) CalculateWilliamsR(rates_total, start, WPR_Period8, WPR_Buffer8, high, low, close);
   if(Enable_WPR9) CalculateWilliamsR(rates_total, start, WPR_Period9, WPR_Buffer9, high, low, close);
   if(Enable_WPR10) CalculateWilliamsR(rates_total, start, WPR_Period10, WPR_Buffer10, high, low, close);
   
   return(rates_total);
}

//+------------------------------------------------------------------+
//| 计算威廉指标                                                     |
//+------------------------------------------------------------------+
void CalculateWilliamsR(const int rates_total, const int start, const int period, 
                        double &buffer[], const double &high[], const double &low[], 
                        const double &close[])
{
   // 确保周期有效
   if(period <= 0)
   {
      Print("错误: 威廉指标周期必须大于0");
      return;
   }
   
   // 计算威廉指标
   for(int i=start; i<rates_total; i++)
   {
      // 确保有足够的数据点
      if(i < period) continue;
      
      // 查找周期内的最高价和最低价
      double highest = high[i];
      double lowest = low[i];
      
      for(int j=1; j<period; j++)
      {
         highest = MathMax(highest, high[i-j]);
         lowest = MathMin(lowest, low[i-j]);
      }
      
      // 计算威廉指标值
      if(highest != lowest)
         buffer[i] = -100.0 * (highest - close[i]) / (highest - lowest);
      else
         buffer[i] = -50.0; // 如果最高价等于最低价，返回中间值
   }
}
