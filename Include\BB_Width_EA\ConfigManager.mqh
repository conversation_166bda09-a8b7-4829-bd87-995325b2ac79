//+------------------------------------------------------------------+
//|                                              ConfigManager.mqh |
//|                                                                |
//+------------------------------------------------------------------+
#ifndef __CONFIG_MANAGER_MQH__
#define __CONFIG_MANAGER_MQH__

#include "Common.mqh"

// 配置结构体
struct BBWidthConfig {
   string symbol;           // 交易品种
   ENUM_TIMEFRAMES timeframe; // 时间周期
   double threshold;        // 宽度阈值
   int consecutive;         // 连续次数

   // 构造函数
   BBWidthConfig(string s="", ENUM_TIMEFRAMES tf=PERIOD_CURRENT, double th=0.0, int cons=0) {
      symbol = s;
      timeframe = tf;
      threshold = th;
      consecutive = cons;
   }

   // 获取品种的基本名称（去除后缀）
   string GetBaseSymbol(string s) {
      // 查找点号位置
      int dotPos = StringFind(s, ".");
      if(dotPos > 0) {
         // 返回点号前的部分
         return StringSubstr(s, 0, dotPos);
      }
      // 如果没有点号，返回原始名称
      return s;
   }

   // 检查配置是否匹配当前交易品种和时间周期
   bool Matches(string s, ENUM_TIMEFRAMES tf) {
      // 获取基本品种名称（去除后缀）
      string baseSymbol = GetBaseSymbol(s);
      string configBaseSymbol = GetBaseSymbol(symbol);

      // 比较基本名称和时间周期
      return (configBaseSymbol == baseSymbol && timeframe == tf);
   }

   // 转换为字符串
   string ToString() {
      return StringFormat("Symbol: %s, Timeframe: %s, Threshold: %.5f, Consecutive: %d",
                         symbol, EnumToString(timeframe), threshold, consecutive);
   }
};

// 配置管理类
class CConfigManager {
private:
   BBWidthConfig m_configs[];  // 配置数组
   int m_count;                // 配置数量

   // 从文件加载配置
   bool LoadFromFile() {
      string fileName = "BB_Width_Configs.csv";

      // 打印配置文件的完整路径
      string commonFilesPath = TerminalInfoString(TERMINAL_COMMONDATA_PATH) + "\\MQL5\\Files\\" + fileName;
      string terminalFilesPath = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL5\\Files\\" + fileName;

      Print("尝试从以下位置读取配置文件:");
      Print("1. 公共文件目录: ", commonFilesPath);
      Print("2. 终端文件目录: ", terminalFilesPath);

      int fileHandle = FileOpen(fileName, FILE_READ|FILE_CSV|FILE_COMMON, ',');

      if(fileHandle == INVALID_HANDLE) {
         Print("无法打开配置文件: ", fileName, ", 错误: ", GetLastError());
         return false;
      }

      Print("成功打开配置文件，开始读取配置...");


      // 跳过标题行
      if(!FileIsEnding(fileHandle)) {
         FileReadString(fileHandle);  // Symbol
         FileReadString(fileHandle);  // Timeframe
         FileReadString(fileHandle);  // Threshold
         FileReadString(fileHandle);  // Consecutive
      }

      // 读取配置
      while(!FileIsEnding(fileHandle)) {
         string symbol = FileReadString(fileHandle);
         string tfStr = FileReadString(fileHandle);
         double threshold = StringToDouble(FileReadString(fileHandle));
         int consecutive = (int)StringToInteger(FileReadString(fileHandle));

         ENUM_TIMEFRAMES tf = StringToTimeframe(tfStr);

         if(symbol != "" && tf != PERIOD_CURRENT) {
            AddConfig(symbol, tf, threshold, consecutive);
         }
      }

      FileClose(fileHandle);

      // 打印所有读取到的配置
      Print("成功读取配置文件，共加载 ", m_count, " 个配置:");
      for(int i=0; i<m_count; i++) {
         Print(StringFormat("配置 #%d: %s", i+1, m_configs[i].ToString()));
      }

      return true;
   }

   // 字符串转换为时间周期枚举
   ENUM_TIMEFRAMES StringToTimeframe(string tfStr) {
      if(tfStr == "M1") return PERIOD_M1;
      if(tfStr == "M5") return PERIOD_M5;
      if(tfStr == "M15") return PERIOD_M15;
      if(tfStr == "M30") return PERIOD_M30;
      if(tfStr == "H1") return PERIOD_H1;
      if(tfStr == "H4") return PERIOD_H4;
      if(tfStr == "D1") return PERIOD_D1;
      if(tfStr == "W1") return PERIOD_W1;
      if(tfStr == "MN1") return PERIOD_MN1;
      return PERIOD_CURRENT;
   }

public:
   // 构造函数
   CConfigManager() {
      m_count = 0;
      Initialize();
   }

   // 初始化
   void Initialize() {
      Print("===== BB_Width_EA 配置管理器初始化 =====");

      // 添加默认配置
      Print("添加默认配置...");
      AddDefaultConfigs();

      // 尝试从文件加载配置
      Print("尝试从文件加载配置...");
      bool fileLoaded = LoadFromFile();

      if(fileLoaded) {
         Print("配置文件加载成功");
      } else {
         Print("配置文件加载失败，将使用默认配置");
      }

      Print("配置管理器初始化完成，共加载 ", m_count, " 个配置");
      Print("==========================================");
   }

   // 添加默认配置
   void AddDefaultConfigs() {
      AddConfig("XAUUSD", PERIOD_M5, 6.5, 5);
      // 欧元美元 M15
      AddConfig("EURUSD", PERIOD_M15, 0.00050, 5);

      // 欧元美元 H1
      AddConfig("EURUSD", PERIOD_H1, 0.00080, 3);

      // 英镑美元 M15
      AddConfig("GBPUSD", PERIOD_M15, 0.00060, 5);

      // 美元日元 H1
      AddConfig("USDJPY", PERIOD_H1, 0.080, 3);
   }

   // 添加配置
   void AddConfig(string symbol, ENUM_TIMEFRAMES timeframe, double threshold, int consecutive) {
      m_count++;
      ArrayResize(m_configs, m_count);
      m_configs[m_count-1] = BBWidthConfig(symbol, timeframe, threshold, consecutive);
   }

   // 保存配置到文件
   bool SaveToFile() {
      string fileName = "BB_Width_Configs.csv";
      int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_CSV|FILE_COMMON, ',');

      if(fileHandle == INVALID_HANDLE) {
         Print("无法创建配置文件: ", fileName, ", 错误: ", GetLastError());
         return false;
      }

      // 写入标题
      FileWrite(fileHandle, "Symbol", "Timeframe", "Threshold", "Consecutive");

      // 写入配置
      for(int i=0; i<m_count; i++) {
         FileWrite(fileHandle,
                  m_configs[i].symbol,
                  EnumToString(m_configs[i].timeframe),
                  DoubleToString(m_configs[i].threshold, 5),
                  IntegerToString(m_configs[i].consecutive));
      }

      FileClose(fileHandle);
      return true;
   }

   // 查找匹配的配置
   bool FindConfig(string symbol, ENUM_TIMEFRAMES timeframe, BBWidthConfig &config) {
      for(int i=0; i<m_count; i++) {
         if(m_configs[i].Matches(symbol, timeframe)) {
            config = m_configs[i];
            return true;
         }
      }
      return false;
   }

   // 获取配置数量
   int GetCount() {
      return m_count;
   }

   // 获取指定索引的配置
   BBWidthConfig GetConfig(int index) {
      if(index >= 0 && index < m_count) {
         return m_configs[index];
      }
      return BBWidthConfig();
   }
};

// 全局配置管理器实例声明
extern CConfigManager g_configManager;

#endif
