//+------------------------------------------------------------------+
//|                                              SmartBoxFinder.mq5 |
//|                        Copyright 2023, <PERSON><PERSON><PERSON><PERSON><PERSON> & AI Assistant |
//+------------------------------------------------------------------+
#property copyright "RongQiang Ye & AI Assistant"
#property version   "3.10"
#property description "箱体检测系统"
#include <Arrays\ArrayObj.mqh>

//--- 输入参数
input int      BoxFormationBars   = 20;    // 箱体形成周期（K线数量）
input double   MaxPriceRangePct   = 2.5;   // 最大价格波动百分比
input int      MinClusterSize     = 8;     // 最小聚集K线数
input color    BoxColor           = clrLightGoldenrod;  // 箱体颜色
input int      Transparency       = 75;    // 透明度(0-100)
input int      MaxBoxes           = 30;    // 最大显示箱体数
input int      MergeThreshold     = 3;     // 合并阈值（单位：K线数）

//--- 布林带参数
input int      BBPeriod          = 20;    // 布林带周期
input double   BBDeviation       = 2.0;   // 布林带标准差
input double   BBWidthThreshold  = 0.05;  // 布林带宽度阈值（相对中轨的百分比）

//--- 信息面板参数
input int      InfoPanelX        = 20;    // 信息面板X坐标
input int      InfoPanelY        = 20;    // 信息面板Y坐标
input int      InfoPanelWidth    = 400;   // 信息面板宽度
input int      InfoPanelHeight   = 500;   // 信息面板高度
input color    InfoPanelColor    = clrBlack;  // 信息面板背景色
input color    InfoTextColor     = clrWhite;  // 信息文本颜色

//--- 信息面板文本颜色
input color    InfoDiscoveryColor = clrDarkGreen;   // 箱体发现信息颜色
input color    InfoUpdateColor   = clrDarkBlue;     // 箱体更新信息颜色
input color    InfoEndColor      = clrFireBrick;    // 箱体结束信息颜色
input color    InfoSystemColor   = clrDarkSlateGray; // 系统信息颜色
input bool     ShowStatistics    = true;            // 显示统计信息

//+------------------------------------------------------------------+
//| 智能箱体对象类                     |
//+------------------------------------------------------------------+
class SmartBox : public CObject
{
public:
   datetime  startTime;
   datetime  endTime;
   double    top;
   double    bottom;
   string    name;
   bool      isActive;
   int       duration;

   SmartBox(datetime st, datetime et, double t, double b) :
      startTime(st), endTime(et), top(t), bottom(b), isActive(true), duration(1)
   {
      name = "SmartBox_"+IntegerToString(st)+"_"+IntegerToString(chartID);
      CreateVisual();
      // 美化箱体发现信息显示 - 移除箱体名称，增加更多有用属性
      AddInfoText(StringFormat("[箱体发现]\n  ▶ 价格区间: %.*f - %.*f\n  ▶ 高度: %.*f (%.*f点)\n  ▶ 时间: %s\n  ▶ 周期: %s", 
                 _Digits, top, _Digits, bottom, _Digits, top-bottom, _Digits, MathRound((top-bottom)/Point()), 
                 TimeToString(startTime, TIME_DATE|TIME_MINUTES), EnumToString((ENUM_TIMEFRAMES)_Period)), InfoDiscoveryColor);
   }

   ~SmartBox()
   {
      // 美化箱体结束信息显示 - 移除箱体名称，增加更多有用属性
      double height = top-bottom;
      double priceRange = height/Point();
      double timeRange = (double)(endTime - startTime) / PeriodSeconds(_Period);
      
      AddInfoText(StringFormat("[箱体结束]\n  ▶ 最终区间: %.*f - %.*f\n  ▶ 持续: %d根K线 (%.1f小时)\n  ▶ 高度: %.*f (%.*f点)\n  ▶ 时间范围: %s - %s", 
                 _Digits, top, _Digits, bottom, duration, timeRange/60.0, 
                 _Digits, height, 0, priceRange,
                 TimeToString(startTime, TIME_DATE|TIME_MINUTES), 
                 TimeToString(endTime, TIME_DATE|TIME_MINUTES)), InfoEndColor);
      ObjectDelete(0, name);
   }

   void Update(datetime newTime, double newHigh, double newLow)
   {
      endTime = newTime;
      top = fmax(top, newHigh);
      bottom = fmin(bottom, newLow);
      duration++;
      Redraw();
      // 美化箱体更新信息显示 - 移除箱体名称，增加更多有用属性
      double height = top-bottom;
      double priceRange = height/Point();
      double timeRange = (double)(endTime - startTime) / PeriodSeconds(_Period);
      
      AddInfoText(StringFormat("[箱体更新]\n  ▶ 区间: %.*f - %.*f\n  ▶ 持续: %d根K线 (%.1f小时)\n  ▶ 高度: %.*f (%.*f点)\n  ▶ 波动率: %.2f%%", 
                 _Digits, top, _Digits, bottom, duration, timeRange/60.0, 
                 _Digits, height, 0, priceRange,
                 height/((top+bottom)/2)*100), InfoUpdateColor);
   }

   bool CanMerge(const SmartBox* other) const
   {
      if(other == NULL) return false;
      
      // 调试输出合并参数
      Print(StringFormat("尝试合并箱体 %s 与 %s", name, other.name));
      // Print(StringFormat("当前箱体时间范围: %s - %s 候选箱体时间: %s", 
      //       TimeToString(startTime), TimeToString(endTime), TimeToString(other.startTime)));
      
      // 时间差计算（单位：秒）
      int timeDiffSeconds = int(other.startTime - endTime);
      int thresholdSeconds = MergeThreshold * PeriodSeconds(_Period);
      // Print(StringFormat("时间差: %d秒 合并阈值: %d秒", timeDiffSeconds, thresholdSeconds));
      
      if(timeDiffSeconds > thresholdSeconds)
      {
         Print("时间连续性不满足，跳过合并");
         return false;
      }
      
      // 价格重叠度计算
      double overlapTop = fmin(top, other.top);
      double overlapBottom = fmax(bottom, other.bottom);
      double overlapRange = overlapTop - overlapBottom;
      
      // Print(StringFormat("当前箱体范围: %.*f - %.*f 候选箱体范围: %.*f - %.*f", 
      //       _Digits, top, _Digits, bottom, _Digits, other.top, _Digits, other.bottom));
      // Print(StringFormat("重叠区间: %.*f - %.*f 重叠范围: %.*f", 
      //       _Digits, overlapBottom, _Digits, overlapTop, _Digits, overlapRange));
      
      double thisRange = top - bottom;
      double otherRange = other.top - other.bottom;
      double overlapRatio = overlapRange / fmin(thisRange, otherRange);
      
      // Print(StringFormat("重叠比例: %.2f 要求>=0.6", overlapRatio));
      
      // 扩张比例计算
      double mergedTop = fmax(top, other.top);
      double mergedBottom = fmin(bottom, other.bottom);
      double expansionRatio = (mergedTop - mergedBottom) / fmax(thisRange, otherRange);
      
      // Print(StringFormat("扩张比例: %.2f 要求<=2.0", expansionRatio));
      
      bool canMerge = (overlapRatio >= 0.6 && expansionRatio <= 2.0);
      // Print(StringFormat("合并结果: %s", canMerge ? "通过" : "不通过"));
      
      return canMerge;
   }

private:
   void CreateVisual()
   {
      if(ObjectFind(0, name) < 0)
      {
         ObjectCreate(0, name, OBJ_RECTANGLE, 0, startTime, top, endTime, bottom);
         ObjectSetInteger(0, name, OBJPROP_COLOR, BoxColor);
         ObjectSetInteger(0, name, OBJPROP_BACK, true);
         ObjectSetInteger(0, name, OBJPROP_FILL, false);
         ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
         ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
      }
   }

   void Redraw()
   {
      if(ObjectFind(0, name) >= 0)
      {
         ObjectSetInteger(0, name, OBJPROP_TIME, 0, startTime);
         ObjectSetInteger(0, name, OBJPROP_TIME, 1, endTime);
         ObjectSetDouble(0, name, OBJPROP_PRICE, 0, top);
         ObjectSetDouble(0, name, OBJPROP_PRICE, 1, bottom);
      }
   }
};

CArrayObj ActiveBoxes;
datetime lastBarTime;
int bbHandle;  // 布林带指标句柄
double bbUpper[], bbMiddle[], bbLower[];  // 布林带数据缓冲区

//--- 信息面板相关变量
long chartID = ChartID();  // 当前图表ID
string infoPanelName = "InfoPanel_";    // 信息面板对象名称
string infoBuffer = "";  // 信息缓冲区
int totalBoxesCreated = 0;    // 历史箱体总数计数器
int mergeCounter = 0;         // 合并事件计数器

//+------------------------------------------------------------------+
//| 箱体检测核心逻辑（增加越界保护）                                |
//+------------------------------------------------------------------+
bool DetectBoxFormation(SmartBox* &newBox)
{
   int totalBars = Bars(_Symbol, _Period);
   int startPos = BoxFormationBars;
   
   // 增加越界保护
   if(startPos >= totalBars || startPos <= 0) return false;
   
   // 获取布林带数据
   if(CopyBuffer(bbHandle, 1, 0, startPos+1, bbUpper) <= 0 ||
      CopyBuffer(bbHandle, 0, 0, startPos+1, bbMiddle) <= 0 ||
      CopyBuffer(bbHandle, 2, 0, startPos+1, bbLower) <= 0)
   {
      Print("获取布林带数据失败！");
      return false;
   }

   double maxHigh = iHigh(_Symbol, _Period, startPos);
   double minHigh = maxHigh;
   double maxLow = iLow(_Symbol, _Period, startPos);
   double minLow = maxLow;
   double avgPrice = (maxHigh + minLow) / 2;
   double initialRange = maxHigh - minLow;
   
   // 动态调整最大波动范围（增加平滑处理）
   double dynamicMaxRange = (initialRange + iATR(_Symbol, _Period, 14)) * 0.5 * (1 + MaxPriceRangePct / 100);
   
   int clusterCount = 0;
   int consecutiveCount = 0;
   datetime boxStart = iTime(_Symbol, _Period, startPos);
   
   for(int i=startPos; i>0; i--)
   {
      if(i >= totalBars) continue;

      double currentHigh = iHigh(_Symbol, _Period, i);
      double currentLow = iLow(_Symbol, _Period, i);
      
      // 调试输出布林带数据
      // Print(StringFormat("Bar[%d] 布林带宽度: %.2f 阈值: %.2f", i, (bbUpper[i]-bbLower[i])/bbMiddle[i], BBWidthThreshold));

      // 更新价格极值
      maxHigh = fmax(maxHigh, currentHigh);
      minHigh = fmin(minHigh, currentHigh);
      maxLow = fmax(maxLow, currentLow);
      minLow = fmin(minLow, currentLow);
      
      // 调试输出连续计数状态
      // Print(StringFormat("当前连续计数: %d 总聚集数: %d", consecutiveCount, clusterCount));

      // 计算布林带宽度
      double bbWidth = (bbUpper[i] - bbLower[i]) / bbMiddle[i];
      
      // 使用动态价格区间来判断波动
      double priceRange = maxHigh - minLow;
      double rangeExpansion = priceRange - initialRange;
      double volatilityPct = rangeExpansion / initialRange * 100;
      
      // 调试输出关键判断参数
      // Print(StringFormat("Bar[%d] 价格区间: %.*f 波动率: %.1f%% 布林带宽比: %.2f", 
      //       i, _Digits, priceRange, volatilityPct, bbWidth));
      
      // 修改后的波动率判断条件（增加容忍度）
      if(priceRange <= dynamicMaxRange && volatilityPct <= MaxPriceRangePct * 1.5 && bbWidth <= BBWidthThreshold)
      {
         clusterCount++;
         consecutiveCount++;
         
         // 新增连续计数保护机制
         if(consecutiveCount >= 1 && consecutiveCount % 2 == 0) 
         {
            clusterCount = fmax(clusterCount, consecutiveCount);
         }
      
         // 调整后的箱体形成条件
         if(consecutiveCount >= MinClusterSize/2 && clusterCount >= BoxFormationBars/3)
         {
            datetime endTime = iTime(_Symbol, _Period, i);
            newBox = new SmartBox(boxStart, endTime, maxHigh, minLow);
            return true;
         }
      }
      
      // 优化后的重置条件（增加缓冲区）
      if(priceRange > dynamicMaxRange * 1.3 || volatilityPct > MaxPriceRangePct * 2.0)
      {
         clusterCount = 0;
         consecutiveCount = 0;
         // Print("重置所有计数（剧烈波动）");
         boxStart = iTime(_Symbol, _Period, i);
         maxHigh = currentHigh;
         minHigh = currentHigh;
         maxLow = currentLow;
         minLow = currentLow;
      }
      else if(priceRange <= dynamicMaxRange * 0.8 && volatilityPct <= MaxPriceRangePct)
      {
         // 允许连续计数继续累积
         // Print("保持连续计数（稳定波动）");
      }
   }
   return false;  // 如果没有找到合适的箱体形态，返回false
}


//+------------------------------------------------------------------+
//| 箱体管理系统（修复合并逻辑）                                    |
//+------------------------------------------------------------------+
void ManageBoxes()
{
   SmartBox* newBox = NULL;
   if(DetectBoxFormation(newBox) && newBox != NULL)
   {
      // 尝试合并已有箱体
      bool merged = false;
      for(int i=ActiveBoxes.Total()-1; i>=0; i--)
      {
         SmartBox* existing = dynamic_cast<SmartBox*>(ActiveBoxes.At(i));
         if(existing != NULL && existing.CanMerge(newBox))
         {
             // 修正合并逻辑：使用指针正确访问成员
             existing.Update(newBox.endTime, newBox.top, newBox.bottom);
             merged = true;
             delete newBox;
             break;
         }
      }

      // 添加新箱体
      if(!merged)
      {
         ActiveBoxes.Add(newBox);
         CleanOldBoxes();
      }
   }
}

//+------------------------------------------------------------------+
//| 清理旧箱体（增强稳定性）                                        |
//+------------------------------------------------------------------+
void CleanOldBoxes()
{
   while(ActiveBoxes.Total() > MaxBoxes)
   {
      SmartBox* oldest = dynamic_cast<SmartBox*>(ActiveBoxes.At(0));
      if(oldest != NULL)
      {
         delete oldest;
      }
      ActiveBoxes.Delete(0);
   }
}

//+------------------------------------------------------------------+
//| EA主循环（增加新K线检测）                                       |
//+------------------------------------------------------------------+
void OnTick()
{
   datetime currentTime[1];
   if(CopyTime(_Symbol, _Period, 0, 1, currentTime) != 1) return;

   if(currentTime[0] != lastBarTime)
   {
      lastBarTime = currentTime[0];
      ManageBoxes();
      
      // 调试输出并更新统计信息
      Print("当前活跃箱体数量：", ActiveBoxes.Total());
      if(ShowStatistics) UpdateStatistics();
   }
}

//+------------------------------------------------------------------+
//| 初始化函数                                                      |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| 添加信息到面板                                                  |
//+------------------------------------------------------------------+
datetime lastUpdateTime = 0;  // 上次更新时间
int updateInterval = 200;     // 更新间隔（毫秒）
string statisticsText = "";  // 统计信息文本

//+------------------------------------------------------------------+
//| 更新统计信息                                                     |
//+------------------------------------------------------------------+
void UpdateStatistics()
{
   if(!ShowStatistics) return;
   
   // 清空之前的统计信息
   statisticsText = "";
   
   // 获取统计数据
   int activeBoxCount = ActiveBoxes.Total();
   int totalBoxCount = totalBoxesCreated;
   double avgHeight = 0.0;
   double maxHeight = 0.0;
   double minHeight = DBL_MAX;
   double totalDuration = 0.0;
   double totalPoints = 0.0;
   
   if(activeBoxCount > 0)
   {
      for(int i=0; i<activeBoxCount; i++)
      {
         SmartBox* box = dynamic_cast<SmartBox*>(ActiveBoxes.At(i));
         if(box != NULL)
         {
            double height = box.top - box.bottom;
            avgHeight += height;
            maxHeight = fmax(maxHeight, height);
            minHeight = fmin(minHeight, height);
            totalDuration += box.duration;
            totalPoints += height/Point();
         }
      }
      avgHeight /= activeBoxCount;
      double avgDuration = totalDuration / activeBoxCount;
      double avgPoints = totalPoints / activeBoxCount;
      
      // 构建统计信息文本
      statisticsText += StringFormat("活跃箱体数量：%d\n", activeBoxCount);
      statisticsText += StringFormat("平均高度：%.*f\n", _Digits, avgHeight);
      statisticsText += StringFormat("平均持续：%.1f根K线\n", avgDuration);
      statisticsText += StringFormat("最大高度：%.*f\n", _Digits, maxHeight);
      statisticsText += StringFormat("最小高度：%.*f\n", _Digits, minHeight == DBL_MAX ? 0 : minHeight);
      statisticsText += StringFormat("平均点数：%.1f\n", avgPoints);
   }
   else
   {
      // 无活跃箱体时的统计信息
      statisticsText += "活跃箱体数量：0\n";
      statisticsText += "平均高度：N/A\n";
      statisticsText += "平均持续：N/A\n";
      statisticsText += "最大高度：N/A\n";
      statisticsText += "最小高度：N/A\n";
      statisticsText += "平均点数：N/A\n";
   }
   
   // 添加历史统计信息
   statisticsText += StringFormat("历史箱体总数：%d\n", totalBoxCount);
   statisticsText += StringFormat("合并事件次数：%d\n", mergeCounter);
   
   // 计算成功率（如果有历史数据）
   if(totalBoxCount > 0)
   {
      double successRate = (double)activeBoxCount / totalBoxCount * 100.0;
      statisticsText += StringFormat("成功率：%.1f%%\n", successRate);
   }
   else
   {
      statisticsText += "成功率：N/A\n";
   }
   
   // 更新标签（保留此功能以兼容其他部分）
   UpdateInfoLabel("ActiveBoxes", StringFormat("活跃箱体数量：%d", activeBoxCount));
}

void UpdateInfoLabel(string labelName, string text)
{
   string fullName = labelName + "_" + IntegerToString(ChartID());
   if(ObjectFind(0, fullName) >= 0)
   {
      ObjectSetString(0, fullName, OBJPROP_TEXT, text);
      ObjectSetInteger(0, fullName, OBJPROP_COLOR, clrLightCyan);
   }
}

// 函数声明
void AddInfoText(string text, color textColor = clrNONE);
void CreateInfoPanel();
void CreatePanelBackground();
void CreateInfoLabels();
void UpdateInfoPanel();
void UpdateInfoLabel(string labelName, string text);
void UpdateStatistics();

//+------------------------------------------------------------------+
//| 添加信息到面板                                                  |
//+------------------------------------------------------------------+
void AddInfoText(string text, color textColor = clrNONE)
{
   // 如果未指定颜色，使用默认颜色
   if(textColor == clrNONE) textColor = InfoTextColor;
   
   // 添加时间戳 - 使用统一格式
   string timeStr = TimeToString(TimeCurrent(), TIME_MINUTES|TIME_SECONDS);
   string formattedText = StringFormat("[%s] %s", timeStr, text);
   
   // 更新缓冲区 - 使用数组管理消息
   static string messageArray[];
   static int maxMessages = 100;
   static int displayMessages = 15; // 控制显示的消息数量
   
   // 添加新消息到数组
   int size = ArraySize(messageArray);
   if(size >= maxMessages)
   {
      // 移除最旧的消息
      for(int i=0; i<size-1; i++)
         messageArray[i] = messageArray[i+1];
      messageArray[size-1] = formattedText;
   }
   else
   {
      ArrayResize(messageArray, size + 1);
      messageArray[size] = formattedText;
   }
   
   // 重建信息缓冲区 - 优化格式
   infoBuffer = "\n"; // 顶部留白
   
   // 只显示最新的几条消息，并添加分隔符
   int startIdx = MathMax(0, ArraySize(messageArray) - displayMessages);
   for(int i = startIdx; i < ArraySize(messageArray); i++) 
   {
      // 为不同类型的消息添加不同的前缀和格式
      string msg = messageArray[i];
      
      // 根据消息类型添加不同的缩进和格式
      if(StringFind(msg, "[箱体发现]") >= 0)
         infoBuffer += "  " + msg + "\n\n";
      else if(StringFind(msg, "[箱体更新]") >= 0)
         infoBuffer += "  " + msg + "\n\n";
      else if(StringFind(msg, "[箱体结束]") >= 0)
         infoBuffer += "  " + msg + "\n\n";
      else
         infoBuffer += "  " + msg + "\n\n";
   }
   
   // 添加分隔线
   if(ShowStatistics)
      infoBuffer += "\n--------------------------------------------------\n\n";
   
   // 更新统计信息
   if(ShowStatistics) UpdateStatistics();
   
   // 立即更新面板显示
   UpdateInfoPanel();
   
   // 保持控制台输出同步
   Print(formattedText);
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                    |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   if(ObjectFind(0, infoPanelName) >= 0)
   {
      // 格式化统计信息
      string statsDisplay = "";
      if(ShowStatistics) 
      {
         // 创建格式化的统计信息区域
         statsDisplay = "  【箱体统计信息】\n";
         statsDisplay += "  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n";
         
         // 添加统计信息，使用表格式布局
         string statLabels[];
         string statValues[];
         int statCount = 0;
         
         // 从统计文本中提取标签和值
         string lines[];
         StringSplit(statisticsText, '\n', lines);
         
         for(int i=0; i<ArraySize(lines); i++)
         {
            if(StringLen(lines[i]) > 0)
            {
               string parts[];
               StringSplit(lines[i], '：', parts);
               if(ArraySize(parts) == 2)
               {
                  ArrayResize(statLabels, statCount+1);
                  ArrayResize(statValues, statCount+1);
                  statLabels[statCount] = parts[0];
                  statValues[statCount] = parts[1];
                  statCount++;
               }
            }
         }
         
         // 创建两列布局
         for(int i=0; i<statCount; i+=2)
         {
            string line = StringFormat("  %-20s %-10s", statLabels[i]+"：", statValues[i]);
            if(i+1 < statCount)
               line += StringFormat("    %-20s %-10s", statLabels[i+1]+"：", statValues[i+1]);
            statsDisplay += line + "\n";
         }
      }
      
      // 设置文本并强制更新
      string displayText = infoBuffer + statsDisplay;
      
      // 直接更新文本内容
      ObjectSetString(0, infoPanelName, OBJPROP_TEXT, displayText);
      
      // 强制重绘
      ChartRedraw(0);
      
      // 更新时间戳
      lastUpdateTime = GetTickCount();
   }
}

// 辅助函数：获取颜色的红色分量
uchar GetRValue(color clr)
{
   return (uchar)clr;
}

// 辅助函数：获取颜色的绿色分量
uchar GetGValue(color clr)
{
   return (uchar)(clr >> 8);
}

// 辅助函数：获取颜色的蓝色分量
uchar GetBValue(color clr)
{
   return (uchar)(clr >> 16);
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                    |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // 创建双层背景结构
   CreatePanelBackground();
   CreateInfoLabels();
}

//+------------------------------------------------------------------+
//| 创建信息面板背景                                                |
//+------------------------------------------------------------------+
void CreatePanelBackground()
{
   string bgName = "InfoPanelBG_" + IntegerToString((int)chartID);
   string sepName = "Separator_" + IntegerToString((int)chartID);
   infoPanelName = infoPanelName + IntegerToString((int)chartID);
   
   // 主背景层
   if(ObjectFind(0, bgName) < 0)
   {
      ObjectCreate(0, bgName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
      ObjectSetInteger(0, bgName, OBJPROP_XDISTANCE, InfoPanelX);
      ObjectSetInteger(0, bgName, OBJPROP_YDISTANCE, InfoPanelY);
      ObjectSetInteger(0, bgName, OBJPROP_XSIZE, InfoPanelWidth);
      ObjectSetInteger(0, bgName, OBJPROP_YSIZE, InfoPanelHeight);
      // 使用更高透明度的深色背景
      ObjectSetInteger(0, bgName, OBJPROP_BGCOLOR, clrBlack);
      ObjectSetInteger(0, bgName, OBJPROP_BACK, false);
      ObjectSetInteger(0, bgName, OBJPROP_ZORDER, 0);
      ObjectSetInteger(0, bgName, OBJPROP_BORDER_COLOR, clrDimGray);
      ObjectSetInteger(0, bgName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   }
   
   // 标题栏
   string titleName = "PanelTitle_" + IntegerToString(chartID);
   ObjectCreate(0, titleName, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, titleName, OBJPROP_XDISTANCE, InfoPanelX);
   ObjectSetInteger(0, titleName, OBJPROP_YDISTANCE, InfoPanelY-15);
   ObjectSetString(0, titleName, OBJPROP_TEXT, "智能箱体监控系统 v3.10");
   ObjectSetInteger(0, titleName, OBJPROP_COLOR, clrGoldenrod);
   ObjectSetInteger(0, titleName, OBJPROP_FONTSIZE, 10);
}

void CreateInfoLabels()
{
   // 列式布局参数 - 三列布局
   int column1X = InfoPanelX + 10;
   int column2X = InfoPanelX + 140;
   int column3X = InfoPanelX + 270;
   int startY = InfoPanelY + 15;
   int lineHeight = 20;
   
   // 箱体状态列
   CreateInfoLabel("BoxStatusTitle", column1X, startY, "当前箱体状态", clrGoldenrod);
   CreateInfoLabel("ActiveBoxes", column1X, startY+lineHeight, "活跃箱体数量：0", clrLightCyan);
   CreateInfoLabel("AvgHeight", column1X, startY+lineHeight*2, "平均高度：N/A", clrLightCyan);
   CreateInfoLabel("AvgDuration", column1X, startY+lineHeight*3, "平均持续时间：N/A", clrLightCyan);
   
   // 价格信息列
   CreateInfoLabel("PriceTitle", column2X, startY, "价格数据", clrGoldenrod);
   CreateInfoLabel("MaxHeight", column2X, startY+lineHeight, "最大高度：N/A", clrLightCyan);
   CreateInfoLabel("MinHeight", column2X, startY+lineHeight*2, "最小高度：N/A", clrLightCyan);
   CreateInfoLabel("AvgPoints", column2X, startY+lineHeight*3, "平均点数：N/A", clrLightCyan);
   
   // 统计信息列
   CreateInfoLabel("StatsTitle", column3X, startY, "统计数据", clrGoldenrod);
   CreateInfoLabel("TotalBoxes", column3X, startY+lineHeight, "历史箱体总数：0", clrLightCyan);
   CreateInfoLabel("MergeCount", column3X, startY+lineHeight*2, "合并事件次数：0", clrLightCyan);
   CreateInfoLabel("SuccessRate", column3X, startY+lineHeight*3, "成功率：N/A", clrLightCyan);
   
   // 分隔线
   string sepName = "Separator_" + IntegerToString(chartID);
   ObjectCreate(0, sepName, OBJ_TREND, 0, 0,0,0,0);
   ObjectSetInteger(0, sepName, OBJPROP_TIME, 0, TimeCurrent());
   ObjectSetDouble(0, sepName, OBJPROP_PRICE, 0, InfoPanelY+70);
   ObjectSetInteger(0, sepName, OBJPROP_TIME, 1, TimeCurrent()+PeriodSeconds(PERIOD_D1));
   ObjectSetDouble(0, sepName, OBJPROP_PRICE, 1, InfoPanelY+70);
   ObjectSetInteger(0, sepName, OBJPROP_RAY, false);
   ObjectSetInteger(0, sepName, OBJPROP_COLOR, clrDimGray);
   
   // 创建信息文本框 - 改进布局和可读性
   string panelName = "InfoPanel_" + IntegerToString(chartID);
   if(ObjectFind(0, panelName) < 0)
   {
      ObjectCreate(0, panelName, OBJ_EDIT, 0, 0, 0);
      ObjectSetInteger(0, panelName, OBJPROP_XDISTANCE, InfoPanelX + 5);
      ObjectSetInteger(0, panelName, OBJPROP_YDISTANCE, InfoPanelY + 95); // 下移位置，为状态栏留出空间
      ObjectSetInteger(0, panelName, OBJPROP_XSIZE, InfoPanelWidth - 10);
      ObjectSetInteger(0, panelName, OBJPROP_YSIZE, InfoPanelHeight - 100); // 调整高度
      ObjectSetInteger(0, panelName, OBJPROP_COLOR, InfoTextColor);
      // 使用半透明背景，提高可读性
      ObjectSetInteger(0, panelName, OBJPROP_BGCOLOR, clrBlack);
      ObjectSetInteger(0, panelName, OBJPROP_BORDER_COLOR, clrGray);
      ObjectSetInteger(0, panelName, OBJPROP_READONLY, true);
      ObjectSetInteger(0, panelName, OBJPROP_ALIGN, ALIGN_LEFT);
      ObjectSetInteger(0, panelName, OBJPROP_FONTSIZE, 9); // 增大字体
      ObjectSetString(0, panelName, OBJPROP_FONT, "Microsoft YaHei");
      ObjectSetInteger(0, panelName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, panelName, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
      ObjectSetString(0, panelName, OBJPROP_TOOLTIP, "箱体检测系统信息面板");
      ObjectSetInteger(0, panelName, OBJPROP_BACK, false);
      ObjectSetInteger(0, panelName, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, panelName, OBJPROP_SELECTED, false);
      ObjectSetInteger(0, panelName, OBJPROP_ZORDER, 1);
   }
}

//+------------------------------------------------------------------+
//| 创建信息标签                                                    |
//+------------------------------------------------------------------+
void CreateInfoLabel(string name, int x, int y, string text, color clr)
{
   string fullName = name + "_" + IntegerToString(ChartID());
   ObjectCreate(0, fullName, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, fullName, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, fullName, OBJPROP_YDISTANCE, y);
   ObjectSetString(0, fullName, OBJPROP_TEXT, text);
   ObjectSetInteger(0, fullName, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, fullName, OBJPROP_FONTSIZE, 8);
   ObjectSetString(0, fullName, OBJPROP_FONT, "Consolas");
   ObjectSetInteger(0, fullName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, fullName, OBJPROP_BACK, false);
   ObjectSetInteger(0, fullName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, fullName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, fullName, OBJPROP_HIDDEN, true);
   ObjectSetInteger(0, fullName, OBJPROP_ZORDER, 0);
}

int OnInit()
{
   ObjectsDeleteAll(0, "SmartBox_");
   ObjectsDeleteAll(0, "InfoPanel_");
   ActiveBoxes.Clear();
   infoBuffer = "";
   
   // 创建信息面板
   CreateInfoPanel();
   AddInfoText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", InfoSystemColor);
   AddInfoText("智能箱体检测系统 v3.10 初始化...", InfoSystemColor);
   AddInfoText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", InfoSystemColor);
   
   // 初始化布林带指标
   bbHandle = iBands(_Symbol, _Period, BBPeriod, 0, BBDeviation, PRICE_CLOSE);
   if(bbHandle == INVALID_HANDLE)
   {
      AddInfoText("布林带指标初始化失败！", clrRed);
      return(INIT_FAILED);
   }
   
   ArraySetAsSeries(bbUpper, true);
   ArraySetAsSeries(bbMiddle, true);
   ArraySetAsSeries(bbLower, true);
   
   AddInfoText("系统初始化完成，开始运行...", InfoSystemColor);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 逆初始化函数                                                    |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 添加终止信息
   AddInfoText("系统正在关闭，清理资源...", InfoSystemColor);
   
   // 清理所有箱体对象
   for(int i=ActiveBoxes.Total()-1; i>=0; i--)
   {
      SmartBox* box = dynamic_cast<SmartBox*>(ActiveBoxes.At(i));
      if(box != NULL) delete box;
   }
   ActiveBoxes.Clear();
   
   // 清理信息面板及相关对象
   string bgName = "InfoPanelBG_" + IntegerToString(ChartID());
   
   // 删除面板背景
   if(ObjectFind(0, bgName) >= 0)
   {
      ObjectDelete(0, bgName);
   }
   
   // 删除信息文本框
   if(ObjectFind(0, infoPanelName) >= 0)
   {
      ObjectDelete(0, infoPanelName);
   }
   
   // 清理缓冲区并重绘
   infoBuffer = "";
   ChartRedraw(0); // 强制重绘图表以确保所有对象被清除
}