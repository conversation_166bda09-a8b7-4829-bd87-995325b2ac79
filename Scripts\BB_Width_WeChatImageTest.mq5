//+------------------------------------------------------------------+
//|                                      BB_Width_WeChatImageTest.mq5 |
//|                                         https://www.deepseek.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, DeepSeek"
#property link      "https://www.deepseek.com"
#property version   "1.00"
#property script_show_inputs

#include <WeChatRobotManager.mqh>

// 输入参数
input string WebhookURL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c48b29b1-0dbb-41e7-be9b-d1d123f5f110"; // 企业微信机器人Webhook地址
input string TestImageName = "test_screenshot.png"; // 测试截图文件名

//+------------------------------------------------------------------+
//| 脚本程序起始函数                                                  |
//+------------------------------------------------------------------+
void OnStart()
{
   // 初始化微信机器人
   CWeChatRobotManager wechatManager;
   if(!wechatManager.Initialize(WebhookURL))
   {
      MessageBox("微信机器人初始化失败，请检查Webhook地址", "错误", MB_ICONERROR);
      return;
   }

   // 获取当前CSV文件名作为目录名（不带后缀）
   MqlDateTime timeStruct;
   TimeToStruct(TimeCurrent(), timeStruct);
   int daysToMonday = (timeStruct.day_of_week == 0) ? 6 : (timeStruct.day_of_week - 1);
   datetime mondayTime = TimeCurrent() - daysToMonday * 24 * 60 * 60;
   datetime sundayTime = mondayTime + 6 * 24 * 60 * 60;
   string weekRange = TimeToString(mondayTime, TIME_DATE) + "_" + TimeToString(sundayTime, TIME_DATE);
   int currentWeekNumber = (int)(mondayTime / (7 * 24 * 60 * 60));
   string weekNumber = IntegerToString(currentWeekNumber);
   string dirName = "BB_Width_Data_" + Symbol() + "_" + EnumToString(_Period) + "_Week" + weekNumber + "_"+ weekRange;

   // 确保目录存在
   if(!FileIsExist(dirName, FILE_COMMON))
   {
      if(!FolderCreate(dirName, FILE_COMMON))
      {
         MessageBox("创建截图目录失败: " + IntegerToString(GetLastError()), "错误", MB_ICONERROR);
         return;
      }
      Print("已创建截图目录: ", dirName);
   }

   // 生成测试截图
   string filePath = dirName + "\\" + TestImageName;

   // 调整图表显示
   ChartSetInteger(0, CHART_SHOW_DATE_SCALE, true);
   ChartSetInteger(0, CHART_SHOW_PRICE_SCALE, true);
   ChartNavigate(0, CHART_END, -30);

   // 截图
   if(!ChartScreenShot(0, filePath, 1920, 1080, ALIGN_RIGHT))
   {
      int error = GetLastError();
      MessageBox("截图失败! 错误码: " + IntegerToString(error), "错误", MB_ICONERROR);
      return;
   }

   Print("截图已生成: ", filePath);

   // 发送文本消息
   string message = StringFormat("BB_Width_EA 微信图片发送测试\n时间: %s\n品种: %s\n周期: %s",
                                TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES | TIME_SECONDS),
                                Symbol(),
                                EnumToString(_Period));

   if(!wechatManager.SendMessage(message))
   {
      MessageBox("发送文本消息失败", "错误", MB_ICONERROR);
      return;
   }

   // 发送图片消息
   if(!wechatManager.SendImage(filePath))
   {
      MessageBox("发送图片消息失败", "错误", MB_ICONERROR);
      return;
   }

   MessageBox("测试成功! 文本和图片消息已发送", "成功", MB_ICONINFORMATION);
}
//+------------------------------------------------------------------+
