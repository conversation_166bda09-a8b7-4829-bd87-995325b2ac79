#include <Trade/Trade.mqh>
CTrade trade;

#define BTN_ST "暂停"
#define BTN_RN "运行"
#define BTN_P "+"
#define BTN_LOT "手数"
#define BTN_M "-"
#define BTN_BUY "买入"
#define BTN_SELL "卖出"
#define BTN_CLOSE "平仓"

input group "基本配置"
input int MagicNumber = 1234; // EA唯一标识
input int fontSize = 8;       // 面板默认字体大小

bool tradeInAction = false; // 暂停盘面运行
int speed = 100000;         // 暂停速度

int OnInit(){
    int y = 30;
    int yH = 40;
    int split = 3; 
    createBtn(BTN_ST,110,y,110,30,BTN_ST,fontSize,clrWhite,clrIndigo, clrWhite);
    createBtn(BTN_RN,220,y,110,30,BTN_RN,fontSize,clrWhite,clrIndigo, clrWhite);

    y += yH;
    createBtn(BTN_P,110,y,80,30,BTN_P,fontSize,clrWhite,clrBlack, clrWhite);
    createBtn(BTN_LOT,190,y,60,30,"0.01",fontSize,clrWhite,clrBlack, clrWhite);
    createBtn(BTN_M,250,y,80,30,BTN_M,fontSize,clrWhite,clrBlack, clrWhite);

    y += yH;
    createBtn(BTN_BUY,110,y,110,30,BTN_BUY,fontSize,clrWhite,clrLightBlue, clrWhite);
    createBtn(BTN_SELL,220,y,110,30,BTN_SELL,fontSize,clrWhite,clrRed, clrWhite);

    y += yH;
    createBtn(BTN_CLOSE,110,y,220,30,BTN_CLOSE,fontSize,clrWhite,clrIndigo, clrWhite);
    trade.SetExpertMagicNumber(MagicNumber);

    return (INIT_SUCCEEDED);
}

void OnTick(){
    ControlSpeed();

    double Ask = NormalizeDouble(SymbolInfoDouble(_Symbol, SYMBOL_ASK), _Digits);
    double Bid = NormalizeDouble(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits);

    if(GetState(BTN_ST)){
        ObjectSetInteger(0, BTN_ST,OBJPROP_STATE, false);
        tradeInAction = true;
        Print("暂停");
        ChartRedraw(0);
    }

    if(GetState(BTN_RN)){
        ObjectSetInteger(0, BTN_RN,OBJPROP_STATE, false);
        tradeInAction = false;
        Print("开始");
        ChartRedraw(0);
    }
    if(GetState(BTN_P)){
        double newLot = (double)GetValue(BTN_LOT);
        double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        newLot += lotStep;
        newLot = NormalizeDouble(newLot, 2);
        newLot = newLot > 0.11 ? lotStep:newLot;
        ObjectSetString(0, BTN_LOT, OBJPROP_TEXT, (string)newLot);
        ObjectSetInteger(0, BTN_P,OBJPROP_STATE, false);
        ChartRedraw(0);
    }
    if(GetState(BTN_M)){
        double newLot = (double)GetValue(BTN_LOT);
        double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        newLot -= lotStep;
        newLot = NormalizeDouble(newLot, 2);
        newLot = newLot <= 0.01 ? lotStep:newLot;
        ObjectSetString(0, BTN_LOT, OBJPROP_TEXT, (string)newLot);

    }

    if(GetState(BTN_CLOSE)){
        CloseAllPositions();
        ObjectSetInteger(0, BTN_CLOSE,OBJPROP_STATE, false);
        tradeInAction = false;
        ChartRedraw(0);
    }

    if(GetState(BTN_BUY)){
        trade.Buy((double)GetValue(BTN_LOT), _Symbol, Ask, 0, 0, "手动买入");
        ObjectSetInteger(0, BTN_BUY,OBJPROP_STATE, false);
        tradeInAction = false;
        ChartRedraw(0);

    }
    if(GetState(BTN_SELL)){
        trade.Sell((double)GetValue(BTN_LOT), _Symbol, Bid, 0, 0, "手动卖出");
        ObjectSetInteger(0, BTN_SELL,OBJPROP_STATE, false);
        tradeInAction = false;
        ChartRedraw(0);
    }
}

void OnDeinit(const int reason){
    deleteBtn();
}


void createBtn(string objName, int xD, int yD, int xS,int yS,string txt, int fs=13, color clrTxt=clrWhite, color clrBg=clrBlack,color clrBd=clrBlack){
    ObjectCreate(0, objName, OBJ_BUTTON, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, xD);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, yD);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, xS);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, yS);
    ObjectSetString(0, objName, OBJPROP_TEXT, txt);
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fs);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clrTxt);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, clrBg);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_COLOR, clrBd);
    ObjectSetString(0, objName, OBJPROP_FONT,"黑体");
}

void deleteBtn(){
    ObjectDelete(0, BTN_ST);
    ObjectDelete(0, BTN_RN);
    ObjectDelete(0, BTN_P);
    ObjectDelete(0, BTN_LOT);
    ObjectDelete(0, BTN_M);
    ObjectDelete(0, BTN_BUY);
    ObjectDelete(0, BTN_SELL);
    ObjectDelete(0, BTN_CLOSE);
}

int GetState(string name){
    return (int)ObjectGetInteger(0, name, OBJPROP_STATE);
}

string GetValue(string name){
    return ObjectGetString(0, name, OBJPROP_TEXT);
}

void ControlSpeed(){
    if(!tradeInAction) return;

    for(int i=0;i<speed;i++){
        Comment("暂停："+(string)i);
    }
}

void CloseAllPositions(){
    for(int i = PositionsTotal()-1;i>=0;i--){
        ulong ticket = PositionGetTicket(i);
        if(ticket>0){
            if(PositionSelectByTicket(ticket)){
                if(PositionGetString(POSITION_SYMBOL)==_Symbol && PositionGetInteger(POSITION_MAGIC)==MagicNumber){
                    trade.PositionClose(ticket);
                }
            }
        }
    }
}